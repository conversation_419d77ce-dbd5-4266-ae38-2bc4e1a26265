<template>
  <el-form
    ref="elFormRef"
    v-bind="getFormBindValue()"
    :model="props.isCustom ? props.model : formModel"
    :class="prefixCls"
    v-loading="props.vLoading"
  >
    <template v-if="props.isCustom">
      <slot />
    </template>
    <template v-else>
      <el-row v-if="getProps.isCol" :gutter="20">
        <template v-for="item in visibleSchema" :key="item.field">
          <div v-if="item.component === 'Divider'" :class="'w-full'">
            <component
              :is="componentMap['Divider']"
              v-bind="{ contentPosition: 'left', ...item.componentProps }"
            >
              {{ item?.label }}
            </component>
          </div>
          <el-col v-else v-bind="setGridProp(item.colProps)">
            <FormItem
              :item="item"
              :form-model="formModel"
              :auto-set-placeholder="getProps.autoSetPlaceholder"
            >
              <template
                v-for="(_, slotName) in $slots"
                :key="slotName"
                #[slotName]="slotProps"
              >
                <slot :name="slotName" v-bind="slotProps" />
              </template>
            </FormItem>
          </el-col>
        </template>
      </el-row>
      <template v-else>
        <template v-for="item in visibleSchema" :key="item.field">
          <div v-if="item.component === 'Divider'" :class="'w-full'">
            <component
              :is="componentMap['Divider']"
              v-bind="{ contentPosition: 'left', ...item.componentProps }"
            >
              {{ item?.label }}
            </component>
          </div>
          <FormItem
            v-else
            :item="item"
            :form-model="formModel"
            :auto-set-placeholder="getProps.autoSetPlaceholder"
          >
            <template
              v-for="(_, slotName) in $slots"
              :key="slotName"
              #[slotName]="slotProps"
            >
              <slot :name="slotName" v-bind="slotProps" />
            </template>
          </FormItem>
        </template>
      </template>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, unref, watch } from 'vue';
import { ElCol, ElForm, ElRow } from 'element-plus';
import { componentMap } from './componentMap';
import { initModel, setGridProp } from './helper';
import { useDesign } from '@/hooks/web/useDesign';
import { findIndex } from '@/utils';
import { set } from 'lodash-es';
import { FormProps } from './types';
import { FormSchema, FormSetPropsType } from '@/types/form';
// @ts-ignore
import FormItem from './FormItem.vue';

const { prefixCls } = useDesign('form');

interface Props {
  // 生成Form的布局结构数组
  schema?: FormSchema[];
  // 是否需要栅格布局
  isCol?: boolean;
  // 表单数据对象
  model?: Recordable;
  // 是否自动设置placeholder
  autoSetPlaceholder?: boolean;
  // 是否自定义内容
  isCustom?: boolean;
  // 表单label宽度
  labelWidth?: string | number;
  // 是否 loading 数据中
  vLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  schema: () => [],
  isCol: false,
  model: () => ({}),
  autoSetPlaceholder: true,
  isCustom: false,
  labelWidth: 'auto',
  vLoading: false,
});

const emit = defineEmits<{
  register: [formRef: any, elFormRef: any];
}>();

// element form 实例
const elFormRef = ref<any>();

// useForm传入的props
const outsideProps = ref<FormProps>({});
const mergeProps = ref<FormProps>({});

const getProps = computed(() => {
  const propsObj = { ...props };
  Object.assign(propsObj, unref(mergeProps));
  return propsObj;
});

// 表单数据
const formModel = ref<Recordable>({});

// 过滤掉隐藏的schema
const visibleSchema = computed(() => {
  return getProps.value.schema?.filter((v) => !v.hidden) || [];
});

onMounted(() => {
  emit('register', (unref(elFormRef) as any)?.$parent, unref(elFormRef));
});

// 对表单赋值
const setValues = (data: Recordable = {}) => {
  formModel.value = Object.assign(unref(formModel), data);
};

const setProps = (props: FormProps = {}) => {
  mergeProps.value = Object.assign(unref(mergeProps), props);
  outsideProps.value = props;
};

const delSchema = (field: string) => {
  const { schema } = unref(getProps);
  const index = findIndex(schema, (v: FormSchema) => v.field === field);
  if (index > -1) {
    schema.splice(index, 1);
  }
};

const addSchema = (formSchema: FormSchema, index?: number) => {
  const { schema } = unref(getProps);
  if (index !== void 0) {
    schema.splice(index, 0, formSchema);
    return;
  }
  schema.push(formSchema);
};

const setSchema = (schemaProps: FormSetPropsType[]) => {
  const { schema } = unref(getProps);
  for (const v of schema) {
    for (const item of schemaProps) {
      if (v.field === item.field) {
        set(v, item.path, item.value);
      }
    }
  }
};

const getElFormRef = () => {
  return unref(elFormRef);
};

// 过滤传入Form组件的属性
const getFormBindValue = () => {
  // 避免在标签上出现多余的属性
  const delKeys = [
    'schema',
    'isCol',
    'autoSetPlaceholder',
    'isCustom',
    'model',
  ];
  const formProps = { ...unref(getProps) };
  for (const key in formProps) {
    if (delKeys.indexOf(key) !== -1) {
      delete formProps[key];
    }
  }
  return formProps;
};

// 监听表单结构化数组，重新生成formModel
watch(
  () => unref(getProps).schema,
  (schema = []) => {
    formModel.value = initModel(schema, unref(formModel));
  },
  {
    immediate: true,
    deep: true,
  },
);

defineExpose({
  setValues,
  formModel,
  setProps,
  delSchema,
  addSchema,
  setSchema,
  getElFormRef,
});
</script>

<style lang="scss" scoped>
.#{$elNamespace}-form.#{$sass-namespace}-form .#{$elNamespace}-row {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
</style>
