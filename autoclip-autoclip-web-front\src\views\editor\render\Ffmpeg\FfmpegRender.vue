<template>
  <div>
    <!-- Ren<PERSON> -->
    <button
      @click="render"
      :class="`inline-flex transform items-center rounded-lg bg-white p-3 font-bold text-gray-900 transition-all hover:bg-[#ccc] disabled:opacity-50`"
      :disabled="
        !loadFfmpeg ||
        isRendering ||
        (mediaFiles.length === 0 && textElements.length === 0)
      "
    >
      <span v-if="!loadFfmpeg || isRendering" class="mr-2 animate-spin">
        <svg
          viewBox="0 0 1024 1024"
          focusable="false"
          data-icon="loading"
          width="1em"
          height="1em"
        >
          <path
            d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
          ></path>
        </svg>
      </span>
      <p>
        {{
          loadFfmpeg
            ? isRendering
              ? 'Rendering...'
              : 'Render'
            : 'Loading FFmpeg...'
        }}
      </p>
    </button>

    <!-- Render Modal -->
    <div
      v-if="showModal"
      class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50"
    >
      <div
        class="w-full max-w-xl rounded-xl border border-gray-200 bg-white p-6 shadow-lg"
      >
        <!-- Title and close button -->
        <div class="mb-4 flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ isRendering ? 'Rendering...' : projectName }}
          </h2>
          <button
            @click="handleCloseModal"
            class="text-4xl font-bold text-gray-500 hover:text-red-500"
            aria-label="Close"
          >
            &times;
          </button>
        </div>

        <div v-if="isRendering">
          <div
            class="h-40 rounded border border-gray-200 bg-gray-50 p-2 font-mono text-sm"
          >
            <div class="text-gray-900">{{ logMessages }}</div>
            <p class="text-xs italic text-gray-600">
              The progress bar is experimental in FFmpeg WASM, so it might
              appear slow or unresponsive even though the actual processing is
              not.
            </p>
            <FfmpegProgressBar :ffmpeg="ffmpeg" />
          </div>
        </div>
        <div v-else>
          <video
            v-if="previewUrl"
            :src="previewUrl"
            controls
            class="mb-4 w-full rounded border border-gray-200"
          />
          <div class="flex justify-between">
            <a
              :href="previewUrl || '#'"
              :download="`${projectName}.mp4`"
              class="inline-flex transform items-center rounded-lg bg-blue-600 p-3 font-bold text-white transition-all hover:bg-blue-700"
            >
              <img
                alt="Download"
                class="opacity-90"
                height="18"
                src="https://www.svgrepo.com/show/501347/save.svg"
                width="18"
              />
              <span class="ml-2">Save Video</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { useProjectStore } from '@/store/editor';
import { extractConfigs } from '@/utils/extractConfigs';
import { mimeToExt } from '@/types/editor';
import { ElMessage } from 'element-plus';
import FfmpegProgressBar from './ProgressBar.vue';

const props = defineProps<{
  loadFunction: () => Promise<void>;
  loadFfmpeg: boolean;
  ffmpeg: FFmpeg;
  logMessages: string;
}>();

const projectStore = useProjectStore();
const mediaFiles = computed(() => projectStore.getMediaFiles);
const projectName = computed(() => projectStore.getProjectName);
const exportSettings = computed(() => projectStore.getExportSettings);
const duration = computed(() => projectStore.getDuration);
const textElements = computed(() => projectStore.getTextElements);
const totalDuration = computed(() => duration.value);

const videoRef = ref<HTMLVideoElement | null>(null);
const loaded = ref(false);
const showModal = ref(false);
const previewUrl = ref<string | null>(null);
const isRendering = ref(false);

watch([loaded, previewUrl], () => {
  if (loaded.value && videoRef.value && previewUrl.value) {
    videoRef.value.src = previewUrl.value;
  }
});

const handleCloseModal = async () => {
  showModal.value = false;
  isRendering.value = false;
  try {
    props.ffmpeg.terminate();
    await props.loadFunction();
  } catch (e) {
    console.error('Failed to reset FFmpeg:', e);
  }
};

const render = async () => {
  if (mediaFiles.value.length === 0 && textElements.value.length === 0) {
    console.log('No media files to render');
    return;
  }
  showModal.value = true;
  isRendering.value = true;

  const renderFunction = async () => {
    const params = extractConfigs(exportSettings.value);

    try {
      const filters: string[] = [];
      const overlays: Array<{
        label: string;
        x: number;
        y: number;
        start: string;
        end: string;
      }> = [];
      const inputs: string[] = [];
      const audioDelays: string[] = [];

      // Create base black background
      filters.push(
        `color=c=black:size=1920x1080:d=${totalDuration.value.toFixed(3)}[base]`,
      );
      // Sort videos by zIndex ascending (lowest drawn first)
      const sortedMediaFiles = [...mediaFiles.value].sort(
        (a, b) => (a.zIndex || 0) - (b.zIndex || 0),
      );

      for (let i = 0; i < sortedMediaFiles.length; i++) {
        // timing
        const { startTime, positionStart, positionEnd } = sortedMediaFiles[i];
        const duration = positionEnd - positionStart;

        // get the file data and write to ffmpeg
        const fileData = await projectStore.getFile(sortedMediaFiles[i].fileId);
        const buffer = await fileData?.arrayBuffer();
        const ext =
          mimeToExt[fileData?.type as keyof typeof mimeToExt] ||
          fileData?.type?.split('/')[1];
        await props.ffmpeg.writeFile(
          `input${i}.${ext}`,
          new Uint8Array(buffer as ArrayBuffer),
        );

        if (sortedMediaFiles[i].type === 'image') {
          inputs.push(
            '-loop',
            '1',
            '-t',
            duration.toFixed(3),
            '-i',
            `input${i}.${ext}`,
          );
        } else {
          inputs.push('-i', `input${i}.${ext}`);
        }

        const visualLabel = `visual${i}`;
        const audioLabel = `audio${i}`;

        // Shift clip to correct place on timeline (video)
        if (sortedMediaFiles[i].type === 'video') {
          filters.push(
            `[${i}:v]trim=start=${startTime.toFixed(3)}:duration=${duration.toFixed(3)},scale=${sortedMediaFiles[i].width}:${sortedMediaFiles[i].height},setpts=PTS-STARTPTS+${positionStart.toFixed(3)}/TB[${visualLabel}]`,
          );
        }
        if (sortedMediaFiles[i].type === 'image') {
          filters.push(
            `[${i}:v]scale=${sortedMediaFiles[i].width}:${sortedMediaFiles[i].height},setpts=PTS+${positionStart.toFixed(3)}/TB[${visualLabel}]`,
          );
        }

        // Apply opacity
        if (
          sortedMediaFiles[i].type === 'video' ||
          sortedMediaFiles[i].type === 'image'
        ) {
          const alpha = Math.min(
            Math.max((sortedMediaFiles[i].opacity || 100) / 100, 0),
            1,
          );
          filters.push(
            `[${visualLabel}]format=yuva420p,colorchannelmixer=aa=${alpha}[${visualLabel}]`,
          );
        }

        // Store overlay range that matches shifted time
        if (
          sortedMediaFiles[i].type === 'video' ||
          sortedMediaFiles[i].type === 'image'
        ) {
          overlays.push({
            label: visualLabel,
            x: sortedMediaFiles[i].x || 0,
            y: sortedMediaFiles[i].y || 0,
            start: positionStart.toFixed(3),
            end: positionEnd.toFixed(3),
          });
        }

        // Audio: trim, then delay (in ms)
        if (
          sortedMediaFiles[i].type === 'audio' ||
          sortedMediaFiles[i].type === 'video'
        ) {
          const delayMs = Math.round(positionStart * 1000);
          const volume =
            sortedMediaFiles[i].volume !== undefined
              ? sortedMediaFiles[i].volume / 100
              : 1;
          filters.push(
            `[${i}:a]atrim=start=${startTime.toFixed(3)}:duration=${duration.toFixed(3)},asetpts=PTS-STARTPTS,adelay=${delayMs}|${delayMs},volume=${volume}[${audioLabel}]`,
          );
          audioDelays.push(`[${audioLabel}]`);
        }
      }

      // Apply overlays in z-index order
      let lastLabel = 'base';
      if (overlays.length > 0) {
        for (let i = 0; i < overlays.length; i++) {
          const { label, start, end, x, y } = overlays[i];
          const nextLabel = i === overlays.length - 1 ? 'outv' : `tmp${i}`;
          filters.push(
            `[${lastLabel}][${label}]overlay=${x}:${y}:enable='between(t\\,${start}\\,${end})'[${nextLabel}]`,
          );
          lastLabel = nextLabel;
        }
      }

      // Apply text
      if (textElements.value.length > 0) {
        // load fonts
        let fonts = ['Arial', 'Inter', 'Lato'];
        for (let i = 0; i < fonts.length; i++) {
          const font = fonts[i];
          const res = await fetch(`/fonts/${font}.ttf`);
          const fontBuf = await res.arrayBuffer();
          await props.ffmpeg.writeFile(
            `font${font}.ttf`,
            new Uint8Array(fontBuf),
          );
        }
        // Apply text
        for (let i = 0; i < textElements.value.length; i++) {
          const text = textElements.value[i];
          const label =
            i === textElements.value.length - 1 ? 'outv' : `text${i}`;
          const escapedText = text.text
            .replace(/:/g, '\\:')
            .replace(/'/g, "\\\\'");
          const alpha = Math.min(Math.max((text.opacity ?? 100) / 100, 0), 1);
          const color = text.color?.includes('@')
            ? text.color
            : `${text.color || 'white'}@${alpha}`;
          filters.push(
            `[${lastLabel}]drawtext=fontfile=font${text.font}.ttf:text='${escapedText}':x=${text.x}:y=${text.y}:fontsize=${text.fontSize || 24}:fontcolor=${color}:enable='between(t\\,${text.positionStart}\\,${text.positionEnd})'[${label}]`,
          );
          lastLabel = label;
        }
      }

      // Mix all audio tracks
      if (audioDelays.length > 0) {
        const audioMix = audioDelays.join('');
        filters.push(
          `${audioMix}amix=inputs=${audioDelays.length}:normalize=0[outa]`,
        );
      }

      // Final filter_complex
      const complexFilter = filters.join('; ');
      const ffmpegArgs = [
        ...inputs,
        '-filter_complex',
        complexFilter,
        '-map',
        '[outv]',
      ];

      if (audioDelays.length > 0) {
        ffmpegArgs.push('-map', '[outa]');
      }

      ffmpegArgs.push(
        '-c:v',
        'libx264',
        '-c:a',
        'aac',
        '-preset',
        params.preset,
        '-crf',
        params.crf.toString(),
        '-t',
        totalDuration.value.toFixed(3),
        'output.mp4',
      );

      await props.ffmpeg.exec(ffmpegArgs);
    } catch (err) {
      console.error('FFmpeg processing error:', err);
    }

    // return the output url
    const outputData = await props.ffmpeg.readFile('output.mp4');
    const outputBlob = new Blob([outputData as Uint8Array], {
      type: 'video/mp4',
    });
    const outputUrl = URL.createObjectURL(outputBlob);
    return outputUrl;
  };

  // Run the function and handle the result/error
  try {
    const outputUrl = await renderFunction();
    previewUrl.value = outputUrl;
    loaded.value = true;
    isRendering.value = false;
    ElMessage.success('Video rendered successfully');
  } catch (err) {
    ElMessage.error('Failed to render video');
    console.error('Failed to render video:', err);
  }
};
</script>

<style scoped>
.el-dialog {
  background-color: black;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

:deep(.el-dialog__header) {
  margin-right: 0;
  padding: 0;
}

:deep(.el-dialog__body) {
  padding: 1.5rem;
}
</style>
