// Inspira Checkbox 组件类型定义

export interface InspiraCheckboxProps {
  // 绑定值
  modelValue?: boolean | string | number | (string | number)[];
  
  // 选项的值
  value?: string | number | boolean;
  
  // 标签文本
  label?: string;
  
  // 是否禁用
  disabled?: boolean;
  
  // 设置 indeterminate 状态，只负责样式控制
  indeterminate?: boolean;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 原生 name 属性
  name?: string;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'scale' | 'bounce' | 'pulse' | 'flip' | 'slide' | 'none';
}

export interface InspiraCheckboxEmits {
  'update:modelValue': [value: boolean | string | number | (string | number)[]];
  change: [value: boolean | string | number | (string | number)[]];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}

export interface InspiraCheckboxSlots {
  default: () => any;
}

// CheckboxGroup 组件类型定义
export interface InspiraCheckboxGroupProps {
  // 绑定值
  modelValue: (string | number)[];
  
  // 是否禁用
  disabled?: boolean;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 可被勾选的 checkbox 的最小数量
  min?: number;
  
  // 可被勾选的 checkbox 的最大数量
  max?: number;
  
  // 文本颜色
  textColor?: string;
  
  // 填充颜色
  fill?: string;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'scale' | 'bounce' | 'pulse' | 'flip' | 'slide' | 'none';
  
  // 排列方向
  direction?: 'horizontal' | 'vertical';
  
  // 间距
  gap?: string | number;
}

export interface InspiraCheckboxGroupEmits {
  'update:modelValue': [value: (string | number)[]];
  change: [value: (string | number)[]];
}

export interface InspiraCheckboxGroupSlots {
  default: () => any;
}

// CheckboxButton 组件类型定义
export interface InspiraCheckboxButtonProps {
  // 绑定值
  modelValue?: boolean | string | number | (string | number)[];
  
  // 选项的值
  value?: string | number | boolean;
  
  // 标签文本
  label?: string;
  
  // 是否禁用
  disabled?: boolean;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 原生 name 属性
  name?: string;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'slide' | 'fade' | 'scale' | 'none';
}

export interface InspiraCheckboxButtonEmits {
  'update:modelValue': [value: boolean | string | number | (string | number)[]];
  change: [value: boolean | string | number | (string | number)[]];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}

export interface InspiraCheckboxButtonSlots {
  default: () => any;
}