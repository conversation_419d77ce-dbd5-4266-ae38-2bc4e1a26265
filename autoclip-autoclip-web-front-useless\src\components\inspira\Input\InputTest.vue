<!-- 
  输入框组件测试页面
  用于验证 InspiraInput 组件的各种功能
-->
<template>
  <div class="input-test-container">
    <h2 class="test-title">Inspira Input 组件测试</h2>
    
    <!-- 基础输入框测试 -->
    <section class="test-section">
      <h3>基础输入框</h3>
      <div class="input-group">
        <InspiraInput v-model="basicValue" placeholder="基础输入框" />
        <InspiraInput v-model="basicValue" placeholder="禁用状态" :disabled="true" />
        <InspiraInput v-model="basicValue" placeholder="只读状态" :readonly="true" />
      </div>
      <p class="value-display">当前值: {{ basicValue }}</p>
    </section>

    <!-- 尺寸测试 -->
    <section class="test-section">
      <h3>输入框尺寸</h3>
      <div class="input-group">
        <InspiraInput v-model="sizeValue" placeholder="小尺寸" size="small" />
        <InspiraInput v-model="sizeValue" placeholder="默认尺寸" size="default" />
        <InspiraInput v-model="sizeValue" placeholder="大尺寸" size="large" />
      </div>
      <p class="value-display">当前值: {{ sizeValue }}</p>
    </section>

    <!-- 输入类型测试 -->
    <section class="test-section">
      <h3>输入类型</h3>
      <div class="input-group">
        <InspiraInput v-model="textValue" type="text" placeholder="文本输入" />
        <InspiraInput v-model="passwordValue" type="password" placeholder="密码输入" :show-password="true" />
        <InspiraInput v-model="emailValue" type="email" placeholder="邮箱输入" />
        <InspiraInput v-model="numberValue" type="number" placeholder="数字输入" />
      </div>
      <div class="values-display">
        <p>文本: {{ textValue }}</p>
        <p>密码: {{ passwordValue }}</p>
        <p>邮箱: {{ emailValue }}</p>
        <p>数字: {{ numberValue }}</p>
      </div>
    </section>

    <!-- 图标和插槽测试 -->
    <section class="test-section">
      <h3>图标和插槽</h3>
      <div class="input-group">
        <InspiraInput 
          v-model="iconValue" 
          placeholder="前缀图标" 
          prefix-icon="i-mdi:account"
        />
        <InspiraInput 
          v-model="iconValue" 
          placeholder="后缀图标" 
          suffix-icon="i-mdi:magnify"
        />
        <InspiraInput v-model="iconValue" placeholder="可清空" :clearable="true" />
      </div>
      <div class="input-group">
        <InspiraInput v-model="slotValue" placeholder="自定义插槽">
          <template #prefix>
            <Icon icon="i-mdi:account" class="text-blue-500" />
          </template>
          <template #suffix>
            <span class="text-sm text-gray-500">@example.com</span>
          </template>
        </InspiraInput>
      </div>
      <p class="value-display">图标值: {{ iconValue }}, 插槽值: {{ slotValue }}</p>
    </section>

    <!-- 动画效果测试 -->
    <section class="test-section">
      <h3>动画效果</h3>
      <div class="input-group">
        <InspiraInput v-model="animationValue" placeholder="焦点动画" animation="focus" />
        <InspiraInput v-model="animationValue" placeholder="模糊动画" animation="blur" />
        <InspiraInput v-model="animationValue" placeholder="输入动画" animation="typing" />
        <InspiraInput v-model="animationValue" placeholder="无动画" animation="none" />
      </div>
      <p class="value-display">动画值: {{ animationValue }}</p>
    </section>

    <!-- 验证状态测试 -->
    <section class="test-section">
      <h3>验证状态</h3>
      <div class="input-group">
        <InspiraInput 
          v-model="validateValue" 
          placeholder="成功状态" 
          validate-status="success"
          validate-message="验证成功！"
        />
        <InspiraInput 
          v-model="validateValue" 
          placeholder="警告状态" 
          validate-status="warning"
          validate-message="请注意格式"
        />
        <InspiraInput 
          v-model="validateValue" 
          placeholder="错误状态" 
          validate-status="error"
          validate-message="输入格式错误"
        />
        <InspiraInput 
          v-model="validateValue" 
          placeholder="验证中状态" 
          validate-status="validating"
          validate-message="正在验证..."
        />
      </div>
      <p class="value-display">验证值: {{ validateValue }}</p>
    </section>

    <!-- 前置后置内容测试 -->
    <section class="test-section">
      <h3>前置后置内容</h3>
      <div class="input-group">
        <InspiraInput v-model="prependValue" placeholder="输入网址">
          <template #prepend>https://</template>
        </InspiraInput>
        <InspiraInput v-model="appendValue" placeholder="输入邮箱">
          <template #append>@example.com</template>
        </InspiraInput>
        <InspiraInput v-model="bothValue" placeholder="完整URL">
          <template #prepend>https://</template>
          <template #append>.com</template>
        </InspiraInput>
      </div>
      <div class="values-display">
        <p>前置: {{ prependValue }}</p>
        <p>后置: {{ appendValue }}</p>
        <p>两端: {{ bothValue }}</p>
      </div>
    </section>

    <!-- 与 Element Plus 对比 -->
    <section class="test-section">
      <h3>与 Element Plus 对比</h3>
      <div class="comparison-group">
        <div class="comparison-item">
          <h4>Element Plus</h4>
          <div class="input-group">
            <el-input v-model="comparisonValue" placeholder="Element Plus 输入框" />
            <el-input v-model="comparisonValue" placeholder="密码输入" type="password" show-password />
            <el-input v-model="comparisonValue" placeholder="可清空" clearable />
          </div>
        </div>
        <div class="comparison-item">
          <h4>Inspira UI</h4>
          <div class="input-group">
            <InspiraInput v-model="comparisonValue" placeholder="Inspira UI 输入框" />
            <InspiraInput v-model="comparisonValue" placeholder="密码输入" type="password" :show-password="true" />
            <InspiraInput v-model="comparisonValue" placeholder="可清空" :clearable="true" />
          </div>
        </div>
      </div>
      <p class="value-display">对比值: {{ comparisonValue }}</p>
    </section>

    <!-- 事件测试 -->
    <section class="test-section">
      <h3>事件测试</h3>
      <div class="input-group">
        <InspiraInput 
          v-model="eventValue"
          placeholder="事件测试输入框"
          @input="handleInput"
          @change="handleChange"
          @focus="handleFocus"
          @blur="handleBlur"
          @clear="handleClear"
          @keydown="handleKeydown"
        />
      </div>
      <div class="event-log">
        <h4>事件日志：</h4>
        <ul>
          <li v-for="(event, index) in eventLog" :key="index">
            {{ event }}
          </li>
        </ul>
        <button @click="clearEventLog" class="clear-log">清空日志</button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Icon } from '@/components/Icon';
import InspiraInput from './InspiraInput.vue';

// 测试数据
const basicValue = ref('');
const sizeValue = ref('');
const textValue = ref('');
const passwordValue = ref('');
const emailValue = ref('');
const numberValue = ref('');
const iconValue = ref('');
const slotValue = ref('');
const animationValue = ref('');
const validateValue = ref('');
const prependValue = ref('');
const appendValue = ref('');
const bothValue = ref('');
const comparisonValue = ref('');
const eventValue = ref('');

// 事件日志
const eventLog = ref<string[]>([]);

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] ${message}`);
  if (eventLog.value.length > 15) {
    eventLog.value = eventLog.value.slice(0, 15);
  }
};

// 事件处理
const handleInput = (value: string | number, event: Event) => {
  addLog(`输入事件: ${value}`);
};

const handleChange = (value: string | number, event: Event) => {
  addLog(`变化事件: ${value}`);
};

const handleFocus = (event: FocusEvent) => {
  addLog('获得焦点');
};

const handleBlur = (event: FocusEvent) => {
  addLog('失去焦点');
};

const handleClear = () => {
  addLog('清空按钮被点击');
};

const handleKeydown = (event: KeyboardEvent) => {
  addLog(`按键按下: ${event.key}`);
};

const clearEventLog = () => {
  eventLog.value = [];
};
</script>

<style scoped>
.input-test-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--inspira-text);
}

.test-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  border: 1px solid var(--inspira-border);
  border-radius: var(--inspira-radius);
  background: var(--inspira-surface);
}

.test-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--inspira-text);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.value-display,
.values-display p {
  font-size: 0.875rem;
  color: var(--inspira-text-secondary);
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: var(--inspira-background);
  border-radius: 4px;
  border: 1px solid var(--inspira-border);
}

.values-display {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.comparison-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.comparison-item h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--inspira-text-secondary);
}

.event-log {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--inspira-background);
  border-radius: var(--inspira-radius);
  border: 1px solid var(--inspira-border);
}

.event-log h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--inspira-text);
}

.event-log ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.event-log li {
  padding: 0.25rem 0;
  font-size: 0.75rem;
  color: var(--inspira-text-secondary);
  border-bottom: 1px solid var(--inspira-border);
}

.event-log li:last-child {
  border-bottom: none;
}

.clear-log {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
}

.clear-log:hover {
  background: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-test-container {
    padding: 1rem;
  }
  
  .comparison-group {
    grid-template-columns: 1fr;
  }
  
  .values-display {
    grid-template-columns: 1fr;
  }
}
</style>