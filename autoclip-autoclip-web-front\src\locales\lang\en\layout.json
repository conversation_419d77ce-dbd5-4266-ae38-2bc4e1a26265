{"footer": {"onlinePreview": "Preview", "onlineDocument": "Document"}, "header": {"dropdownChangeApi": "Change Api", "dropdownItemDoc": "Document", "dropdownItemLoginOut": "Log Out", "tooltipErrorLog": "Error log", "tooltipLock": "Lock screen", "tooltipNotify": "Notification", "tooltipEntryFull": "Full Screen", "tooltipExitFull": "Exit Full Screen", "lockScreenPassword": "Lock screen password", "lockScreen": "Lock screen", "lockScreenBtn": "Locking", "home": "Home", "upgrade-prompt": {"title": "New version released", "content": "Vben Admin v5.0.0 preview version has been released", "ok-text": "Go to new version"}}, "multipleTab": {"reload": "Refresh current", "close": "Close current", "closeLeft": "Close Left", "closeRight": "Close Right", "closeOther": "Close Other", "closeAll": "Close All"}, "setting": {"contentModeFull": "Full", "contentModeFixed": "Fixed width", "topMenuAlignLeft": "Left", "topMenuAlignRight": "Center", "topMenuAlignCenter": "Right", "menuTriggerNone": "Not Show", "menuTriggerBottom": "Bottom", "menuTriggerTop": "Top", "menuTypeSidebar": "Left menu mode", "menuTypeMixSidebar": "Left menu mixed mode", "menuTypeMix": "Top Menu Mix mode", "menuTypeTopMenu": "Top menu mode", "on": "On", "off": "Off", "minute": "Minute", "operatingTitle": "Successful!", "operatingContent": "The copy is successful, please go to src/settings/projectSetting.ts to modify the configuration!", "resetSuccess": "Successfully reset!", "copyBtn": "Copy", "clearBtn": "Clear cache and to the login page", "drawerTitle": "Configuration", "darkMode": "Dark mode", "navMode": "Navigation mode", "interfaceFunction": "Interface function", "interfaceDisplay": "Interface display", "animation": "Animation", "splitMenu": "Split menu", "closeMixSidebarOnChange": "Switch page to close menu", "sysTheme": "System theme", "headerTheme": "Header theme", "sidebarTheme": "Menu theme", "menuDrag": "Drag Sidebar", "menuSearch": "Menu search", "menuAccordion": "Sidebar accordion", "menuCollapse": "Collapse menu", "collapseMenuDisplayName": "Collapse menu display name", "topMenuLayout": "Top menu layout", "menuCollapseButton": "<PERSON>u collapse button", "contentMode": "Content area width", "expandedMenuWidth": "Expanded menu width", "breadcrumb": "Breadcrumbs", "breadcrumbIcon": "Breadcrumbs Icon", "tabs": "Tabs", "tabDetail": "<PERSON><PERSON>", "tabsQuickBtn": "Tabs quick button", "tabsRedoBtn": "Tabs redo button", "tabsFoldBtn": "Tabs fold button", "sidebar": "Sidebar", "header": "Header", "footer": "Footer", "fullContent": "Full content", "grayMode": "Gray mode", "colorWeak": "Color Weak Mode", "progress": "Progress", "switchLoading": "Switch Loading", "switchAnimation": "Switch animation", "animationType": "Animation type", "autoScreenLock": "Auto screen lock", "notAutoScreenLock": "Not auto lock", "fixedHeader": "Fixed header", "fixedSideBar": "Fixed Sidebar", "mixSidebarTrigger": "Mixed menu Trigger", "triggerHover": "Hover", "triggerClick": "Click", "mixSidebarFixed": "Fixed expanded menu", "autoCollapseTabsInFold": "Auto collapse tabs in fold"}}