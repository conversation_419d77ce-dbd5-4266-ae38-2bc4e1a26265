## 📚 完整组件索引

### 🌈 Backgrounds

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **Aurora Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/aurora-background) | 提供可定制的、平滑移动的极光效果背景。 |
| **Black Hole Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/black-hole-background) | 模拟黑洞视觉效果的动态背景。 |
| **Bubbles Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/bubbles-background) | 创建带有可配置模糊效果的动态气泡背景。 |
| **Cosmic Portal** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/cosmic-portal) | 创造一个通往宇宙的传送门视觉特效背景。 |
| **Falling Stars Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/falling-stars-background) | 模拟流星划过夜空的动态背景效果。 |
| **Flickering Grid** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/flickering-grid) | 可定制大小、颜色和闪烁频率的网格背景。 |
| **Interactive Grid Pattern** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/interactive-grid-pattern) | 可与用户鼠标交互的网格图案背景。 |
| **Lamp Effect** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/lamp-effect) | 模拟聚光灯从顶部照射下来，照亮内容的背景效果。 |
| **Liquid Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/liquid-background) | 实现动态流体效果的背景。 |
| **Particle Whirlpool** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/particle-whirlpool) | 创建由大量粒子组成的漩涡动画背景。 |
| **Particles Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/particles-background) | 生成可与鼠标交互的动态粒子背景。 |
| **Pattern Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/pattern-background) | 提供可动画的网格或点状图案背景。 |
| **Ripple** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/ripple) | 创建由中心向外扩散的水波纹动画背景。 |
| **Snowfall Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/snowfall-background) | 模拟雪花飘落的动态背景效果。 |
| **Sparkles** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/sparkles) | 创建闪亮粒子背景，可自定义粒子颜色、大小和密度。 |
| **Tetris** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/tetris) | 模拟俄罗斯方块下落的趣味背景。 |
| **Video Text** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/video-text) | 将视频作为文字遮罩，实现视频纹理文字效果。 |
| **Vortex Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/vortex-background) | 创建多彩的、可定制的粒子漩涡背景。 |
| **Warp Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/warp-background) | 模拟超空间跳跃或光速飞行的网格动画背景。 |
| **Wavy Background** | [查看文档](https://inspira-ui.com/docs/components/backgrounds/wavy-background) | 通过 Simplex Noise 算法生成平滑的波浪动画背景。 |

### 🔘 Buttons

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **Gradient Button** | [查看文档](https://inspira-ui.com/docs/components/buttons/gradient-button) | 具有动态渐变边框和模糊效果的按钮。 |
| **Interactive Hover Button** | [查看文档](https://inspira-ui.com/docs/components/buttons/interactive-hover-button) | 提供多种交互式悬停效果的按钮。 |
| **Rainbow Button** | [查看文档](https://inspira-ui.com/docs/components/buttons/rainbow-button) | 带有彩虹色动画边框的按钮。 |
| **Ripple Button** | [查看文档](https://inspira-ui.com/docs/components/buttons/ripple-button) | 点击时产生水波纹扩散效果的按钮。 |
| **Shimmer Button** | [查看文档](https://inspira-ui.com/docs/components/buttons/shimmer-button) | 带有微光扫过效果的按钮。 |

### 🎴 Cards

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **3D Card Effect** | [查看文档](https://inspira-ui.com/docs/components/cards/3d-card-effect) | 根据鼠标位置创建交互式 3D 视角卡片。 |
| **Apple Card Carousel** | [查看文档](https://inspira-ui.com/docs/components/cards/apple-card-carousel) | 一个类似 Apple 官网风格的卡片轮播组件，支持展开和 FLIP 动画。 |
| **Card Spotlight** | [查看文档](https://inspira-ui.com/docs/components/cards/card-spotlight) | 在鼠标悬停时产生聚光灯效果的卡片。 |
| **Direction Aware Hover** | [查看文档](https://inspira-ui.com/docs/components/cards/direction-aware-hover) | 根据鼠标进入的方向显示不同的悬停效果。 |
| **Flip Card** | [查看文档](https://inspira-ui.com/docs/components/cards/flip-card) | 提供正面和背面内容的 3D 翻转卡片。 |
| **Glare Card** | [查看文档](https://inspira-ui.com/docs/components/cards/glare-card) | 在鼠标悬停时产生炫光效果的卡片。 |

### 🖱️ Cursors

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **Fluid Cursor** | [查看文档](https://inspira-ui.com/docs/components/cursors/fluid-cursor) | 模拟平滑的流体效果，跟随鼠标移动。 |
| **Tailed Cursor** | [查看文档](https://inspira-ui.com/docs/components/cursors/tailed-cursor) | 带有拖尾效果的鼠标指针。 |

### 📱 Device Mocks

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **iPhone Mockup** | [查看文档](https://inspira-ui.com/docs/components/device-mocks/iphone-mockup) | 用于展示内容的 iPhone 设备模型。 |
| **Safari Mockup** | [查看文档](https://inspira-ui.com/docs/components/device-mocks/safari-mockup) | 用于展示网页或图片的 Safari 浏览器模型。 |

### 🎯 Input And Forms

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **File Upload** | [查看文档](https://inspira-ui.com/docs/components/input-and-forms/file-upload) | 提供文件拖拽上传界面，并带有 3D 视角效果。 |
| **Input** | [查看文档](https://inspira-ui.com/docs/components/input-and-forms/input) | 基础的输入框组件，可自定义样式。 |
| **Placeholders And Vanish Input** | [查看文档](https://inspira-ui.com/docs/components/input-and-forms/placeholders-and-vanish-input) | 带有动态循环占位符和输入时消失效果的输入框。 |

### 🔧 Miscellaneous

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **Animate Grid** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/animate-grid) | 展示卡片网格，鼠标悬停时卡片会产生发光和 3D 倾斜效果。 |
| **Animated Circular Progress Bar** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/animated-circular-progress-bar) | 带有平滑动画的圆形进度条。 |
| **Animated List** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/animated-list) | 列表项逐个出现的动画列表。 |
| **Animated Testimonials** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/animated-testimonials) | 动态展示用户评价或推荐语的组件。 |
| **Animated Tooltip** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/animated-tooltip) | 鼠标悬停在一组条目上时显示带有动画效果的工具提示。 |
| **Balance Slider** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/balance-slider) | 一个可拖动的平衡滑块，用于比较或调整数值。 |
| **Bento Grid** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/bento-grid) | 一种灵活的网格布局，适用于展示多种类型的内容。 |
| **Book** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/book) | 模拟一本可悬停翻开的 3D 书籍效果。 |
| **Compare** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/compare) | 通过可拖动的滑块来对比两个图像或内容。 |
| **Container Scroll** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/container-scroll) | 当用户滚动页面时，容器内的内容会产生 3D 滚动动画效果。 |
| **Dock** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/dock) | 一个类似 macOS Dock 的组件，鼠标悬停时图标会放大。 |
| **Expandable Gallery** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/expandable-gallery) | 一个可展开的图片画廊，鼠标悬停时图片会放大。 |
| **Images Slider** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/images-slider) | 用于展示多张图片的轮播滑块。 |
| **Lens** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/lens) | 一个放大镜效果组件，可用于放大图片或内容。 |
| **Link Preview** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/link-preview) | 鼠标悬停在链接上时，显示网站预览图。 |
| **Marquee** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/marquee) | 创建水平或垂直滚动的跑马灯效果。 |
| **Morphing Tabs** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/morphing-tabs) | 带有平滑变形动画效果的选项卡。 |
| **Multi Step Loader** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/multi-step-loader) | 显示多步骤加载过程的加载器。 |
| **Photo Gallery** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/photo-gallery) | 一个响应式的图片画廊，支持网格布局。 |
| **Scroll Island** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/scroll-island) | 一个浮动的滚动进度指示器，类似于手机上的“灵动岛”。 |
| **Shader Toy Viewer** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/shader-toy-viewer) | 用于在网页中展示和渲染 ShaderToy 着色器的查看器。 |
| **Smooth Cursor** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/smooth-cursor) | 通过弹簧动画实现平滑的自定义鼠标指针。 |
| **SVG Mask** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/svg-mask) | 通过 SVG 遮罩在鼠标悬停时揭示内容。 |
| **Testimonial Slider** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/testimonial-slider) | 用于展示用户评价的自动轮播滑块。 |
| **Timeline** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/timeline) | 用于按时间顺序展示事件的时间轴。 |
| **Tracing Beam** | [查看文档](https://inspira-ui.com/docs/components/miscellaneous/tracing-beam) | 在滚动时，一条光束会跟随视口中的内容，高亮显示当前激活的元素。 |

### 🎭 Special Effects

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **Animated Beam** | [查看文档](https://inspira-ui.com/docs/components/special-effects/animated-beam) | 在两个 DOM 元素之间绘制一条可定制的动画光束。 |
| **Border Beam** | [查看文档](https://inspira-ui.com/docs/components/special-effects/border-beam) | 在容器边框上创建一条流动的光束动画。 |
| **Confetti** | [查看文档](https://inspira-ui.com/docs/components/special-effects/confetti) | 触发五彩纸屑爆炸效果，可自定义粒子数量、颜色和形状。 |
| **Glow Border** | [查看文档](https://inspira-ui.com/docs/components/special-effects/glow-border) | 为元素添加一个可定制颜色和动画效果的发光边框。 |
| **Glowing Effect** | [查看文档](https://inspira-ui.com/docs/components/special-effects/glowing-effect) | 在鼠标靠近元素时产生动态的发光效果。 |
| **Meteor** | [查看文档](https://inspira-ui.com/docs/components/special-effects/meteor) | 创建流星划过的动画效果。 |
| **Neon Border** | [查看文档](https://inspira-ui.com/docs/components/special-effects/neon-border) | 为元素添加可动画的双色霓虹边框。 |
| **Particle Image** | [查看文档](https://inspira-ui.com/docs/components/special-effects/particle-image) | 将图片转换为可交互的粒子动画。 |
| **Scratch To Reveal** | [查看文档](https://inspira-ui.com/docs/components/special-effects/scratch-to-reveal) | 模拟刮刮卡效果，刮开涂层以显示下方内容。 |
| **Spring Calendar** | [查看文档](https://inspira-ui.com/docs/components/special-effects/spring-calendar) | 一个带有弹簧动画效果的交互式日历。 |

### ✨ Text Animations

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **3D Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/3d-text) | 创建带有描边和阴影的 3D 效果文字。 |
| **Blur Reveal** | [查看文档](https://inspira-ui.com/docs/components/text-animations/blur-reveal) | 通过模糊和位移动画逐个揭示子元素。 |
| **Box Reveal** | [查看文档](https://inspira-ui.com/docs/components/text-animations/box-reveal) | 通过一个滑动的色块来揭示文本内容。 |
| **Colourful Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/colourful-text) | 为文本中的每个字符应用多彩的动画效果。 |
| **Container Text Flip** | [查看文档](https://inspira-ui.com/docs/components/text-animations/container-text-flip) | 当文本在容器中滚动时，产生逐字翻转的动画效果。 |
| **Flip Words** | [查看文档](https://inspira-ui.com/docs/components/text-animations/flip-words) | 在一系列单词之间进行 3D 翻转切换动画。 |
| **Focus** | [查看文档](https://inspira-ui.com/docs/components/text-animations/focus) | 通过模糊其他部分来突出显示文本的特定区域。 |
| **Hyper Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/hyper-text) | 创建带有渐变和动画效果的超链接文本。 |
| **Letter Pullup** | [查看文档](https://inspira-ui.com/docs/components/text-animations/letter-pullup) | 文本中的字母逐个向上拉起的动画效果。 |
| **Line Shadow Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/line-shadow-text) | 为文本添加线性阴影，创造独特的视觉风格。 |
| **Morphing Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/morphing-text) | 在一系列文本之间进行平滑的变形过渡动画。 |
| **Number Ticker** | [查看文档](https://inspira-ui.com/docs/components/text-animations/number-ticker) | 一个可以平滑滚动到目标数字的计数器。 |
| **Radiant Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/radiant-text) | 为文本添加一个循环移动的放射状光辉效果。 |
| **Sparkles Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/sparkles-text) | 在文本上随机出现闪烁的星星动画。 |
| **Spinning Text** | [查看文档](https://inspira-ui.com/docs/components/text-animations/spinning-text) | 使文本沿着圆形路径旋转。 |
| **Text Generate Effect** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-generate-effect) | 模拟文本逐字生成的效果。 |
| **Text Glitch** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-glitch) | 为文本添加故障或信号干扰的动画效果。 |
| **Text Highlight** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-highlight) | 在文本后面创建一个动画高亮效果。 |
| **Text Hover Effect** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-hover-effect) | 鼠标悬停时，文本会从实心填充变为描边效果。 |
| **Text Reveal** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-reveal) | 滚动时文本逐词或逐字揭示。 |
| **Text Reveal Card** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-reveal-card) | 鼠标悬停时，通过动画揭示隐藏的文本内容。 |
| **Text Scroll Reveal** | [查看文档](https://inspira-ui.com/docs/components/text-animations/text-scroll-reveal) | 当文本滚动到视口时，逐词高亮显示。 |

### 📊 Visualization

| 组件名 | 在线文档 | 描述 |
|--------|----------|------|
| **Bending Gallery** | [查看文档](https://inspira-ui.com/docs/components/visualization/bending-gallery) | 一个弯曲布局的画廊，滚动时产生独特的视觉效果。 |
| **3D Carousel** | [查看文档](https://inspira-ui.com/docs/components/visualization/3d-carousel) | 一个具有 3D 旋转效果的轮播组件。 |
| **File Tree** | [查看文档](https://inspira-ui.com/docs/components/visualization/file-tree) | 以树状结构展示文件和文件夹，支持展开和选择。 |
| **Github Globe** | [查看文档](https://inspira-ui.com/docs/components/visualization/github-globe) | 一个可交互的 3D 地球，用于可视化地理数据和连接。 |
| **Globe** | [查看文档](https://inspira-ui.com/docs/components/visualization/globe) | 一个轻量、平滑、可交互的 3D 地球仪。 |
| **Icon Cloud** | [查看文档](https://inspira-ui.com/docs/components/visualization/icon-cloud) | 将一组图标或图片以 3D 球形云的方式动态展示。 |
| **Light Speed** | [查看文档](https://inspira-ui.com/docs/components/visualization/light-speed) | 模拟光速穿梭的视觉效果。 |
| **Liquid Logo** | [查看文档](https://inspira-ui.com/docs/components/visualization/liquid-logo) | 为 Logo 图片添加动态的液体晃动效果。 |
| **Animated Logo Cloud** | [查看文档](https://inspira-ui.com/docs/components/visualization/animated-logo-cloud) | 动态展示合作伙伴或客户的 Logo 墙。 |
| **Logo Origami** | [查看文档](https://inspira-ui.com/docs/components/visualization/logo-origami) | 模拟 Logo 折纸展开的动画效果。 |
| **Orbit** | [查看文档](https://inspira-ui.com/docs/components/visualization/orbit) | 让元素围绕一个中心点进行轨道运行。 |
| **Spline** | [查看文档](https://inspira-ui.com/docs/components/visualization/spline) | 在网页中嵌入和交互 Spline 3D 场景。 |
| **World Map** | [查看文档](https://inspira-ui.com/docs/components/visualization/world-map) | 一个由点阵组成的、可交互的世界地图。 |