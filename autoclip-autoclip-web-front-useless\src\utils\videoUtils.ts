export interface VideoInfo {
  url: string;
  name: string;
  type: string;
  size?: number;
  duration?: number;
  width?: number;
  height?: number;
  currentTime?: number;
  readyState?: number;
  networkState?: number;
  error?: {
    code: number;
    message: string;
  } | null;
  urlType?: 'local' | 'remote';
}

/**
 * 从视频元素获取元数据信息
 * @param videoElement 视频元素
 * @param url 视频URL
 * @returns VideoInfo 对象
 */
export const getVideoMetadata = async (
  videoElement: HTMLVideoElement,
  url: string,
): Promise<VideoInfo> => {
  // 等待视频元数据加载完成
  if (videoElement.readyState < 1) {
    await new Promise((resolve) => {
      videoElement.onloadedmetadata = resolve;
    });
  }

  // 估算文件大小：使用视频时长和比特率
  let size: number | undefined;
  if (videoElement.duration) {
    // 假设平均比特率为 2Mbps (250KB/s)
    const averageBitrate = 250 * 1024; // 250KB/s
    size = Math.round(videoElement.duration * averageBitrate);
  }

  return {
    url,
    name: url.split('/').pop() || 'video.mp4',
    type: 'video/mp4',
    size,
    duration: videoElement.duration,
    width: videoElement.videoWidth,
    height: videoElement.videoHeight,
    currentTime: videoElement.currentTime,
    readyState: videoElement.readyState,
    networkState: videoElement.networkState,
    error: videoElement.error
      ? {
          code: videoElement.error.code,
          message: videoElement.error.message,
        }
      : null,
  };
};

/**
 * 从视频URL获取元数据信息
 * @param url 视频URL
 * @returns Promise<VideoInfo>
 */
export const getVideoMetadataFromUrl = async (
  url: string,
): Promise<VideoInfo> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = async () => {
      const metadata = await getVideoMetadata(video, url);
      video.remove(); // 清理DOM
      resolve(metadata);
    };

    video.onerror = async () => {
      const metadata = await getVideoMetadata(video, url);
      video.remove(); // 清理DOM
      reject(metadata);
    };

    video.src = url;
  });
};

/**
 * 从File对象获取元数据信息
 * @param file File对象
 * @returns Promise<VideoInfo>
 */
export const getVideoMetadataFromFile = (file: File): Promise<VideoInfo> => {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file);
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = async () => {
      const metadata = await getVideoMetadata(video, url);
      metadata.size = file.size; // 直接从File对象获取大小
      URL.revokeObjectURL(url); // 清理URL
      video.remove(); // 清理DOM
      resolve(metadata);
    };

    video.onerror = async () => {
      const metadata = await getVideoMetadata(video, url);
      metadata.size = file.size; // 直接从File对象获取大小
      URL.revokeObjectURL(url); // 清理URL
      video.remove(); // 清理DOM
      reject(metadata);
    };

    video.src = url;
  });
};
