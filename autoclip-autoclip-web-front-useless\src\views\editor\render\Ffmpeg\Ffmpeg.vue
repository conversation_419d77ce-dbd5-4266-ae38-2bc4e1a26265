<template>
  <div class="flex flex-col items-center justify-center py-2">
    <RenderOptions />
    <FfmpegRender
      :load-function="loadFFmpegFunction"
      :load-ffmpeg="loadFfmpeg"
      :log-messages="logMessages"
      :ffmpeg="ffmpegRef as any"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL } from '@ffmpeg/util';
import FfmpegRender from './FfmpegRender.vue';
import RenderOptions from './RenderOptions.vue';

const loadFfmpeg = ref(false);
const ffmpegRef = ref<FFmpeg>(new FFmpeg());
const logMessages = ref('');

const loadFFmpegFunction = async () => {
  loadFfmpeg.value = false;
  const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd';

  const ffmpeg = new FFmpeg();
  ffmpegRef.value = ffmpeg;

  ffmpeg.on('log', ({ message }) => {
    logMessages.value = message;
  });

  await ffmpeg.load({
    coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
    wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
    // TODO: For Multi Threading as mentioned in the ffmpeg docs but it is not fetched for some reason
    // workerURL: await toBlobURL(`${baseURL}/ffmpeg-core.worker.js`, 'text/javascript'),
  });

  loadFfmpeg.value = true;
};

onMounted(() => {
  loadFFmpegFunction();
});
</script>
