import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

export const user: AppRouteModule = {
  path: '/user',
  name: 'User',
  component: LAYOUT,
  redirect: '/user/profile',
  meta: { title: '用户', hideMenu: true },
  children: [
    {
      path: '/user/profile',
      name: 'UserProfile',
      component: () => import('@/views/profile/index.vue'),
      redirect: '/user/profile/admin',
      meta: { title: '用户配置', hideMenu: true },
      children: [
        {
          path: '/user/profile/admin',
          component: () => import('@/views/profile/admin/index.vue'),
          name: 'UserProfileAdmin',
          meta: {
            title: '用户通用配置',
          },
        },
        {
          path: '/user/profile/security',
          component: () => import('@/views/profile/security/index.vue'),
          name: 'UserProfileSecurity',
          meta: {
            title: '用户安全配置',
          },
        },
      ],
    },
  ],
};
export default user;
