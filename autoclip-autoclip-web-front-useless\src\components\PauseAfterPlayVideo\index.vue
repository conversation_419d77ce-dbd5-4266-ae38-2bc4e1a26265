<script setup lang="ts">
import VideoPlayer from '@/components/VideoPlayer/index.vue';
import { VideoSource } from '@/components/VideoPlayer/types';

interface Props {
  url?: string;
  sources?: VideoSource[];
  useVideoPlayer?: boolean;
  autoplay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
  preload?: string;
  responsive?: boolean;
  fluid?: boolean;
  playbackRates?: number[];
  language?: string;
  poster?: string;
}

const props = withDefaults(defineProps<Props>(), {
  useVideoPlayer: false,
  autoplay: false,
  controls: true,
  muted: true,
  loop: false,
  preload: 'metadata',
  responsive: true,
  fluid: true,
  playbackRates: () => [0.5, 1, 1.25, 1.5, 2],
  language: 'zh-CN',
});

const videoRef = ref<InstanceType<typeof VideoPlayer> | HTMLVideoElement>();

const isPaused = ref(false);
watch(
  () => props.url,
  () => {
    if (videoRef.value) {
      isPaused.value = false;
      if (props.useVideoPlayer) {
        (videoRef.value as InstanceType<typeof VideoPlayer>).instance?.play();
      } else {
        (videoRef.value as HTMLVideoElement).play();
      }
    }
  },
  {
    flush: 'post',
    immediate: true,
    deep: true,
  },
);

onMounted(() => {
  if (videoRef.value) {
    if (!props.useVideoPlayer) {
      (videoRef.value as HTMLVideoElement).addEventListener('playing', () => {
        handlePlaying();
      });
    }
  }
  if (props.url) {
    isPaused.value = false;
    if (props.useVideoPlayer) {
      (videoRef.value as InstanceType<typeof VideoPlayer>).instance?.play();
    } else {
      (videoRef.value as HTMLVideoElement).play();
    }
  }
});

const handlePlaying = () => {
  if (videoRef.value) {
    if (!isPaused.value) {
      isPaused.value = true;
      if (props.useVideoPlayer) {
        (videoRef.value as InstanceType<typeof VideoPlayer>).instance?.pause();
      } else {
        (videoRef.value as HTMLVideoElement).pause();
      }
    }
  }
};
</script>

<template>
  <video-player
    v-if="useVideoPlayer"
    ref="videoRef"
    :sources="sources || [{ src: url, type: 'video/mp4' }]"
    :src="url"
    :autoplay="autoplay"
    :controls="controls"
    :muted="muted"
    :loop="loop"
    :preload="preload"
    :responsive="responsive"
    :fluid="fluid"
    :playbackRates="playbackRates"
    :language="language"
    :poster="poster"
    @playing="handlePlaying"
  ></video-player>
  <video v-else ref="videoRef" :src="url"></video>
</template>

<style scoped lang="scss"></style>
