// Inspira Radio 组件类型定义

export interface InspiraRadioProps {
  // 绑定值
  modelValue?: string | number | boolean;
  
  // 选项的值
  value: string | number | boolean;
  
  // 标签文本
  label?: string;
  
  // 是否禁用
  disabled?: boolean;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 原生 name 属性
  name?: string;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'scale' | 'bounce' | 'pulse' | 'ripple' | 'none';
}

export interface InspiraRadioEmits {
  'update:modelValue': [value: string | number | boolean];
  change: [value: string | number | boolean];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}

export interface InspiraRadioSlots {
  default: () => any;
}

// RadioGroup 组件类型定义
export interface InspiraRadioGroupProps {
  // 绑定值
  modelValue: string | number | boolean;
  
  // 是否禁用
  disabled?: boolean;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 文本颜色
  textColor?: string;
  
  // 填充颜色
  fill?: string;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'scale' | 'bounce' | 'pulse' | 'ripple' | 'none';
  
  // 排列方向
  direction?: 'horizontal' | 'vertical';
  
  // 间距
  gap?: string | number;
}

export interface InspiraRadioGroupEmits {
  'update:modelValue': [value: string | number | boolean];
  change: [value: string | number | boolean];
}

export interface InspiraRadioGroupSlots {
  default: () => any;
}

// RadioButton 组件类型定义
export interface InspiraRadioButtonProps {
  // 绑定值
  modelValue?: string | number | boolean;
  
  // 选项的值
  value: string | number | boolean;
  
  // 标签文本
  label?: string;
  
  // 是否禁用
  disabled?: boolean;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 原生 name 属性
  name?: string;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'slide' | 'fade' | 'scale' | 'none';
}

export interface InspiraRadioButtonEmits {
  'update:modelValue': [value: string | number | boolean];
  change: [value: string | number | boolean];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}

export interface InspiraRadioButtonSlots {
  default: () => any;
}