<!-- 
  Inspira UI 错误边界组件
  提供组件错误捕获和降级功能
-->
<template>
  <div class="inspira-error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误时渲染降级组件 -->
    <component
      v-else
      :is="fallbackComponent"
      v-bind="fallbackProps"
      v-on="fallbackListeners"
    >
      <slot />
    </component>
    
    <!-- 错误信息显示（开发环境） -->
    <div v-if="hasError && showError && isDev" class="inspira-error-info">
      <p class="inspira-error-title">组件错误 - 已降级到: {{ fallbackComponent }}</p>
      <details class="inspira-error-details">
        <summary>错误详情</summary>
        <pre>{{ lastError?.message }}</pre>
        <pre>{{ lastError?.stack }}</pre>
      </details>
      <button @click="resetError" class="inspira-error-retry">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, computed, getCurrentInstance } from 'vue';
import type { ErrorBoundaryProps, ErrorBoundaryEmits } from './types';

const props = withDefaults(defineProps<ErrorBoundaryProps>(), {
  fallbackProps: () => ({}),
  showError: true,
  maxRetries: 3,
  retryDelay: 1000,
});

const emit = defineEmits<ErrorBoundaryEmits>();

// 错误状态
const hasError = ref(false);
const lastError = ref<Error | null>(null);
const retryCount = ref(0);
const isDev = import.meta.env.DEV;

// 计算降级组件的事件监听器
const fallbackListeners = computed(() => {
  const instance = getCurrentInstance();
  const listeners: Record<string, any> = {};
  
  // 转发所有事件到父组件
  if (instance?.vnode.props) {
    Object.keys(instance.vnode.props).forEach(key => {
      if (key.startsWith('on')) {
        const eventName = key.slice(2).toLowerCase();
        listeners[eventName] = (...args: any[]) => {
          emit(eventName as any, ...args);
        };
      }
    });
  }
  
  return listeners;
});

// 错误捕获处理
onErrorCaptured((error: Error, instance, info) => {
  hasError.value = true;
  lastError.value = error;
  
  if (props.showError) {
    console.warn(`Inspira UI Component Error (falling back to ${props.fallbackComponent}):`, {
      error: error.message,
      component: instance?.type?.name || 'Unknown',
      info,
      retryCount: retryCount.value,
    });
  }
  
  // 调用自定义错误处理函数
  if (props.onError) {
    props.onError(error, instance, info);
  }
  
  // 发出错误事件
  emit('error', error);
  emit('fallback', props.fallbackComponent);
  
  // 调用自定义降级处理函数
  if (props.onFallback) {
    props.onFallback(props.fallbackComponent, error);
  }
  
  // 阻止错误继续传播
  return false;
});

// 重置错误状态
const resetError = async () => {
  if (retryCount.value >= props.maxRetries) {
    console.warn(`Max retries (${props.maxRetries}) reached for component`);
    return;
  }
  
  retryCount.value++;
  emit('retry', retryCount.value);
  
  // 延迟重试
  if (props.retryDelay > 0) {
    await new Promise(resolve => setTimeout(resolve, props.retryDelay));
  }
  
  hasError.value = false;
  lastError.value = null;
  
  emit('reset');
};

// 暴露方法给父组件
defineExpose({
  resetError,
  hasError: computed(() => hasError.value),
  lastError: computed(() => lastError.value),
  retryCount: computed(() => retryCount.value),
});
</script>

<style scoped>
.inspira-error-boundary {
  /* 确保错误边界不影响布局 */
  display: contents;
}

.inspira-error-info {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  max-width: 400px;
  z-index: 9999;
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.inspira-error-title {
  color: #dc2626;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.inspira-error-details {
  margin: 8px 0;
}

.inspira-error-details summary {
  cursor: pointer;
  color: #7c2d12;
  font-weight: bold;
}

.inspira-error-details pre {
  background: #fef7f7;
  padding: 8px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
  margin: 4px 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.inspira-error-retry {
  background: #dc2626;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.inspira-error-retry:hover {
  background: #b91c1c;
}
</style>