<template>
  <div>
    <layout-header />
    <div>
      <layout-sidebar class="fixed z-10 h-full w-full bg-white" />
      <layout-content class="mx-auto min-h-[calc(100vh-5rem)]" />
    </div>
    <layout-footer v-if="route.meta.showFooter || getIsMobile" />
    <Login :visible="getShowLoginModal" @close="setShowLoginModal(false)" />
  </div>
</template>

<script lang="ts" setup>
import LayoutContent from '@/layouts/default/content/index.vue';
import LayoutHeader from '@/layouts/default/header/index.vue';
import LayoutSidebar from '@/layouts/default/sidebar/index.vue';
import LayoutFooter from '@/layouts/default/footer/index.vue';
import { useRoute } from 'vue-router';
import { useAppInject } from '@/hooks/web/useAppInject';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import Login from '@/components/Login/src/Login.vue';
const { getShowLoginModal, setRootSetting } = useRootSetting();

const setShowLoginModal = (flag: boolean) => {
  setRootSetting({
    showLoginModal: flag,
  });
};

const route = useRoute();
const { getIsMobile } = useAppInject();
</script>
<style lang="scss" scoped>
@use '@/design/var/index.scss' as *;
@use '@/design/var/color' as *;

$prefix-cls: #{$sass-namespace}-default-layout;

.#{prefix-cls} {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  background-color: $content-bg;

  > .ant-layout {
    min-height: 100%;
  }

  &-main {
    width: 100%;
    margin-left: 1px;
  }
}

.#{prefix-cls}-out {
  &.ant-layout-has-sider {
    .#{prefix-cls} {
      &-main {
        margin-left: 1px;
      }
    }
  }
}
</style>
