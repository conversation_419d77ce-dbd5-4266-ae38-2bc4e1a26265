import { MediaFile, TextElement } from '@/types/editor';
import { create } from 'zustand';

interface ProjectState {
  currentTime: number;
  isPlaying: boolean;
  duration: number;
  isMuted: boolean;
  mediaFiles: MediaFile[];
  textElements: TextElement[];
}

interface ProjectActions {
  setMediaFiles: (files: MediaFile[]) => void;
  setTextElements: (elements: TextElement[]) => void;
  setCurrentTime: (time: number) => void;
  setIsPlaying: (playing: boolean) => void;
  setIsMuted: (muted: boolean) => void;
  setDuration: (duration: number) => void;
}

export const useProjectStore = create<ProjectState & ProjectActions>((set) => ({
  // State
  mediaFiles: [],
  textElements: [],
  isMuted: false,
  currentTime: 0,
  isPlaying: false,
  duration: 0,

  // Actions
  setMediaFiles: (files) => set({ mediaFiles: files }),
  setTextElements: (elements) => set({ textElements: elements }),
  setCurrentTime: (time) => set({ currentTime: time }),
  setIsPlaying: (playing) => set({ isPlaying: playing }),
  setIsMuted: (muted) => set({ isMuted: muted }),
  setDuration: (duration) => set({ duration: duration }),
}));
