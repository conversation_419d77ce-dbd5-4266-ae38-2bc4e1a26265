import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const { t } = useI18n();

export const assets: AppRouteModule = {
  path: '/assets',
  name: 'assets',
  component: LAYOUT,
  redirect: '/assets/index',
  meta: {
    title: t('视频库'),
    icon: 'login',
    orderNo: 3,
    hideChildrenInMenu: true,
    hideFooter: true,
  },
  children: [
    {
      path: '/assets/index',
      name: 'assetsIndex',
      component: () => import('@/views/assets/index.vue'),
      meta: {
        hidden: true,
        title: '首页',
        noTagsView: true,
      },
    },
  ],
};

export default assets;
