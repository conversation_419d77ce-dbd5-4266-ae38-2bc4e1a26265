<script setup lang="ts">
import Menu from '@/layouts/default/menu/src/Menu.vue';
import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';
import { useAppInject } from '@/hooks/web/useAppInject';

defineOptions({ name: 'LayoutSidebar' });
const { getMenuHidden, setMenuSetting } = useMenuSetting();

const { getIsMobile } = useAppInject();
const { getBasicTransition } = useTransitionSetting();

const handleSelect = (_: string) => {
  setMenuSetting({ hidden: true });
};
</script>

<template>
  <transition :name="getBasicTransition" mode="out-in" appear>
    <Menu
      v-if="!getMenuHidden && getIsMobile"
      :elMenuConfig="{ mode: 'vertical' }"
      @select="handleSelect"
    ></Menu>
  </transition>
</template>
<style scoped lang="scss"></style>
