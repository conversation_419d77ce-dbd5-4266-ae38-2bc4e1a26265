<script setup lang="ts">
import { useMessage } from '@/hooks/web/useMessage';
import { getFile, useProjectStore } from '@/store/editor';
import type { MediaFile } from '@/types/editor';
import { categorizeFile } from '@/utils/utils';
import { getVideoMetadataFromFile, VideoInfo } from '@/utils/videoUtils';
import { storeToRefs } from 'pinia';
import { buildUUID } from '@/utils/uuid';

interface Props {
  fileId: string;
}

const props = defineProps<Props>();

const projectStore = useProjectStore();
const { $message } = useMessage();
const { mediaFiles } = storeToRefs(projectStore);

const handleFileChange = async () => {
  try {
    const updatedMedia = toRaw(mediaFiles.value);

    const file: File | null = await getFile(props.fileId);
    const mediaId = buildUUID();
    let videoInfo: VideoInfo | null = null;
    if (file) {
      videoInfo = await getVideoMetadataFromFile(file);
    } else {
      $message.error('File not found or invalid file ID.');
      return;
    }

    if (props.fileId && file) {
      const relevantClips = updatedMedia.filter(
        (clip) => clip.type === categorizeFile(file.type),
      );
      const lastEnd =
        relevantClips.length > 0
          ? Math.max(...relevantClips.map((f) => f.positionEnd))
          : 0;

      const newMediaFile: MediaFile = {
        id: mediaId,
        fileName: file.name,
        fileId: props.fileId,
        startTime: 0,
        endTime: 30,
        src: URL.createObjectURL(file),
        positionStart: lastEnd,
        positionEnd: lastEnd + (videoInfo.duration || 0),
        includeInMerge: true,
        x: 0,
        y: 0,
        width: 1920,
        height: 1080,
        rotation: 0,
        opacity: 100,
        crop: { x: 0, y: 0, width: 1920, height: 1080 },
        playbackSpeed: 1,
        volume: 100,
        type: categorizeFile(file.type),
        zIndex: 0,
      };

      projectStore.addMediaFile(newMediaFile);
      $message.success('Media added successfully.');
    } else {
      $message.error('File not found or invalid file ID.');
    }
  } catch (error) {
    console.error('Error adding media:', error);
    $message.error('Failed to add media. Please try again.');
  }
};
</script>

<template>
  <div>
    <button
      class="flex cursor-pointer flex-col items-center justify-center rounded-lg border border-gray-300 bg-white px-2 py-2 font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md"
      @click="handleFileChange"
    >
      <el-image
        alt="Add Media"
        class="h-4 w-4"
        :src="'https://www.svgrepo.com/show/513803/add.svg'"
      />
    </button>
  </div>
</template>
