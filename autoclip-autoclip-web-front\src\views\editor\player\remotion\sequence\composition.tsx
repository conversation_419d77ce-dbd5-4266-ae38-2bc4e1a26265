import React from 'react';
import { useProjectStore } from '@/store/editor/react/projectStore';
import { MediaFile, TextElement } from '@/types/editor';
import { useEffect, useRef } from 'react';
import { useCurrentFrame } from 'remotion';
import { SequenceItem } from './sequence-item';

const Composition = () => {
  const { mediaFiles, textElements, setCurrentTime } = useProjectStore();

  const frame = useCurrentFrame();
  const fps = 30;

  const THRESHOLD = 0.1;
  const previousTime = useRef(0);

  useEffect(() => {
    const currentTimeInSeconds = frame / fps;
    if (Math.abs(currentTimeInSeconds - previousTime.current) > THRESHOLD) {
      if (currentTimeInSeconds !== undefined) {
        setCurrentTime(currentTimeInSeconds);
        previousTime.current = currentTimeInSeconds;
      }
    }
  }, [frame, setCurrentTime]);

  return (
    <>
      {mediaFiles.map((item: MediaFile) => {
        if (!item) return null;
        return SequenceItem[item.type](item, { fps });
      })}
      {textElements.map((item: TextElement) => {
        if (!item) return null;
        return SequenceItem['text'](item, { fps });
      })}
    </>
  );
};

export default Composition;
