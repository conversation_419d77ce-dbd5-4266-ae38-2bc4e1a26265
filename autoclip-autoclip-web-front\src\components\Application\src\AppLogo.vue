<!--
 * @Author: Vben
 * @Description: logo component
-->
<template>
  <div class="anticon cursor-pointer" :class="getAppLogoClass" @click="goHome">
    <div class="flex items-center lg:flex-1">
      <img
        class="h-12 w-auto"
        src="/src/assets/logo_no_font.png"
        alt="sorawebui.com"
      />
      <img class="h-7 w-auto" src="/src/assets/font.png" alt="sorawebui.com" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, unref } from 'vue';
import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
import { useDesign } from '@/hooks/web/useDesign';
import { PageEnum } from '@/enums/pageEnum';

const props = defineProps({
  /**
   * The theme of the current parent component
   */
  theme: {
    type: String,
    validator: (v: string) => ['light', 'dark'].includes(v),
  },
  /**
   * Whether to show title
   */
  showTitle: { type: Boolean, default: true },
  /**
   * The title is also displayed when the menu is collapsed
   */
  alwaysShowTitle: { type: Boolean },
});

const { prefixCls } = useDesign('app-logo');
const { getCollapsedShowTitle } = useMenuSetting();
const { push } = useRouter();

const getAppLogoClass = computed(() => [
  prefixCls,
  props.theme,
  { 'collapsed-show-title': unref(getCollapsedShowTitle) },
]);

function goHome() {
  push(PageEnum.BASE_HOME);
}
</script>
<style lang="scss" scoped>
@use '@/design/var/index.scss' as *;
@use '@/design/var/color' as *;
$prefix-cls: #{$sass-namespace}-app-logo;

.#{prefix-cls} {
  display: flex;
  align-items: center;
  padding-left: 7px;
  transition: all 0.2s ease;
  cursor: pointer;

  &.light {
    border-bottom: 1px solid $border-color-base;
  }

  &.collapsed-show-title {
    padding-left: 20px;
  }

  &.light &__title {
    color: $primary-color;
  }

  &.dark &__title {
    color: $white;
  }

  &__title {
    transition: all 0.5s;
    font-size: 16px;
    font-weight: 700;
    line-height: normal;
  }
}
</style>
