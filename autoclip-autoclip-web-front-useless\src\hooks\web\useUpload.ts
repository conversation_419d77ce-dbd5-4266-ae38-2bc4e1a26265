import * as FileApi from '@/api/infra/file';
import axios, { type AxiosRequestConfig } from 'axios';
import { UploadRequestOptions } from 'element-plus';
import { defHttp } from '@/utils/http/axios';
import type { UploadFileParams } from '#/axios';

/**
 * 获得上传 URL
 */
export const getUploadUrl = (): string => {
  return (
    import.meta.env.VITE_BASE_URL +
    import.meta.env.VITE_API_URL +
    '/infra/file/upload'
  );
};

export const useUpload = () => {
  // 后端上传地址
  const uploadUrl = getUploadUrl();
  // 是否使用前端直连上传
  const isClientUpload = true;
  // 重写ElUpload上传方法
  const httpRequest = async (options: UploadRequestOptions) => {
    // 模式一：前端上传
    if (isClientUpload) {
      // 1.1 生成文件名称
      const fileName = options.file.name;
      // 1.2 获取文件预签名地址
      const presignedInfo = await FileApi.getFilePresignedUrl({
        name: fileName,
        directory: '',
      });
      return axios
        .put(presignedInfo.uploadUrl, options.file, {
          headers: {
            'Content-Type': options.file.type,
          },
        })
        .then(() => {
          // 1.4. 记录文件信息到后端（异步）
          createFile(presignedInfo, fileName, options.file);
          // 通知成功，数据格式保持与后端上传的返回结果一致
          return { data: presignedInfo.url };
        });
    } else {
      // 模式二：后端上传
      // 重写 el-upload httpRequest 文件上传成功会走成功的钩子，失败走失败的钩子
      return new Promise((resolve, reject) => {
        FileApi.updateFile({ file: options.file })
          .then((res) => {
            if (res.code === 0) {
              resolve(res);
            } else {
              reject(res);
            }
          })
          .catch((res) => {
            reject(res);
          });
      });
    }
  };

  const axiosUpload = async (
    config: AxiosRequestConfig,
    uploadParams: UploadFileParams,
  ) => {
    const fileName = uploadParams.file.name;
    // 1.2 获取文件预签名地址
    const presignedInfo = await FileApi.getFilePresignedUrl({
      name: fileName,
      directory: '',
    });
    defHttp.uploadFile<Blob>(config, uploadParams).then(() => {
      // 1.4. 记录文件信息到后端（异步）
      createFile(presignedInfo, fileName, uploadParams.file);
      // 通知成功，数据格式保持与后端上传的返回结果一致
      return { data: presignedInfo.url };
    });
  };

  return {
    axiosUpload,
    uploadUrl,
    httpRequest,
  };
};

/**
 * 创建文件信息
 * @param vo 文件预签名信息
 * @param name 文件名称
 * @param file 文件
 */
function createFile(
  vo: FileApi.FilePresignedUrlRespVO,
  name: string,
  file: File,
) {
  const fileVo = {
    configId: vo.configId,
    url: vo.url,
    path: name,
    name: file.name,
    type: file.type,
    size: file.size,
  };
  FileApi.createFile(fileVo);
  return fileVo;
}
