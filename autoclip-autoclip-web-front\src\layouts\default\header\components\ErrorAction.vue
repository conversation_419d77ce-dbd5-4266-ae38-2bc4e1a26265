<template>
  <el-tooltip
    content="错误日志"
    placement="bottom"
    :disabled="getCount <= 0"
    effect="light"
  >
    <template #content>
      {{ t('layout.header.tooltipErrorLog') }}
    </template>
    <el-badge
      :value="getCount"
      :max="99"
      :offset="[0, 10]"
      @click="handleToErrorList"
    >
      <el-icon size="18"> bug </el-icon>
    </el-badge>
  </el-tooltip>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ElTooltip, ElBadge } from 'element-plus';
import { useErrorLogStore } from '@/store/modules/errorLog';
import { PageEnum } from '@/enums/pageEnum';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

// 定义组件名称
defineOptions({ name: 'ErrorAction' });

// 初始化钩子
const { t } = useI18n();
const { push } = useRouter();
const errorLogStore = useErrorLogStore();

// 计算错误日志数量
const getCount = computed(() => errorLogStore.getErrorLogListCount);

// 跳转错误日志页面并重置计数
function handleToErrorList() {
  push(PageEnum.ERROR_LOG_PAGE).then(() => {
    errorLogStore.setErrorLogListCount(0);
  });
}
</script>
