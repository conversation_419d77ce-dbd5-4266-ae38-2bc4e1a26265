<template>
  <div
    class="relative w-full max-w-md rounded-lg border border-gray-100 bg-white shadow-2xl"
  >
    <!-- 关闭按钮 -->
    <el-button
      @click="closeForgotPassword"
      class="absolute right-4 top-4 !border-none !bg-transparent !p-1 !text-gray-400 hover:!text-gray-600"
      text
    >
      <Icon icon="i-mdi:close" />
    </el-button>

    <div class="space-y-6 p-6">
      <!-- 忘记密码标题 -->
      <div class="text-center">
        <h2 class="mb-6 text-xl font-bold text-gray-800">重置密码</h2>
      </div>

      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center class="!mb-8">
        <el-step title="验证身份" />
        <el-step title="设置新密码" />
        <el-step title="完成" />
      </el-steps>

      <!-- 第一步：验证身份 -->
      <div v-if="currentStep === 0">
        <el-form
          ref="verifyFormRef"
          :model="verifyForm"
          :rules="verifyRules"
          @submit.prevent="handleResetPassword"
          class="space-y-4"
        >
          <!-- 邮箱/手机号输入 -->
          <el-form-item prop="account" class="!mb-4">
            <el-input
              v-model="verifyForm.account"
              placeholder="请输入手机号/邮箱"
              class="!h-12"
              size="large"
            >
              <template #prefix>
                <Icon icon="i-mdi:email" class="text-gray-400" />
              </template>
            </el-input>
          </el-form-item>

          <!-- 验证码输入 -->
          <el-form-item prop="verifyCode" class="!mb-4">
            <el-input
              v-model="verifyForm.verifyCode"
              type="text"
              placeholder="请输入验证码"
              autocomplete="off"
              class="!h-12"
              size="large"
            >
              <template #prefix>
                <Icon icon="i-mdi:key-variant" class="text-gray-400" />
              </template>
              <!-- 获取验证码按钮 -->
              <template #suffix>
                <el-button
                  :disabled="verifyCountdown > 0"
                  @click="getVerifyCode"
                  text
                  class="!text-primary !px-3 !py-1 !text-sm"
                >
                  {{
                    verifyCountdown > 0
                      ? `${verifyCountdown}s后重新获取`
                      : '获取验证码'
                  }}
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- 新密码输入 -->
        <el-form-item prop="newPassword" class="!mb-4">
          <el-input
            v-model="verifyForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            autocomplete="new-password"
            class="!h-12"
            size="large"
            show-password
          >
            <template #prefix>
              <Icon icon="i-mdi:lock" class="text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <!-- 确认密码输入 -->
        <el-form-item prop="confirmPassword" class="!mb-4">
          <el-input
            v-model="verifyForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            autocomplete="new-password"
            class="!h-12"
            size="large"
            show-password
          >
            <template #prefix>
              <Icon icon="i-mdi:lock-check" class="text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <!-- 重置密码按钮 -->
        <el-form-item class="!mb-0">
          <el-button
            :loading="resetLoading"
            @click="handleResetPassword"
            class="!h-12 !w-full !font-medium"
            size="large"
          >
            重置密码
          </el-button>
        </el-form-item>
      </div>

      <!-- 第三步：完成 -->
      <div v-if="currentStep === 1" class="space-y-4 text-center">
        <div class="flex justify-center">
          <Icon icon="i-mdi:check-circle" class="text-6xl text-green-500" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800">密码重置成功！</h3>
        <p class="text-gray-600">
          您的密码已成功重置，现在可以使用新密码登录了。
        </p>

        <el-button
          @click="switchForm(FormType.LOGIN)"
          class="!h-12 !w-full !font-medium"
          size="large"
        >
          返回登录
        </el-button>
      </div>

      <!-- 返回登录链接 -->
      <p v-if="currentStep < 2" class="text-center text-sm text-gray-500">
        想起密码了？
        <el-link
          type="primary"
          :underline="'never'"
          @click="switchForm(FormType.LOGIN)"
          class="hover:underline"
        >
          返回登录
        </el-link>
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import type { FormInstance, FormItemRule, FormRules } from 'element-plus';
import { FormType } from './useLogin';
import AuthUtil, { getIdentifierType, SmsSceneEnum } from '@/api/member/auth';
import UserApi from '@/api/member/user';

const { $notification } = useMessage();
// Emits 类型定义
type Emits = {
  (event: 'close'): void;
  (event: 'switch-form', formType: FormType): void;
};

const emit = defineEmits<Emits>();

const verifyFormRef = ref<FormInstance>();

// 当前步骤
const currentStep = ref(0);

// 验证表单
interface VerifyForm {
  account: string;
  verifyCode: string;
  newPassword: string;
  confirmPassword: string;
}

// 状态管理
const verifyLoading = ref(false);
const resetLoading = ref(false);
const verifyCountdown = ref(0);

const verifyForm = reactive<VerifyForm>({
  account: '',
  verifyCode: '',
  newPassword: '',
  confirmPassword: '',
});

// 密码强度验证
const passwordValidation = computed(() => {
  const password = verifyForm.newPassword;
  return {
    length: password.length >= 8,
    hasUpperCase: /[A-Z]/.test(password),
    hasLowerCase: /[a-z]/.test(password),
    hasNumber: /\d/.test(password),
  };
});

// 验证表单规则
const verifyRules = computed<FormRules>(() => {
  const baseRules: Recordable<Array<FormItemRule>> = {
    account: [
      {
        required: true,
        message: '请输入手机号/邮箱',
        trigger: 'blur',
      },
    ],
    verifyCode: [
      {
        required: true,
        message: '请输入验证码',
        trigger: 'blur',
      },
      {
        pattern: /^\d+$/,
        message: '请输入4位数字验证码',
        trigger: 'blur',
      },
    ],
    newPassword: [
      {
        required: true,
        message: '请输入新密码',
        trigger: 'blur',
      },
      {
        min: 8,
        message: '密码长度至少8位',
        trigger: 'blur',
      },
      {
        validator: (_rule, _value, callback) => {
          const validation = passwordValidation.value;
          if (
            !validation.length ||
            !validation.hasUpperCase ||
            !validation.hasLowerCase ||
            !validation.hasNumber
          ) {
            callback(new Error('密码必须包含大小写字母和数字'));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    confirmPassword: [
      {
        required: true,
        message: '请确认新密码',
        trigger: 'blur',
      },
      {
        validator: (_rule, value, callback) => {
          if (value !== verifyForm.newPassword) {
            callback(new Error('两次输入的密码不一致'));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
  };

  baseRules.account?.push({
    pattern: /^1[3-9]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    message: '请输入正确的手机号或邮箱',
    trigger: 'blur',
  });

  return baseRules as FormRules;
});

const resetForm = () => {
  // 重置验证表单
  verifyForm.account = '';
  verifyForm.verifyCode = '';
  verifyForm.newPassword = '';
  verifyForm.confirmPassword = '';

  // 重置表单验证状态
  verifyFormRef.value?.resetFields();

  // 重置状态
  currentStep.value = 0;
  verifyCountdown.value = 0;
  verifyLoading.value = false;
  resetLoading.value = false;
};

// 关闭模态框
const closeForgotPassword = () => {
  emit('close');
  resetForm();
};

// 返回登录
const switchForm = (formType: FormType) => {
  resetForm();
  emit('switch-form', formType);
};

// 获取验证码
const getVerifyCode = async () => {
  verifyFormRef.value?.validate();
  verifyCountdown.value = 60;
  const timer = setInterval(() => {
    verifyCountdown.value--;
    if (verifyCountdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);

  AuthUtil.sendAuthCode(
    verifyForm.account,
    getIdentifierType(verifyForm.account),
    SmsSceneEnum.MEMBER_RESET_PASSWORD,
  )
    .then(() => {
      $notification.success('验证码已发送，请注意查收');
    })
    .catch(() => {
      verifyCountdown.value = 0;
      clearInterval(timer);
    });
};

const handleResetPassword = async () => {
  if (!verifyFormRef.value) return;

  verifyFormRef.value.validate();

  resetLoading.value = true;

  UserApi.resetUserPassword({
    password: verifyForm.confirmPassword,
    code: verifyForm.verifyCode,
    identifier: verifyForm.account,
    identifierType: getIdentifierType(verifyForm.account),
  })
    .then(() => {
      $notification.success('密码重置成功');
      switchForm(FormType.LOGIN);
    })
    .finally(() => {
      resetLoading.value = false;
    });
};
</script>

<style scoped lang="scss">
/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid rgb(229 231 235);
  padding: 12px;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(156 163 175);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

:deep(.el-button--primary) {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  border-width: 2px;
}

:deep(.el-button--primary:hover) {
  background-color: var(--el-color-primary-dark-2);
  border-color: var(--el-color-primary-dark-2);
}

:deep(.el-radio-group) {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-radio__input) {
  display: none;
}

:deep(.el-radio__label) {
  padding-left: 0;
  color: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

:deep(.el-radio.is-checked .el-radio__label) {
  color: var(--el-color-primary);
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 4px;
}

:deep(.el-steps) {
  margin-bottom: 2rem;
}

:deep(.el-step__title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-step__head.is-process) {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

:deep(.el-step__head.is-finish) {
  color: var(--el-color-success);
  border-color: var(--el-color-success);
}
</style>
