{"mcpServers": {"Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "interactive": {"command": "npx", "args": ["-y", "interactive-mcp"], "disabled": true, "autoApprove": ["message_complete_notification", "start_intensive_chat", "ask_intensive_chat", "stop_intensive_chat"]}, "filesystem": {"command": "uvx", "args": ["mcp-server-filesystem", "--", "/path/to/allowed/files"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": true, "autoApprove": ["read_file", "list_directory"]}, "brave-search": {"command": "uvx", "args": ["mcp-server-brave-search"], "env": {"BRAVE_API_KEY": "your-api-key-here", "FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": true, "autoApprove": []}, "github": {"command": "uvx", "args": ["mcp-server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-token-here", "FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": true, "autoApprove": []}, "crawl4ai": {"command": "%USERPROFILE%\\.cherrystudio\\bin\\uv", "args": ["run", "python", "F:/cherry/MCP/crawl4ai-mcp/crawl4ai_mcp_server/standalone_server.py"], "env": {"CRAWL4AI_HEADLESS": "false", "CRAWL4AI_BROWSER_PATH": "%PROGRAMFILES(X86)%\\Microsoft\\Edge\\Application\\msedge.exe", "CRAWL4AI_USER_DATA_DIR": "%USERPROFILE%\\.crawl4ai\\edge_profile", "CRAWL4AI_PERSISTENT_CONTEXT": "true", "CRAWL4AI_VIEWPORT_WIDTH": "1280", "CRAWL4AI_VIEWPORT_HEIGHT": "720"}, "disabled": false, "autoApprove": []}}}