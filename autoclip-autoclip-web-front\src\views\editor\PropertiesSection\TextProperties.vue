<template>
  <div class="space-y-4 rounded-lg bg-white p-4" v-if="textElement">
    <div class="grid grid-cols-2 gap-8">
      <!-- Text Content -->
      <div class="space-y-2">
        <h4 class="font-semibold text-gray-900">Text Content</h4>
        <div>
          <textarea
            :value="textElement.text"
            @input="
              (e: Event) =>
                onUpdateText(textElement.id, {
                  text: (e.target as HTMLTextAreaElement).value,
                })
            "
            class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows="3"
          />
        </div>
      </div>
      <!-- Timing Position -->
      <div class="space-y-2">
        <h4 class="font-semibold text-gray-900">Timing Position</h4>
        <div class="flex items-center space-x-4">
          <div>
            <label class="block text-sm text-gray-700">Start (s)</label>
            <input
              type="number"
              :value="textElement.positionStart"
              min="0"
              readonly
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    positionStart: Number((e.target as HTMLInputElement).value),
                    positionEnd:
                      Number((e.target as HTMLInputElement).value) +
                      (textElement.positionEnd - textElement.positionStart),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">End (s)</label>
            <input
              type="number"
              readonly
              :value="textElement.positionEnd"
              :min="textElement.positionStart"
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    positionEnd: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
      <!-- Visual Properties -->
      <div class="space-y-2">
        <h4 class="font-semibold text-gray-900">Visual Properties</h4>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm text-gray-700">X Position</label>
            <input
              type="number"
              step="10"
              :value="textElement.x || 0"
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    x: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Y Position</label>
            <input
              type="number"
              step="10"
              :value="textElement.y || 0"
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    y: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Font Size</label>
            <input
              type="number"
              step="5"
              :value="textElement.fontSize || 24"
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    fontSize: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <!-- Font Type -->
          <div>
            <label class="block text-sm font-medium text-gray-700"
              >Font Type</label
            >
            <select
              :value="textElement.font"
              @change="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    font: (e.target as HTMLSelectElement).value,
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Arial">Arial</option>
              <option value="Inter">Inter</option>
              <option value="Lato">Lato</option>
            </select>
          </div>
        </div>
      </div>
      <!-- Style Properties -->
      <div class="space-y-2">
        <h4 class="font-semibold text-gray-900">Style Properties</h4>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm text-gray-700">Text Color</label>
            <input
              type="color"
              :value="textElement.color || '#ffffff'"
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    color: (e.target as HTMLInputElement).value,
                  })
              "
              class="h-10 w-full rounded border border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Opacity</label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              :value="textElement.opacity"
              @input="
                (e: Event) =>
                  onUpdateText(textElement.id, {
                    opacity: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useProjectStore } from '@/store/editor';
import type { TextElement } from '@/types/editor';

const projectStore = useProjectStore();
const textElements = computed(() => projectStore.getTextElements);
const activeElementIndex = computed(() => projectStore.getActiveElementIndex);
const textElement = computed(
  () => textElements.value[activeElementIndex.value],
);

function onUpdateText(id: string, updates: Partial<TextElement>) {
  projectStore.setTextElements(
    textElements.value.map((text) =>
      text.id === id ? { ...text, ...updates } : text,
    ),
  );
}
</script>
