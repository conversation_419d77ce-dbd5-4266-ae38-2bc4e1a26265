<template>
  <el-menu-item @click="push(menu.path)" :index="menu.path">
    <el-icon v-if="menu.icon">
      <component :is="menu.icon" />
    </el-icon>

    <span>{{ menu.meta?.title ?? menu.name }}</span>
  </el-menu-item>
</template>

<script lang="ts" setup>
import type { Menu } from '@/router/types';
import { useRouter } from 'vue-router';

const { push } = useRouter();

defineProps<{
  menu: Menu;
}>();
</script>

<style scoped lang="scss"></style>
