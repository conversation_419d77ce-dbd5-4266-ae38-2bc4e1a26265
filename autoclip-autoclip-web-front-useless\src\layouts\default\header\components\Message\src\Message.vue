<script lang="ts" setup>
import * as NotifyMessageApi from '@/api/system/notify/message';
import { useUserStore } from '@/store/modules/user';
import { XButton } from '@/components/XButton';
import { formatDate } from '@/utils/dateUtil';
import { Icon } from '@/components/Icon';

defineOptions({ name: 'Message' });

const { push } = useRouter();
const userStore = useUserStore();
const activeName = ref('notice');
const unreadCount = ref(0); // 未读消息数量
const list = ref<any[]>([]); // 消息列表

// 获得消息列表
const getList = async () => {
  list.value = await NotifyMessageApi.getUnreadNotifyMessageList();
};

// 获得未读消息数
const getUnreadCount = async () => {
  NotifyMessageApi.getUnreadNotifyMessageCount().then((data) => {
    unreadCount.value = data;
  });
};

// 跳转我的站内信
const goMyList = () => {
  push({
    name: 'MyNotifyMessage',
  });
};

// ========== 初始化 =========
onMounted(() => {
  // 首次加载小红点
  getUnreadCount();
  // 轮询刷新小红点
  setInterval(
    () => {
      if (userStore.getIsSetUser) {
        getUnreadCount();
      } else {
        unreadCount.value = 0;
      }
    },
    1000 * 60 * 2,
  );
});
</script>
<template>
  <div class="message">
    <ElPopover :width="400" placement="bottom" trigger="click">
      <template #reference>
        <ElBadge :is-dot="unreadCount > 0" class="item">
          <Icon
            :size="18"
            class="cursor-pointer"
            icon="ep:bell"
            @click="getList"
          />
        </ElBadge>
      </template>
      <ElTabs v-model="activeName">
        <ElTabPane label="我的站内信" name="notice">
          <el-scrollbar class="message-list">
            <template v-for="item in list" :key="item.id">
              <div
                class="message-item border-b-1 cursor-pointer rounded-xl transition-all delay-0 duration-500 hover:backdrop-brightness-90"
              >
                <img alt="" class="message-icon" src="@/assets/imgs/boy.png" />
                <div class="message-content flex flex-col">
                  <div class="mb-2 flex items-center gap-1 font-bold">
                    <Icon :size="14" icon="i-ep:user" />
                    {{ item.templateNickname }}:
                  </div>
                  <span class="mb-2 brightness-125">
                    {{ item.templateContent }}
                  </span>
                  <div class="flex items-center gap-1">
                    <Icon :size="14" icon="i-ep:calendar" />
                    {{ formatDate(item.createTime) }}
                  </div>
                </div>
              </div>
            </template>
          </el-scrollbar>
        </ElTabPane>
      </ElTabs>
      <!-- 更多 -->
      <div style="margin-top: 10px; text-align: right">
        <XButton
          preIcon="ep:view"
          title="查看全部"
          type="primary"
          @click="goMyList"
        />
      </div>
    </ElPopover>
  </div>
</template>
<style lang="scss" scoped>
.message-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  line-height: 45px;
}

.message-list {
  display: flex;
  height: 400px;
  flex-direction: column;

  .message-item {
    display: flex;
    align-items: center;
    padding: 20px 0;

    &:last-child {
      border: none;
    }

    .message-icon {
      width: 40px;
      height: 40px;
      margin: 0 20px 0 5px;
    }
  }
}
</style>
