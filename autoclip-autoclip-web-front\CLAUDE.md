# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development**: `pnpm dev` - Start development server
- **Build**: `pnpm build` - Build for production (runs TypeScript check, build, and post-build scripts)
- **Type Check**: `pnpm lint:tsc` - Run TypeScript type checking without emitting files
- **Format**: `pnpm lint:prettier` - Format code with Prettier
- **Preview**: `pnpm preview` - Preview production build locally

## Architecture Overview

This is a Vue 3 + TypeScript video editing and auto-clipping web application with a hybrid Vue/React architecture.

### Tech Stack
- **Frontend Framework**: Vue 3 with Composition API and `<script setup>`
- **Build Tool**: Vite with custom plugin configuration
- **UI Library**: Element Plus with custom theming
- **Styling**: SCSS + UnoCSS + Tailwind utilities
- **State Management**: 
  - Pinia for Vue components
  - Zustand for React components (used in video editor)
  - Custom state bridge synchronizing between both stores
- **Video Processing**: FFmpeg via @ffmpeg/ffmpeg, Remotion for video composition
- **Routing**: Vue Router with route guards and authentication
- **Internationalization**: Vue I18n with English and Chinese support
- **HTTP Client**: Axios with custom interceptors and retry logic

### Key Directory Structure

#### Core Application (`src/`)
- **`main.ts`**: Application bootstrap with store, router, i18n, and global directive setup
- **`App.vue`**: Root component with Element Plus configuration provider
- **`router/`**: Vue Router configuration with authentication guards
- **`store/`**: 
  - Pinia stores for Vue (`modules/`)
  - Zustand stores for React components (`editor/react/`)
  - State bridge synchronization (`stateBridge.ts`)

#### Views and Features
- **`views/autoclip/`**: Auto-clipping functionality for different sports (badminton, ping-pong)
  - `framework/`: Shared framework components with WebSocket message handling
- **`views/editor/`**: Video editor with React/Remotion integration
  - Timeline editing, media properties, FFmpeg rendering
  - Mixed Vue/React architecture for video composition
- **`views/home/<USER>
- **`views/profile/`**: User management and authentication

#### UI Components (`components/`)
- **`Application/`**: App-level providers and configuration
- **`VideoPlayer/`**: Custom video player components
- **`Cropper/`**: Image/video cropping functionality
- **`Form/`**: Form components with validation
- **`Loading/`**: Loading states and indicators

#### Configuration and Utils
- **`settings/`**: App configuration (design, locale, project settings)
- **`utils/`**: 
  - HTTP client with axios configuration
  - Authentication utilities
  - File handling and video processing utilities
  - IndexedDB integration for project storage
- **`locales/`**: i18n configuration and language files

### State Management Architecture

The application uses a unique dual-store pattern:
- **Pinia stores** handle Vue component state
- **Zustand stores** manage React component state (primarily for video editor)
- **State bridge** (`stateBridge.ts`) synchronizes data between both systems

### Build Configuration

- **Vite config** (`vite.config.ts`): Custom plugin setup, proxy configuration, SCSS/Less preprocessing
- **Path aliases**: `@/` maps to `src/`, `/#/` maps to `types/`
- **Environment variables**: Loaded via Vite with custom wrapper utilities
- **Build optimization**: Terser minification, chunk splitting, compression plugins

### Authentication & API

- **JWT-based authentication** with encrypted storage
- **API structure** organized by domain (`api/autoclip/`, `api/member/`, etc.)
- **Axios interceptors** for request/response handling and retry logic
- **WebSocket integration** for real-time video processing updates

### Video Processing Pipeline

1. **File Upload**: Drag-drop or file selection with preview
2. **Processing**: WebSocket communication for progress updates
3. **Editing**: Remotion-based video composition with React components
4. **Export**: FFmpeg-based rendering with progress tracking
5. **Storage**: IndexedDB for project persistence

## Important Notes

- The project uses Vue 3 `<script setup>` syntax extensively
- Mixed Vue/React architecture requires careful state synchronization
- Video processing is CPU-intensive and handled via Web Workers where possible
- IndexedDB is used for client-side project and file storage
- WebSocket connections handle real-time processing updates