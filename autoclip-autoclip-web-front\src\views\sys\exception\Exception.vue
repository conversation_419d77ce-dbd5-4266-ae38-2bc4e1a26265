<template>
  <div class="app-exception-page">
    <ElResult
      :status="mapValue?.status"
      :title="title || mapValue?.title"
      :sub-title="subTitle || mapValue?.subTitle"
    >
      <template #extra>
        <ElButton
          v-if="mapValue?.btnText"
          type="primary"
          @click="mapValue?.handler"
        >
          {{ mapValue.btnText }}
        </ElButton>
      </template>

      <template #icon>
        <img v-if="mapValue?.icon" :src="mapValue.icon" alt="exception" />
      </template>
    </ElResult>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, unref } from 'vue';
import { useRoute } from 'vue-router';
import { ElResult, ElButton } from 'element-plus';
import { ExceptionEnum } from '@/enums/exceptionEnum';
import { PageEnum } from '@/enums/pageEnum';
import { useGo, useRedo } from '@/hooks/web/usePage';
import { useI18n } from '@/hooks/web/useI18n';
import notDataSvg from '@/assets/svg/no-data.svg';
import netWorkSvg from '@/assets/svg/net-error.svg';

interface MapValue {
  title: string;
  subTitle: string;
  btnText?: string;
  icon?: string;
  handler?: () => void;
  status?: string;
}

interface Props {
  status?: number;
  title?: string;
  subTitle?: string;
  full?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  status: ExceptionEnum.PAGE_NOT_FOUND,
  title: '',
  subTitle: '',
  full: false,
});

const statusMapRef = ref(new Map<string | number, MapValue>());
const { query } = useRoute();
const go = useGo();
const redo = useRedo();
const { t } = useI18n();

const getStatus = computed(() => {
  const { status: routeStatus } = query;
  return Number(routeStatus) || props.status;
});

const mapValue = computed((): MapValue | undefined => {
  return unref(statusMapRef).get(unref(getStatus));
});

// 初始化状态映射
const backLoginI18n = t('sys.exception.backLogin');
const backHomeI18n = t('sys.exception.backHome');

// 403 无权限访问
unref(statusMapRef).set(ExceptionEnum.PAGE_NOT_ACCESS, {
  title: '403',
  status: `${ExceptionEnum.PAGE_NOT_ACCESS}`,
  subTitle: t('sys.exception.subTitle403'),
  btnText: props.full ? backLoginI18n : backHomeI18n,
  handler: () => (props.full ? go(PageEnum.BASE_HOME) : go()),
});

// 404 页面不存在
unref(statusMapRef).set(ExceptionEnum.PAGE_NOT_FOUND, {
  title: '404',
  status: `${ExceptionEnum.PAGE_NOT_FOUND}`,
  subTitle: t('sys.exception.subTitle404'),
  btnText: props.full ? backLoginI18n : backHomeI18n,
  handler: () => (props.full ? go(PageEnum.BASE_HOME) : go()),
});

// 500 服务器错误
unref(statusMapRef).set(ExceptionEnum.ERROR, {
  title: '500',
  status: `${ExceptionEnum.ERROR}`,
  subTitle: t('sys.exception.subTitle500'),
  btnText: backHomeI18n,
  handler: () => go(),
});

// 无数据
unref(statusMapRef).set(ExceptionEnum.PAGE_NOT_DATA, {
  title: t('sys.exception.noDataTitle'),
  subTitle: '',
  btnText: t('common.redo'),
  handler: () => redo(),
  icon: notDataSvg,
});

// 网络错误
unref(statusMapRef).set(ExceptionEnum.NET_WORK_ERROR, {
  title: t('sys.exception.networkErrorTitle'),
  subTitle: t('sys.exception.networkErrorSubTitle'),
  btnText: t('common.redo'),
  handler: () => redo(),
  icon: netWorkSvg,
});
</script>

<style lang="scss" scoped>
.app-exception-page {
  display: flex;
  align-items: center;
  flex-direction: column;

  :deep(.el-result__icon) {
    img {
      max-width: 400px;
      max-height: 300px;
    }
  }
}
</style>
