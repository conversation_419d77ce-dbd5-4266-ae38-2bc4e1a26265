import { getFile } from '@/store/editor';
import {
  ActiveElement,
  ExportConfig,
  MediaFile,
  ProjectState,
  TextElement,
} from '@/types/editor';
import { defineStore } from 'pinia';
import { buildUUID } from '@/utils/uuid';

const calculateTotalDuration = (
  mediaFiles: MediaFile[],
  textElements: TextElement[],
): number => {
  const mediaDurations = mediaFiles.map((v) => v.positionEnd);
  const textDurations = textElements.map((v) => v.positionEnd);
  return Math.max(0, ...mediaDurations, ...textDurations);
};

export const useProjectStore = defineStore('project', {
  state: (): ProjectState => ({
    id: buildUUID(),
    projectName: '',
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    mediaFiles: [],
    textElements: [],
    currentTime: 0,
    isPlaying: false,
    isMuted: false,
    duration: 0,
    zoomLevel: 1,
    timelineZoom: 100,
    enableMarkerTracking: true,
    activeSection: 'media',
    activeElement: null,
    activeElementIndex: 0,
    resolution: { width: 1920, height: 1080 },
    fps: 30,
    aspectRatio: '16:9',
    history: [],
    future: [],
    exportSettings: {
      resolution: '1080p',
      quality: 'high',
      speed: 'fastest',
      fps: 30,
      format: 'mp4',
      includeSubtitles: false,
    },
    filesID: [],
  }),

  getters: {
    // Project basic info getters
    getId(): string {
      return this.id;
    },
    getProjectName(): string {
      return this.projectName;
    },
    getCreatedAt(): string {
      return this.createdAt;
    },
    getLastModified(): string {
      return this.lastModified;
    },

    // Media and text elements getters
    getMediaFiles(): MediaFile[] {
      return this.mediaFiles;
    },
    getTextElements(): TextElement[] {
      return this.textElements;
    },
    getFilesID(): string[] {
      return this.filesID || [];
    },
    // Playback state getters
    getCurrentTime(): number {
      return this.currentTime;
    },
    getIsPlaying(): boolean {
      return this.isPlaying;
    },
    getIsMuted(): boolean {
      return this.isMuted;
    },
    getDuration(): number {
      return this.duration;
    },
    // UI state getters
    getZoomLevel(): number {
      return this.zoomLevel;
    },
    getTimelineZoom(): number {
      return this.timelineZoom;
    },
    getEnableMarkerTracking(): boolean {
      return this.enableMarkerTracking;
    },

    // Active element getters
    getActiveSection(): ActiveElement {
      return this.activeSection;
    },
    getActiveElement(): ActiveElement | null {
      return this.activeElement;
    },
    getActiveElementIndex(): number {
      return this.activeElementIndex;
    },

    // Project settings getters
    getResolution(): { width: number; height: number } {
      return this.resolution;
    },
    getFps(): number {
      return this.fps;
    },
    getAspectRatio(): string {
      return this.aspectRatio;
    },

    // History getters
    getHistory(): ProjectState[] {
      return this.history;
    },
    getFuture(): ProjectState[] {
      return this.future;
    },

    // Export settings getters
    getExportSettings(): ExportConfig {
      return this.exportSettings;
    },

    // Computed getters
    getTotalDuration(): number {
      return calculateTotalDuration(this.mediaFiles, this.textElements);
    },

    // Get current active media file
    getCurrentMediaFile(): MediaFile | null {
      if (
        this.activeElement === 'media' &&
        this.mediaFiles[this.activeElementIndex]
      ) {
        return this.mediaFiles[this.activeElementIndex];
      }
      return null;
    },

    // Get current active text element
    getCurrentTextElement(): TextElement | null {
      if (
        this.activeElement === 'text' &&
        this.textElements[this.activeElementIndex]
      ) {
        return this.textElements[this.activeElementIndex];
      }
      return null;
    },

    // Get files by type
    getVideoFiles(): MediaFile[] {
      return this.mediaFiles.filter((file) => file.type === 'video');
    },
    getAudioFiles(): MediaFile[] {
      return this.mediaFiles.filter((file) => file.type === 'audio');
    },
    getImageFiles(): MediaFile[] {
      return this.mediaFiles.filter((file) => file.type === 'image');
    },

    // Check if project has content
    hasContent(): boolean {
      return this.mediaFiles.length > 0 || this.textElements.length > 0;
    },

    // Check if project is empty
    isEmpty(): boolean {
      return this.mediaFiles.length === 0 && this.textElements.length === 0;
    },
  },

  actions: {
    setMediaFiles(mediaFiles: MediaFile[]) {
      this.mediaFiles.splice(0, this.mediaFiles.length, ...mediaFiles);
      this.duration = calculateTotalDuration(
        this.mediaFiles,
        this.textElements,
      );
      this.lastModified = new Date().toISOString();
    },

    setProjectName(name: string) {
      this.projectName = name;
      this.lastModified = new Date().toISOString();
    },

    setProjectId(id: string) {
      this.id = id;
    },

    setProjectCreatedAt(date: string) {
      this.createdAt = date;
    },

    setProjectLastModified(date: string) {
      this.lastModified = date;
    },

    setTextElements(elements: TextElement[]) {
      this.textElements = elements;
      this.duration = calculateTotalDuration(
        this.mediaFiles,
        this.textElements,
      );
      this.lastModified = new Date().toISOString();
    },

    setCurrentTime(time: number) {
      this.currentTime = time;
    },

    setIsPlaying(isPlaying: boolean) {
      this.isPlaying = isPlaying;
    },

    setIsMuted(isMuted: boolean) {
      this.isMuted = isMuted;
    },

    setActiveSection(section: ActiveElement) {
      this.activeSection = section;
    },

    setActiveElement(element: ActiveElement | null) {
      this.activeElement = element;
    },

    setActiveElementIndex(index: number) {
      this.activeElementIndex = index;
    },

    setFilesID(ids: string[]) {
      this.filesID = ids;
    },

    setExportSettings(settings: ExportConfig) {
      this.exportSettings = settings;
      this.lastModified = new Date().toISOString();
    },

    setResolution(resolution: string) {
      this.exportSettings.resolution = resolution;
      this.lastModified = new Date().toISOString();
    },

    setQuality(quality: string) {
      this.exportSettings.quality = quality;
      this.lastModified = new Date().toISOString();
    },

    setSpeed(speed: string) {
      this.exportSettings.speed = speed;
      this.lastModified = new Date().toISOString();
    },

    setFps(fps: number) {
      this.exportSettings.fps = fps;
      this.lastModified = new Date().toISOString();
    },

    setTimelineZoom(zoom: number) {
      this.timelineZoom = zoom;
    },

    setMarkerTrack(enabled: boolean) {
      this.enableMarkerTracking = enabled;
    },

    // Helper actions
    addMediaFile(mediaFile: MediaFile) {
      this.mediaFiles.push(mediaFile);
      this.duration = calculateTotalDuration(
        this.mediaFiles,
        this.textElements,
      );
      this.lastModified = new Date().toISOString();
    },

    removeMediaFile(fileId: string) {
      this.mediaFiles = this.mediaFiles.filter((file) => file.id !== fileId);
      this.duration = calculateTotalDuration(
        this.mediaFiles,
        this.textElements,
      );
      this.lastModified = new Date().toISOString();
    },

    addTextElement(textElement: TextElement) {
      this.textElements.push(textElement);
      this.duration = calculateTotalDuration(
        this.mediaFiles,
        this.textElements,
      );
      this.lastModified = new Date().toISOString();
    },

    removeTextElement(elementId: string) {
      this.textElements = this.textElements.filter(
        (element) => element.id !== elementId,
      );
      this.duration = calculateTotalDuration(
        this.mediaFiles,
        this.textElements,
      );
      this.lastModified = new Date().toISOString();
    },

    updateMediaFile(fileId: string, updates: Partial<MediaFile>) {
      const index = this.mediaFiles.findIndex((file) => file.id === fileId);
      if (index !== -1) {
        this.mediaFiles[index] = Object.assign(this.mediaFiles[index], updates);
        this.duration = calculateTotalDuration(
          this.mediaFiles,
          this.textElements,
        );
        this.lastModified = new Date().toISOString();
      }
    },

    updateTextElement(elementId: string, updates: Partial<TextElement>) {
      const index = this.textElements.findIndex(
        (element) => element.id === elementId,
      );
      if (index !== -1) {
        this.textElements[index] = Object.assign(
          this.textElements[index],
          updates,
        );
        this.duration = calculateTotalDuration(
          this.mediaFiles,
          this.textElements,
        );
        this.lastModified = new Date().toISOString();
      }
    },

    async rehydrate(state: ProjectState) {
      const { mediaFiles, textElements, ...rest } = state;
      const mediaFilesWithSrc = await Promise.all(
        mediaFiles.map(async (media: MediaFile) => {
          const file = await getFile(media.fileId);
          return file ? { ...media, src: URL.createObjectURL(file) } : media;
        }),
      );

      this.mediaFiles.splice(0, state.mediaFiles.length, ...mediaFilesWithSrc);
      this.textElements.splice(0, state.textElements.length, ...textElements);
      Object.assign(this, {
        ...rest,
      });
    },

    createNewProject() {
      Object.assign(this, {
        id: buildUUID(),
        projectName: '',
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        mediaFiles: [],
        textElements: [],
        currentTime: 0,
        isPlaying: false,
        isMuted: false,
        duration: 0,
        zoomLevel: 1,
        timelineZoom: 100,
        enableMarkerTracking: true,
        activeSection: 'media',
        activeElement: null,
        activeElementIndex: 0,
        resolution: { width: 1920, height: 1080 },
        fps: 30,
        aspectRatio: '16:9',
        history: [],
        future: [],
        exportSettings: {
          resolution: '1080p',
          quality: 'high',
          speed: 'fastest',
          fps: 30,
          format: 'mp4',
          includeSubtitles: false,
        },
        filesID: [],
      });
    },

    // File management actions
    async getFile(fileId: string) {
      // This will be used by components to get file data
      const { getFile } = await import('@/store/editor');
      return await getFile(fileId);
    },
  },
});
