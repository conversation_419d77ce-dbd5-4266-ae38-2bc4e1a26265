import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const { t } = useI18n();

export const history: AppRouteModule = {
  path: '/history',
  name: 'history',
  component: LAYOUT,
  redirect: '/history/index',
  meta: {
    title: t('历史记录'),
    icon: 'login',
    orderNo: 3,
    hideChildrenInMenu: true,
    hideFooter: true,
  },
  children: [
    {
      path: '/history/index',
      name: 'historyIndex',
      component: () => import('@/views/history/index.vue'),
      meta: {
        hidden: true,
        title: '首页',
        noTagsView: true,
      },
    },
  ],
};

export default history;
