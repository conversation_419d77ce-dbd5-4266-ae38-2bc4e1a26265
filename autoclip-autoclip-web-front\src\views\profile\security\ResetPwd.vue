<template>
  <div class="flex flex-col align-middle">
    <ChangePasswordForm
      ref="changePasswordFormRef"
      :identifier="mobileOrEmail"
      :display-label="true"
    />
    <div class="mx-auto mt-4">
      <XButton :title="t('common.save')" type="primary" @click="submit()" />
      <XButton :title="t('common.reset')" type="danger" @click="reset()" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import ChangePasswordForm from '@/components/Login/src/RegisterForm/ChangePasswordForm.vue';
import { XButton } from '@/components/XButton';

const changePasswordFormRef = ref();
const { t } = useI18n();
const mobileOrEmail = ref('');
const submit = async () => {
  await changePasswordFormRef.value?.handleChangePassword();
};
const reset = () => {
  changePasswordFormRef.value?.resetForm();
};
</script>

<style lang="scss" scoped></style>
