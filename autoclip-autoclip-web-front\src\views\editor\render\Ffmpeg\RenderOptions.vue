<template>
  <div class="relative">
    <div class="z-50 flex items-center justify-center">
      <div
        class="w-11/12 rounded-lg border border-gray-200 bg-white p-2 shadow-lg"
      >
        <div class="space-y-2">
          <div class="grid grid-cols-1 gap-4">
            <div>
              <!-- Resolution Setting -->
              <label class="text-l mb-2 font-bold text-gray-900"
                >Resolution</label
              >
              <select
                :value="exportSettings.resolution"
                @change="
                  (e) =>
                    projectStore.setResolution(
                      (e.target as HTMLSelectElement).value,
                    )
                "
                class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="1080p">1080p</option>
                <option value="720p">720p</option>
                <option value="480p">480p</option>
                <option value="360p">360p</option>
              </select>
            </div>

            <!-- Quality Setting -->
            <div>
              <label class="text-l mb-2 font-bold text-gray-900">Quality</label>
              <select
                :value="exportSettings.quality"
                @change="
                  (e) =>
                    projectStore.setQuality(
                      (e.target as HTMLSelectElement).value,
                    )
                "
                class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>

            <!-- Processing Speed Setting -->
            <div>
              <label class="text-l mb-2 font-bold text-gray-900"
                >Processing Speed</label
              >
              <select
                :value="exportSettings.speed"
                @change="
                  (e) =>
                    projectStore.setSpeed((e.target as HTMLSelectElement).value)
                "
                class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="fastest">Fastest</option>
                <option value="fast">Fast</option>
                <option value="balanced">Balanced</option>
                <option value="slow">Slow</option>
                <option value="slowest">Slowest</option>
              </select>
            </div>
          </div>
        </div>
        <div class="mt-4 text-sm text-gray-700">
          <p>
            Current settings: {{ exportSettings.resolution }} at
            {{ exportSettings.quality }} quality ({{ exportSettings.speed }}
            processing)
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useProjectStore } from '@/store/editor';

const projectStore = useProjectStore();
const exportSettings = computed(() => projectStore.getExportSettings);
</script>

<style scoped>
:deep(.el-select) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__wrapper:hover) {
  border-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: white;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

:deep(.el-input__inner) {
  color: white;
}

:deep(.el-select-dropdown__item) {
  color: white;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-select-dropdown__item.selected) {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
