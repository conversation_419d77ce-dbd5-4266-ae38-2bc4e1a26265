<template>
  <div
    class="relative w-full max-w-md rounded-lg border border-gray-100 bg-white shadow-2xl"
  >
    <el-button
      @click="closeForm"
      class="absolute right-4 top-4 !border-none !bg-transparent !p-1 !text-gray-400 hover:!text-gray-600"
      text
    >
      <Icon icon="i-mdi:close" />
    </el-button>
    <div class="space-y-6 p-6">
      <div class="text-center">
        <h2 class="mb-6 text-xl font-bold text-gray-800">用户注册</h2>
      </div>

      <el-steps :active="currentStep" align-center class="!mb-8">
        <el-step title="手机号/邮箱注册" />
        <el-step title="设置密码" />
        <el-step title="完成" />
      </el-steps>

      <IdentifierRegisterForm
        v-if="currentStep == 0"
        ref="registerFormRef"
        @register-success="handleRegisterSuccess"
        @switch-form="switchForm"
      />

      <ChangePasswordForm
        v-if="currentStep == 1"
        ref="changePasswordFormRef"
        :is-register="true"
        :identifier="identifierRef"
        :code="codeRef"
        @change-password-success="closeForm()"
      />
      <el-form-item class="!mb-0">
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="handleSubmit"
          class="!h-12 !w-full !font-medium"
          size="large"
        >
          {{ buttonText }}
        </el-button>
      </el-form-item>
      <p class="mt-2 text-center text-sm text-gray-500">
        {{ jumpText?.plain }}
        <el-link
          type="primary"
          :underline="'never'"
          @click="jump()"
          class="hover:underline"
        >
          {{ jumpText?.link }}
        </el-link>
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { FormType } from '@/components/Login/src/useLogin';
import ChangePasswordForm from '@/components/Login/src/RegisterForm/ChangePasswordForm.vue';
import IdentifierRegisterForm from '@/components/Login/src/RegisterForm/IdentifierRegisterForm.vue';

defineOptions({
  name: 'RegisterForm',
});
type Emits = {
  (event: 'close'): void;
  (event: 'switch-form', form: FormType): void;
};

const emit = defineEmits<Emits>();

const codeRef = ref('');
const identifierRef = ref('');

const registerFormRef = ref<typeof IdentifierRegisterForm>();
const changePasswordFormRef = ref<typeof ChangePasswordForm>();

const currentStep = ref(0);

const buttonText = computed(() => {
  if (currentStep.value == 0) {
    return buttonLoading.value ? '注册中...' : '立即注册';
  } else if (currentStep.value == 1) {
    return buttonLoading.value ? '密码设置中...' : '设置密码';
  }
});

const jumpText = computed(() => {
  if (currentStep.value == 0) {
    return {
      plain: '已有账号?',
      link: '返回登录',
    };
  } else if (currentStep.value == 1) {
    return {
      plain: '直接登陆?',
      link: '跳过密码设置',
    };
  }
});
const buttonLoading = computed(() => {
  if (currentStep.value == 0) {
    return registerFormRef.value?.buttonLoading;
  } else if (currentStep.value == 1) {
    return changePasswordFormRef.value?.buttonLoading;
  }
});

const closeForm = () => {
  resetForm();
  emit('close');
};

const switchForm = (formType: FormType) => {
  resetForm();
  emit('switch-form', formType);
};

const resetForm = () => {
  registerFormRef.value?.resetForm();
  changePasswordFormRef.value?.resetForm();
  currentStep.value = 0;
};

const handleRegisterSuccess = (
  isUserExists: boolean,
  code: string,
  identifier: string,
) => {
  if (isUserExists) {
    closeForm();
    return;
  }
  currentStep.value = 1;
  codeRef.value = code;
  identifierRef.value = identifier;
};

const handleSubmit = () => {
  if (currentStep.value == 0) {
    registerFormRef.value?.handleRegister();
  } else if (currentStep.value == 1) {
    changePasswordFormRef.value?.handleChangePassword();
  }
};
const jump = () => {
  if (currentStep.value == 0) {
    switchForm(FormType.LOGIN);
  } else if (currentStep.value == 1) {
    closeForm();
  }
};

defineExpose({
  resetForm,
});
</script>

<style scoped lang="scss">
:deep(.el-input) {
  @apply h-12;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid rgb(229 231 235);
  padding: 12px;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(156 163 175);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

:deep(.el-button--primary) {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  border-width: 2px;
}

:deep(.el-button--primary:hover) {
  background-color: var(--el-color-primary-dark-2);
  border-color: var(--el-color-primary-dark-2);
}

:deep(.el-button--primary:disabled) {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

:deep(.el-checkbox__label) {
  color: rgb(75 85 99);
  font-size: 14px;
  line-height: 1.4;
}

:deep(.el-form-item__error) {
  font-size: 12px;
  color: var(--el-color-danger);
  padding-top: 4px;
}

/* 验证码按钮样式 */
:deep(.el-input__suffix .el-button) {
  height: auto;
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 6px;
  margin-right: 8px;
}

/* 链接样式 */
:deep(.el-link) {
  font-size: 14px;
  padding: 0;
}

:deep(.el-link:hover) {
  text-decoration: underline;
}

:deep(.el-step__title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-step__title.is-process) {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

:deep(.el-step__title.is-finish) {
  color: rgba(var(--el-color-success-rgb), 0.7);
}
</style>
