<!-- 
  Inspira UI 表单组件
  保留 Element Plus 的验证逻辑，增强视觉效果
-->
<template>
  <ErrorBoundary
    fallback-component="el-form"
    :fallback-props="elementPlusProps"
    @error="handleError"
  >
    <el-form
      ref="formRef"
      :class="computedClass"
      :model="model"
      :rules="rules"
      :inline="inline"
      :label-position="labelPosition"
      :label-width="labelWidth"
      :label-suffix="labelSuffix"
      :hide-required-asterisk="hideRequiredAsterisk"
      :show-message="showMessage"
      :inline-message="inlineMessage"
      :status-icon="statusIcon"
      :validate-on-rule-change="validateOnRuleChange"
      :size="size"
      :disabled="disabled"
      @validate="handleValidate"
      @submit="handleSubmit"
    >
      <slot />
    </el-form>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { cn } from '@/lib/utils';
import ErrorBoundary from '../Utils/ErrorBoundary.vue';
import type { InspiraFormProps, InspiraFormEmits } from './types';
import type { FormInstance } from 'element-plus';

const props = withDefaults(defineProps<InspiraFormProps>(), {
  labelPosition: 'right',
  hideRequiredAsterisk: false,
  showMessage: true,
  inlineMessage: false,
  statusIcon: false,
  validateOnRuleChange: true,
  size: 'default',
  disabled: false,
  animation: 'fade',
});

const emit = defineEmits<InspiraFormEmits>();

// 表单引用
const formRef = ref<FormInstance>();

// Element Plus 降级 props
const elementPlusProps = computed(() => ({
  model: props.model,
  rules: props.rules,
  inline: props.inline,
  labelPosition: props.labelPosition,
  labelWidth: props.labelWidth,
  labelSuffix: props.labelSuffix,
  hideRequiredAsterisk: props.hideRequiredAsterisk,
  showMessage: props.showMessage,
  inlineMessage: props.inlineMessage,
  statusIcon: props.statusIcon,
  validateOnRuleChange: props.validateOnRuleChange,
  size: props.size,
  disabled: props.disabled,
  class: props.class,
}));

// 计算样式类
const computedClass = computed(() => {
  return cn(
    'inspira-form',
    `inspira-form--${props.size}`,
    {
      'inspira-form--inline': props.inline,
      'inspira-form--disabled': props.disabled,
      [`inspira-form--${props.animation}`]: props.animation !== 'none',
    },
    props.class
  );
});

// 事件处理
const handleValidate = (prop: string, isValid: boolean, message: string) => {
  emit('validate', prop, isValid, message);
};

const handleSubmit = (event: Event) => {
  event.preventDefault();
  emit('submit', event);
};

const handleError = (error: Error) => {
  console.warn('InspiraForm error, falling back to el-form:', error);
};

// 暴露表单方法
const validate = (callback?: (valid: boolean, fields?: any) => void) => {
  return formRef.value?.validate(callback);
};

const validateField = (props: string | string[], callback?: (errorMessage?: string) => void) => {
  return formRef.value?.validateField(props, callback);
};

const resetFields = () => {
  formRef.value?.resetFields();
};

const scrollToField = (prop: string) => {
  formRef.value?.scrollToField(prop);
};

const clearValidate = (props?: string | string[]) => {
  formRef.value?.clearValidate(props);
};

defineExpose({
  validate,
  validateField,
  resetFields,
  scrollToField,
  clearValidate,
  formRef,
});
</script>

<style scoped>
.inspira-form {
  @apply inspira-ui;
  
  /* 基础表单样式增强 */
  :deep(.el-form-item) {
    margin-bottom: 1.5rem;
    transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
    
    /* 标签样式增强 */
    .el-form-item__label {
      color: var(--inspira-text);
      font-weight: 500;
      line-height: 1.5;
      
      &::before {
        color: #ef4444;
      }
    }
    
    /* 内容区域样式 */
    .el-form-item__content {
      position: relative;
    }
    
    /* 错误消息样式增强 */
    .el-form-item__error {
      color: #ef4444;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 4px;
      padding-left: 4px;
      transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
    }
    
    /* 验证状态样式 */
    &.is-error {
      .el-form-item__content {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          height: 2px;
          background: linear-gradient(90deg, #ef4444, transparent);
          border-radius: 1px;
          opacity: 0.6;
        }
      }
    }
    
    &.is-success {
      .el-form-item__content {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          height: 2px;
          background: linear-gradient(90deg, #10b981, transparent);
          border-radius: 1px;
          opacity: 0.6;
        }
      }
    }
    
    &.is-validating {
      .el-form-item__content {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          height: 2px;
          background: linear-gradient(90deg, var(--inspira-primary), transparent);
          border-radius: 1px;
          opacity: 0.6;
          animation: inspira-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      }
    }
  }
  
  /* 内联表单样式 */
  &--inline {
    :deep(.el-form-item) {
      margin-right: 1rem;
      margin-bottom: 0;
      display: inline-block;
      vertical-align: top;
    }
  }
  
  /* 禁用状态 */
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  /* 尺寸变体 */
  &--small {
    :deep(.el-form-item) {
      margin-bottom: 1rem;
      
      .el-form-item__label {
        font-size: 14px;
      }
    }
  }
  
  &--large {
    :deep(.el-form-item) {
      margin-bottom: 2rem;
      
      .el-form-item__label {
        font-size: 18px;
      }
    }
  }
  
  /* 动画效果 */
  &--fade {
    :deep(.el-form-item) {
      &.is-error {
        animation: inspira-shake 0.5s ease-in-out;
      }
    }
  }
  
  &--slide {
    :deep(.el-form-item__error) {
      transform: translateY(-10px);
      opacity: 0;
      animation: inspira-slide-down 0.3s ease-out forwards;
    }
  }
  
  &--bounce {
    :deep(.el-form-item) {
      &.is-error {
        animation: inspira-bounce 0.6s ease-in-out;
      }
    }
  }
}

/* 动画定义 */
@keyframes inspira-shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes inspira-slide-down {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes inspira-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes inspira-pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inspira-form {
    &--inline {
      :deep(.el-form-item) {
        display: block;
        margin-right: 0;
        margin-bottom: 1rem;
      }
    }
  }
}
</style>