<template>
  <component
    :is="radioComponent"
    v-for="option in item?.componentProps?.options"
    :key="getOptionKey(option)"
    v-bind="getOptionProps(option)"
    :label="getOptionValue(option)"
  >
    {{ getOptionLabel(option) }}
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElRadio, ElRadioButton } from 'element-plus';
import { FormSchema } from '@/types/form';
import { ComponentOptions } from '@/types/components';

interface Props {
  item: FormSchema;
}

const props = defineProps<Props>();

const radioComponent = computed(() => {
  return props.item.component === 'Radio' ? ElRadio : ElRadioButton;
});

const getOptionLabel = (option: ComponentOptions) => {
  const labelAlias = props.item?.componentProps?.optionsAlias?.labelField;
  return labelAlias ? option[labelAlias] : option.label;
};

const getOptionValue = (option: ComponentOptions) => {
  const valueAlias = props.item?.componentProps?.optionsAlias?.valueField;
  return valueAlias ? option[valueAlias] : option.value;
};

const getOptionProps = (option: ComponentOptions) => {
  const { label, value, ...other } = option;
  return other;
};

const getOptionKey = (option: ComponentOptions) => {
  return getOptionValue(option) || getOptionLabel(option);
};
</script>
