<template>
  <div class="w-full">
    <!-- 顶部导航栏 -->
    <div
      class="mb-4 flex h-14 items-center justify-between bg-white px-8 shadow-sm"
    >
      <span class="text-xl font-bold text-gray-800">视频库</span>
      <el-button @click="toggleFilterPanel">
        {{ showFilterPanel ? '隐藏筛选' : '显示筛选' }}
      </el-button>
    </div>

    <!-- 过滤面板 -->
    <transition name="fade">
      <div v-show="showFilterPanel" class="border-b-1 mb-4 p-6">
        <el-form
          :model="filterForm"
          :label-width="isMobile ? '80px' : '100px'"
          class="space-y-2"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <el-form-item label="视频名称">
              <el-input
                :size="isMobile ? 'small' : 'default'"
                v-model="filterForm.fileName"
                placeholder="请输入视频名称"
                clearable
              />
            </el-form-item>
            <el-form-item label="视频类型">
              <el-select
                :size="isMobile ? 'small' : 'default'"
                v-model="filterForm.videoProcessType"
                placeholder="请选择类型"
                clearable
                class="w-full"
              >
                <el-option label="原视频" :value="VideoProcessType.RAW" />
                <el-option
                  label="精彩片段"
                  :value="VideoProcessType.GREAT_MATCH"
                />
                <el-option
                  label="所有比赛"
                  :value="VideoProcessType.ALL_MATCH_MERGED"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="运动类型">
              <el-select
                :size="isMobile ? 'small' : 'default'"
                v-model="filterForm.sportType"
                placeholder="请选择类型"
                clearable
                class="w-full"
              >
                <el-option label="乒乓球" :value="1" />
                <el-option label="羽毛球" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="过期状态">
              <el-select
                :size="isMobile ? 'small' : 'default'"
                v-model="filterForm.isExpired"
                placeholder="请选择状态"
                clearable
                class="w-full"
              >
                <el-option label="未过期" :value="false" />
                <el-option label="已过期" :value="true" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                :size="isMobile ? 'small' : 'default'"
                v-model="createTimeRange"
                :type="isMobile ? 'daterange' : 'datetimerange'"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :format="isMobile ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
                :value-format="isMobile ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
                class="w-full"
                clearable
              />
            </el-form-item>
            <el-form-item label="比赛类型">
              <el-select
                :size="isMobile ? 'small' : 'default'"
                v-model="filterForm.matchType"
                placeholder="请选择类型"
                clearable
                class="w-full"
              >
                <el-option label="单打" :value="1" />
                <el-option label="双打" :value="2" />
              </el-select>
            </el-form-item>
          </div>

          <div class="flex flex-col justify-center gap-3 sm:flex-row">
            <el-button @click="handleFilter" class="w-full sm:w-auto"
              >筛选</el-button
            >
            <el-button @click="handleReset" class="w-full sm:w-auto"
              >重置</el-button
            >
            <el-button @click="toggleFilterPanel" class="w-full sm:w-auto"
              >取消</el-button
            >
          </div>
        </el-form>
      </div>
    </transition>

    <el-empty
      v-if="videos.length === 0 && !loading"
      description="暂无数据"
      class="mt-10rem w-full"
    >
      <template #description>
        <div class="mb-4 text-gray-500">暂无数据</div>
        <el-button
          type="primary"
          @click="handleGoClip"
          class="w-full sm:w-auto"
        >
          去剪辑?
        </el-button>
      </template>
    </el-empty>
    <div
      v-else
      v-loading="loading"
      class="lg:mx-4rem mx-4 grid grid-cols-1 gap-x-6 gap-y-8 px-8 pb-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
    >
      <div
        v-for="(video, index) in videos"
        :key="index"
        class="relative flex flex-col overflow-hidden rounded-2xl p-0 shadow-xl transition-all duration-200"
        :class="{
          [videoTypeBorderColorMap[video.videoProcessType]]: true,
        }"
      >
        <div
          @click="handleVideoClick(video)"
          class="h-full w-full cursor-pointer"
        >
          <div
            class="relative aspect-video h-full w-full bg-gray-200"
            v-if="video.expireTime && Number(video.expireTime) < Date.now()"
          >
            <div
              class="absolute left-2 top-2 rounded bg-black/70 px-2 py-0.5 text-xs text-white"
            >
              已过期
            </div>
            <div class="flex h-full w-full items-center justify-center">
              <div class="i-pajamas:time-out h-1/3 w-1/3 text-gray-500" />
            </div>
          </div>
          <div
            v-else-if="video.videoProcessType === VideoProcessType.GREAT_MATCH"
            class="flex aspect-video h-full w-full items-center justify-center bg-gray-100"
          >
            <div class="i-mdi:zip-box h-60% w-60% text-blue-400"></div>
          </div>
          <div v-else class="relative aspect-video bg-gray-200">
            <img
              :src="
                video.thumbnailUrl ||
                'https://dummyimage.com/320x180/eee/aaa&text=No+Thumbnail'
              "
              alt="thumbnail"
              class="h-full w-full object-cover"
            />
            <span
              class="absolute bottom-2 right-2 rounded bg-black/70 px-2 py-0.5 text-xs text-white"
              >{{
                video.duration ? formatDuration(video.duration) : '00:00'
              }}</span
            >
          </div>
        </div>
        <div class="relative flex flex-1 flex-col px-3 py-2">
          <div class="flex items-center justify-between">
            <div
              class="flex items-center gap-2 truncate text-base font-medium text-gray-900"
              :title="video.fileName"
            >
              <div
                class="rounded-full px-2 py-1 text-xs"
                :class="videoTypeTagColorMap[video.videoProcessType]"
              >
                {{ videoTypeNameMap[video.videoProcessType] }}
              </div>
              <span class="overflow-hidden text-ellipsis whitespace-nowrap">
                {{ video.fileName }}
              </span>
            </div>
            <div class="ml-8">
              <el-dropdown
                :trigger="isMobile ? 'click' : 'hover'"
                @command="handleDropdownCommand($event, video)"
                :show-timeout="100"
                :hide-timeout="100"
                :hide-on-click="true"
              >
                <span
                  class="el-dropdown-link flex h-8 w-8 items-center justify-center rounded-full bg-white/80 shadow hover:bg-white"
                >
                  <div class="i-mdi:dots-vertical"></div>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="config">
                      <div class="i-mdi:settings mr-2"></div>
                      视频配置
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="
                        video.videoProcessType !== VideoProcessType.GREAT_MATCH
                      "
                      command="play"
                    >
                      <div class="i-mdi:play mr-2"></div>
                      播放视频
                    </el-dropdown-item>
                    <el-dropdown-item command="download">
                      <div class="i-mdi:download mr-2"></div>
                      下载视频
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div
            class="ml-1 mt-2 flex items-center justify-between text-xs text-gray-500"
          >
            <span class="text-xs text-gray-500">{{
              formatSize(video.size)
            }}</span>
            <span class="text-xs text-gray-500"
              >{{ formatDate(video.createTime) }} ->
              {{ formatDate(video.expireTime) }}</span
            >
          </div>
        </div>
      </div>
      <div class="col-start-1 -col-end-1 mt-6 flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          :total-pages="totalPage"
          :page-size-options="
            isMobile ? ['10', '20'] : ['10', '20', '50', '100']
          "
          v-model:page-size="pageSize"
          :total="total"
          :size="isMobile ? 'small' : 'default'"
          class="flex flex-wrap justify-center gap-2"
          @update:current-page="fetchVideoList"
          @update:page-size="fetchVideoList"
        />
      </div>
    </div>

    <VideoInfoDialog
      v-model="previewDialogVisible"
      title="视频预览"
      :video-info="currentVideoInfo"
    />
  </div>
</template>

<script setup lang="ts">
import {
  getVideoList,
  VideoInfoRespVO,
  VideoProcessType,
} from '@/api/autoclip/auto_clip';
import { ElDropdown, ElDropdownItem, ElDropdownMenu } from 'element-plus';
import { onMounted, ref } from 'vue';

const isMobile = ref(window.innerWidth <= 768);

const videoTypeNameMap = {
  [VideoProcessType.RAW]: '原视频',
  [VideoProcessType.GREAT_MATCH]: '精彩片段',
  [VideoProcessType.ALL_MATCH_MERGED]: '所有比赛',
};

const videoTypeTagColorMap: Record<VideoProcessType, string> = {
  [VideoProcessType.RAW]: 'bg-gray-100',
  [VideoProcessType.GREAT_MATCH]: 'bg-blue-100',
  [VideoProcessType.ALL_MATCH_MERGED]: 'bg-green-100',
};
const videoTypeBorderColorMap: Record<VideoProcessType, string> = {
  [VideoProcessType.RAW]: 'border-gray-200 shadow-gray-200/50',
  [VideoProcessType.GREAT_MATCH]: 'border-blue-200 shadow-blue-200/50',
  [VideoProcessType.ALL_MATCH_MERGED]: 'border-green-200 shadow-green-200/50',
};

// 过滤相关
const showFilterPanel = ref(false);
const createTimeRange = ref<[string, string] | undefined>(undefined);
const expireTimeRange = ref<[string, string] | undefined>(undefined);
const filterForm = ref({
  fileName: '',
  videoProcessType: undefined as number | undefined,
  sportType: undefined as number | undefined,
  createTimeStart: '',
  createTimeEnd: '',
  expireTimeStart: '',
  expireTimeEnd: '',
  isExpired: undefined as boolean | undefined,
  minDuration: undefined as number | undefined,
  maxDuration: undefined as number | undefined,
  minSize: undefined as number | undefined,
  maxSize: undefined as number | undefined,
  matchType: undefined as number | undefined,
  mode: undefined as number | undefined,
  greatBallEditing: undefined as boolean | undefined,
  removeReplay: undefined as boolean | undefined,
  getMatchSegments: undefined as boolean | undefined,
});

// 示例数据，后续可替换为实际数据
const videos = ref<VideoInfoRespVO[]>([]);
const loading = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const totalPage = ref(0);

const previewDialogVisible = ref(false);
const currentVideoInfo = ref<VideoInfoRespVO | null>(null);

const router = useRouter();
const handleGoClip = () => {
  router.push({ path: '/auto_clip' });
};

const handleVideoClick = (video: VideoInfoRespVO) => {
  if (video.videoProcessType === VideoProcessType.GREAT_MATCH) {
    window.open(video.fileUrl, '_blank');
    return;
  }
  currentVideoInfo.value = video;
  previewDialogVisible.value = true;
};

const handleDropdownCommand = (command: string, video: VideoInfoRespVO) => {
  if (command === 'play') {
    handleVideoClick(video);
  } else if (command === 'config') {
    // TODO: 打开视频配置弹窗
    alert('视频配置功能开发中');
  } else if (command === 'download') {
    window.open(video.fileUrl, '_blank');
  }
};

const formatSize = (size: number) => {
  if (size < 1024) {
    return `${size}B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)}KB`;
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / 1024 / 1024).toFixed(2)}MB`;
  } else {
    return `${(size / 1024 / 1024 / 1024).toFixed(2)}GB`;
  }
};

const formatDuration = (duration: number) => {
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = Math.floor(duration % 60);
  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 格式化日期
const formatDate = (timestamp: string | number) => {
  if (!timestamp) return '';
  const date = new Date(Number(timestamp));
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 过滤相关方法
const toggleFilterPanel = () => {
  showFilterPanel.value = !showFilterPanel.value;
};

const handleFilter = () => {
  // 处理创建时间范围
  if (createTimeRange.value && createTimeRange.value.length === 2) {
    filterForm.value.createTimeStart = createTimeRange.value[0];
    filterForm.value.createTimeEnd = createTimeRange.value[1];
  } else {
    filterForm.value.createTimeStart = '';
    filterForm.value.createTimeEnd = '';
  }

  // 处理过期时间范围
  if (expireTimeRange.value && expireTimeRange.value.length === 2) {
    filterForm.value.expireTimeStart = expireTimeRange.value[0];
    filterForm.value.expireTimeEnd = expireTimeRange.value[1];
  } else {
    filterForm.value.expireTimeStart = '';
    filterForm.value.expireTimeEnd = '';
  }

  // 重置到第一页
  currentPage.value = 1;
  fetchVideoList();
};

const handleReset = () => {
  filterForm.value = {
    fileName: '',
    videoProcessType: undefined,
    sportType: undefined,
    createTimeStart: '',
    createTimeEnd: '',
    expireTimeStart: '',
    expireTimeEnd: '',
    isExpired: undefined,
    minDuration: undefined,
    maxDuration: undefined,
    minSize: undefined,
    maxSize: undefined,
    matchType: undefined,
    mode: undefined,
    greatBallEditing: undefined,
    removeReplay: undefined,
    getMatchSegments: undefined,
  };
  createTimeRange.value = undefined;
  expireTimeRange.value = undefined;
  currentPage.value = 1;
  fetchVideoList();
};

// 请求视频列表
const fetchVideoList = async () => {
  try {
    loading.value = true;

    // 构建过滤参数
    const params: any = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
    };

    // 添加过滤条件
    if (filterForm.value.fileName) {
      params.fileName = filterForm.value.fileName;
    }
    if (filterForm.value.videoProcessType !== undefined) {
      params.videoProcessType = filterForm.value.videoProcessType;
    }
    if (filterForm.value.sportType !== undefined) {
      params.sportType = filterForm.value.sportType;
    }
    if (filterForm.value.createTimeStart) {
      params.createTimeStart = filterForm.value.createTimeStart;
    }
    if (filterForm.value.createTimeEnd) {
      params.createTimeEnd = filterForm.value.createTimeEnd;
    }
    if (filterForm.value.expireTimeStart) {
      params.expireTimeStart = filterForm.value.expireTimeStart;
    }
    if (filterForm.value.expireTimeEnd) {
      params.expireTimeEnd = filterForm.value.expireTimeEnd;
    }
    if (filterForm.value.isExpired !== undefined) {
      params.isExpired = filterForm.value.isExpired;
    }
    if (filterForm.value.minDuration !== undefined) {
      params.minDuration = filterForm.value.minDuration;
    }
    if (filterForm.value.maxDuration !== undefined) {
      params.maxDuration = filterForm.value.maxDuration;
    }
    if (filterForm.value.minSize !== undefined) {
      params.minSize = filterForm.value.minSize;
    }
    if (filterForm.value.maxSize !== undefined) {
      params.maxSize = filterForm.value.maxSize;
    }
    if (filterForm.value.matchType !== undefined) {
      params.matchType = filterForm.value.matchType;
    }
    if (filterForm.value.mode !== undefined) {
      params.mode = filterForm.value.mode;
    }
    if (filterForm.value.greatBallEditing !== undefined) {
      params.greatBallEditing = filterForm.value.greatBallEditing;
    }
    if (filterForm.value.removeReplay !== undefined) {
      params.removeReplay = filterForm.value.removeReplay;
    }
    if (filterForm.value.getMatchSegments !== undefined) {
      params.getMatchSegments = filterForm.value.getMatchSegments;
    }

    const response = await getVideoList(params);
    videos.value.splice(0, videos.value.length);
    videos.value.push(...response.list);
    currentPage.value = response.pageNum ?? 0;
    total.value = response.total ?? 0;
    totalPage.value = response.totalPage ?? 0;
  } catch (error) {
    console.error('Error fetching video list:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchVideoList();
  window.addEventListener('resize', () => {
    isMobile.value = window.innerWidth <= 768;
  });
});
</script>

<style scoped>
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-range-editor.el-input__wrapper) {
  padding: 0;
  padding-left: 5px;
}

/* 过滤面板动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
