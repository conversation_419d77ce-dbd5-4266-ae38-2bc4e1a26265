<script setup lang="ts">
interface Props {
  onClick: () => void;
}

const props = defineProps<Props>();
</script>

<template>
  <button
    class="flex h-auto flex-col items-center justify-center rounded border border-solid border-transparent bg-white px-2 py-2 text-sm font-medium text-gray-800 transition-colors hover:bg-[#ccc] sm:w-auto sm:px-5 sm:text-base dark:hover:bg-[#ccc]"
    @click="props.onClick"
  >
    <img
      alt="Shortcuts"
      class="h-auto max-h-[30px] w-auto max-w-[30px]"
      height="30"
      width="30"
      src="https://www.svgrepo.com/show/501605/keyboard-shortcuts.svg"
    />
    <span class="text-xs">Shortcuts</span>
  </button>
</template>
