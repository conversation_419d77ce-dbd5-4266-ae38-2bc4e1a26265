<template>
  <el-container
    v-loading="getOpenPageLoading && getPageLoading"
    :class="[getLayoutContentMode]"
  >
    <PageLayout />
  </el-container>
</template>
<script lang="ts" setup>
import PageLayout from '../../page/index.vue';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';
import { useContentViewHeight } from './useContentViewHeight';

const { getOpenPageLoading } = useTransitionSetting();
const { getLayoutContentMode, getPageLoading } = useRootSetting();

useContentViewHeight();
</script>
<style lang="scss" scoped>
$prefix-cls: 'layout-content';

.#{prefix-cls} {
  position: relative;
  flex: 1 1 auto;
  min-height: 0;

  &.fixed {
    width: 1200px;
    margin: 0 auto;
  }

  &-loading {
    position: absolute;
    top: 200px;
    z-index: 200;
  }
}
</style>
