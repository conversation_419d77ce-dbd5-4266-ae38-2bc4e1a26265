<template></template>
<script lang="ts" setup>
import { useWebSocket } from '@vueuse/core';
import { useUserStore } from '@/store/modules/user';
import { FileCreateReqVO } from '@/api/infra/file';
import { VideoProcessProgressVO } from '@/api/autoclip/auto_clip';

defineOptions({ name: 'MessageReceiver' });

const { $message } = useMessage();

const userStore = useUserStore();

type Emits = {
  (event: 'handle-progress', progress: VideoProcessProgressVO): void;
};

const emit = defineEmits<Emits>();

const server = computed(() => {
  return (
    (import.meta.env.VITE_GLOB_API_URL + '/infra/ws').replace('http', 'ws') +
    '?token=' +
    userStore.getRefreshToken
  );
});
const getIsOpen = computed(() => {
  return status.value === 'OPEN';
});

const { status, data, close, open } = useWebSocket(server.value, {
  immediate: false,
  autoConnect: true,
  autoReconnect: true,
  heartbeat: true,
});

const clipResult = ref<Nullable<FileCreateReqVO>>(null);

watch(
  data,
  (newData, _) => {
    if (!newData) {
      return;
    }
    try {
      if (newData === 'pong') {
        return;
      }

      const jsonMessage = JSON.parse(newData);
      const type = jsonMessage.type;

      if (!type) {
        $message.error('未知的消息类型：' + newData);
        return;
      }

      const content = JSON.parse(jsonMessage.content);
      if (type === 'video-auto-clip-progress') {
        if (content.code === 0) {
          emit('handle-progress', content.data);
        } else {
          $message.error(content.msg);
        }
        return;
      }

      $message.error('未处理消息：' + newData);
    } catch (error) {
      $message.error('处理消息发生异常：' + newData);
    }
  },
  { immediate: true },
);

defineExpose({
  open,
  close,
  clipResult,
  getIsOpen,
});
</script>
