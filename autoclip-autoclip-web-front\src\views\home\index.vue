<template>
  <AuroraBackground class="min-h-screen">
    <DragDropUpload
      accept="video/*"
      @file-selected="handleFile"
      @drag-enter="handleDragEnter"
      @drag-leave="handleDragLeave"
    >
      <div>
      <div
        class="relative flex flex-col items-center justify-center overflow-x-clip"
        style="min-height: calc(-72px + 100vh)"
      >
        <div
          class="flex flex-row items-center justify-center max-md:flex-col max-md:px-8 md:items-start md:gap-16"
        >
          <!-- 左侧内容 -->
          <div class="max-w-38rem relative flex-1">
            <div
              class="i-my-icons-ball h-1.2rem w-1.2rem absolute -top-12 max-md:left-10 md:left-14"
            ></div>
            <div class="mb-4 flex flex-row items-center justify-center">
              <img
                src="/src/assets/163.png"
                alt="logo"
                class="md:max-w-420px max-w-320px rounded-2rem mb-4"
              />
            </div>
            <div class="max-md:items-center max-md:text-center">
              <p
                class="mb-4 text-4xl font-black leading-tight text-gray-700 md:text-6xl"
              >
                AI比赛视频剪辑
              </p>

              <p
                class="mb-1 text-xl font-bold leading-relaxed text-gray-600 max-md:text-base max-md:font-medium"
              >
                自动剪辑精彩片段，移除休息捡球片段<br />提升剪辑效率
                <strong>300%</strong>
              </p>

              <div v-if="!getIsMobile">
                <button
                  class="mr-6 cursor-pointer rounded-lg border-none bg-gray-800 px-10 py-4 text-lg font-semibold text-white transition-all duration-300 hover:-translate-y-0.5 hover:bg-gray-900 hover:shadow-xl"
                  @click="push('/auto_clip/ping_pong')"
                >
                  立即体验
                </button>

                <button
                  class="inline-flex cursor-pointer items-center gap-2 border-none bg-transparent text-lg text-gray-600 transition-colors duration-300 hover:text-gray-800"
                >
                  <Icon icon="i-mdi:play" />
                  观看视频
                </button>
              </div>
            </div>
          </div>

          <div
            class="max-md:max-w-28rem md:mt-8rem max-w-28rem relative flex flex-1 flex-col items-start justify-center gap-6 max-md:items-center max-md:gap-3"
          >
            <input
              ref="fileInput"
              type="file"
              class="hidden"
              accept="video/mp4,video/webm"
              @change="handleFileSelect"
            />
            <div
              v-if="!getIsMobile"
              class="border-1 flex h-[360px] w-[500px] flex-col items-center justify-center rounded-3xl bg-white pt-36 shadow-2xl"
              @dragenter.prevent="handleDragEnter"
              @dragleave.prevent="handleDragLeave"
              @dragover.prevent
              @drop.prevent="handleDrop"
            >
              <button
                class="mb-8 rounded-full bg-blue-600 px-10 py-4 text-xl text-white shadow hover:bg-blue-700"
                @click="triggerFileInput"
              >
                上传视频
              </button>

              <div class="mb-2 text-xl font-bold text-gray-600">
                或者拖放一个文件,
              </div>
              <div class="mb-8 text-sm text-gray-400">
                粘贴图片或
                <a href="#" class="text-blue-500 underline">URL</a>
              </div>
            </div>
            <div v-else class="mt-6 flex w-full items-center justify-center">
              <button
                class="w-full rounded-full bg-blue-600 bg-opacity-85 px-8 py-3 text-2xl text-white shadow transition hover:bg-blue-700"
                @click="triggerFileInput"
              >
                上传视频
              </button>
            </div>
            <div
              class="flex items-center justify-center max-md:flex-col md:flex-row"
            >
              <div
                class="text-l mb-1 flex flex-col font-semibold text-gray-500 max-md:flex-row md:mr-14"
              >
                <span>没有视频？</span><span> 试试这些视频：</span>
              </div>
              <div class="flex flex-row items-center justify-center gap-2">
                <div
                  v-for="video in sampleVideos"
                  :key="video.name"
                  class="flex cursor-pointer flex-row items-center justify-center transition-opacity hover:opacity-80"
                  @click="handleSampleVideo(video.src)"
                >
                  <video
                    class="h-18 rounded-l"
                    :src="video.src"
                    :alt="video.name"
                    :poster="video.post"
                    preload="metadata"
                    muted
                  />
                </div>
              </div>
            </div>
            <div class="text-[0.6rem]">
              上传视频或网址，即代表您同意我们的服务条款。 要了解有关 AI视频剪辑
              如何处理您的个人数据的更多信息，请查看我们的隐私政策。
            </div>
            <svg
              v-if="!getIsMobile"
              width="120"
              height="120"
              viewBox="0 0 120 120"
              fill="none"
              class="-right-40% -top-8rem absolute top-0 block"
              xmlns="http://www.w3.org/2000/svg"
            >
              <!-- 手绘线条 -->
              <path
                d="M20 20 Q40 30 30 50 Q20 70 60 80 Q100 90 80 110"
                stroke="#74D8FF"
                stroke-width="7"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <!-- 三角形箭头 -->
              <polygon points="15,100 35,105 25,115" fill="#74D8FF" />
            </svg>
          </div>
        </div>
        <svg
          v-if="!getIsMobile"
          width="120"
          height="120"
          viewBox="0 0 120 120"
          fill="none"
          class="absolute -bottom-8 left-32 block"
          xmlns="http://www.w3.org/2000/svg"
        >
          <!-- 波浪线条 -->
          <path
            d="M100 20 Q80 40 100 60 Q120 80 60 100 Q20 110 20 80 Q20 50 60 40"
            stroke="#74D8FF"
            stroke-width="7"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <!-- 三角形箭头 -->
          <polygon points="105,15 120,30 100,35" fill="#74D8FF" />
        </svg>
      </div>

      <!-- 惊艳的质量模块 -->
      <div
        class="md-py-12 relative my-4 flex w-full flex-col items-center max-md:px-4"
      >
        <!-- 黄色装饰 -->
        <div v-if="!getIsMobile" class="absolute right-32 top-8 h-10 w-10">
          <svg v-if="!getIsMobile" width="40" height="40">
            <path
              d="M10,10 Q30,20 20,30"
              stroke="#74D8FF"
              stroke-width="5"
              fill="none"
            />
          </svg>
        </div>
        <div v-if="!getIsMobile" class="absolute bottom-8 left-32 h-8 w-8">
          <svg width="32" height="32">
            <circle
              cx="16"
              cy="16"
              r="10"
              stroke="#74D8FF"
              stroke-width="4"
              fill="none"
            />
          </svg>
        </div>
        <!-- 标题 -->
        <div
          class="mb-6 text-center text-4xl font-bold text-gray-800 md:text-5xl"
        >
          惊艳的效果
        </div>
        <div class="mb-4 text-center text-xl text-gray-600">
          视频从一个半小时到二十分钟，没有枯燥的捡球、特写和回放，发球和比赛细节全部保留
          !
        </div>
        <!-- 视频轮播展示区 -->
        <div
          class="relative flex w-full max-w-6xl items-center justify-center px-4"
        >
          <!-- <VideoCarousel
            v-if="carouselVideos.length > 0"
            :videoList="carouselVideos"
            :autoplay="true"
            :autoplayDelay="4000"
            :infinite="true"
            :showNavigation="true"
            :showIndicators="true"
            effect="coverflow"
            @change="onVideoChange"
            class="w-full h-full"
          /> -->
          <!-- 备用显示 -->
          <PauseAfterPlayVideo
            class="relative z-10 w-full rounded-2xl object-contain shadow-lg"
            :sources="sources"
            :autoplay="false"
            :controls="true"
            :muted="true"
            :loop="false"
            :preload="'metadata'"
            :responsive="true"
            :fluid="true"
            :playbackRates="[0.5, 1, 1.25, 1.5, 2]"
            :language="'zh-CN'"
            :url="sources[0].src"
            :useVideoPlayer="true"
          />
        </div>
        <!-- 查看更多 -->
        <div class="mt-6">
          <a href="#" class="font-bold text-blue-600 hover:underline"
            >查看更多样品 &rarr;</a
          >
        </div>
      </div>

      <!-- 新增宣传模块 -->
      <div
        class="md-py-24 my-8 flex items-center justify-center px-8 max-md:flex-col"
      >
        <!-- 左侧文字 -->
        <div class="my-4 max-w-xl text-left">
          <h2 class="mb-4 text-4xl font-bold text-gray-700">
            轻点一下 即可自动剪掉休息片段
          </h2>
          <p class="mb-2 text-gray-600">
            一场国际wtt比赛直播至少需要一个半小时。
          </p>
          <p class="mb-2 text-gray-600">
            凭借restcut，可以将视频时间缩短到20分钟，预计可以减少您至少两小时的剪辑时间。
          </p>
          <p class="text-gray-600">
            该20分钟涵盖比赛精华，无论是回放提升，还是精彩重看，
            都可以让您开心练球，迅速涨分、放松身心，享受乒乓。
          </p>
        </div>
        <!-- 右侧图片 -->
        <div class="relative max-md:my-8 md:ml-12">
          <img
            src="/src/assets/bg4.png"
            alt="手势"
            class="rounded-2rem relative z-10 h-72 w-72 object-contain"
          />
          <!-- 装饰图形（可选） -->
          <div
            class="absolute left-8 top-8 -z-10 h-40 w-40 rounded-full bg-cyan-100"
          ></div>
          <div
            class="absolute bottom-4 right-4 -z-10 h-16 w-16 rounded-full bg-yellow-200"
          ></div>
        </div>
      </div>
      <!-- 生产级代码模块 -->
      <div
        class="md-py-24 my-12 flex items-center justify-center max-md:flex-col max-md:flex-col-reverse max-md:px-8"
      >
        <!-- 左侧图形 -->
        <div class="flex flex-col items-center max-md:my-8 md:mr-16">
          <img
            src="/src/assets/bg3.png"
            alt="logo"
            class="rounded-2rem mb-4 h-72"
          />
        </div>
        <!-- 右侧内容 -->
        <div class="max-w-2xl flex-1 max-md:text-center">
          <h2 class="mb-4 text-4xl font-bold text-gray-800">
            提升效率, 告别枯燥
          </h2>
          <p class="mb-8 text-xl text-gray-500">全程无需参与，准确率比人工高</p>
          <div
            class="grid grid-cols-2 gap-x-8 gap-y-4 max-md:grid-cols-1 max-md:text-center"
          >
            <div
              v-for="feature in features"
              :key="feature.title"
              class="flex flex-col items-start rounded-xl p-2 max-md:items-center max-md:border-2"
            >
              <div class="mb-1 flex items-center">
                <div :class="[feature.icon, 'mr-2']" />
                <div class="text-lg font-bold text-gray-800">
                  {{ feature.title }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-500">
                  {{ feature.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </DragDropUpload>
  </AuroraBackground>
</template>

<script setup lang="ts">
import {
  FAN_ZHEN_DONG_VS_MA_LONG,
  getPingPongRandomSortVideos,
} from '@/assets/videos/urls';
import DragDropUpload from '@/components/DragDropUpload/index.vue';
import { VideoSource } from '@/components/VideoPlayer/types';
import { useAppInject } from '@/hooks/web/useAppInject';
import { getVideoMetadataFromUrl, VideoInfo } from '@/utils/videoUtils';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { AuroraBackground } from '@/components/inspira';

const { push } = useRouter();
const { getIsMobile } = useAppInject();
const fileInput = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);

const sources = ref<VideoSource[]>([
  {
    type: 'video/mp4',
    src: FAN_ZHEN_DONG_VS_MA_LONG.src,
  },
]);

const sampleVideos = getPingPongRandomSortVideos(2);

interface Feature {
  title: string;
  description: string;
  icon: string;
}

const features = ref<Feature[]>([
  {
    title: '离线自动',
    description: '视频剪辑全程在后台进行，无需参与',
    icon: 'i-my-icons-automate2 w-1.4rem h-1.4rem',
  },
  {
    title: '可定制化',
    description:
      '支持将捡球、休息、特写、转场等片段移除，支持自定义发球捡球阶段时长',
    icon: 'i-my-icons-customize w-1.5rem h-1.5rem',
  },
  {
    title: '多种场景',
    description:
      '无论单打还是双打，业余还是专业、俯瞰视角还是近景，都能智能识别',
    icon: 'i-my-icons-mutiscene w-1.5rem h-1.5rem',
  },
  {
    title: '准确率高',
    description: '准确率目前高达95%',
    icon: 'i-my-icons-exactly w-1.4rem h-1.4rem',
  },
]);

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    handleFile(Array.from(input.files));
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    handleFile(Array.from(files));
  }
};

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
};

const handleFile = async (files: File[]) => {
  const file = files[0];
  // 检查文件类型
  const isVideo = file.type === 'video/mp4' || file.type === 'video/webm';
  if (!isVideo) {
    alert('请上传视频文件');
    return;
  }

  // 检查文件大小（2000MB）
  const maxSize = 2000 * 1024 * 1024;
  if (file.size > maxSize) {
    alert('视频大小不能超过2000MB');
    return;
  }

  const url = URL.createObjectURL(file);
  const videoInfo: VideoInfo = {
    url: url,
    name: file.name,
    type: file.type,
    size: file.size,
    urlType: 'local',
  };
  // 跳转到视频上传页面
  push({
    path: '/auto_clip/ping_pong',
    query: {
      videoInfo: JSON.stringify(videoInfo),
    },
  });
};

const handleSampleVideo = async (videoPath: string) => {
  try {
    const videoInfo = await getVideoMetadataFromUrl(videoPath);
    videoInfo.urlType = 'remote';
    // 跳转到视频上传页面
    await push({
      path: '/auto_clip/ping_pong',
      query: {
        videoInfo: JSON.stringify(videoInfo),
      },
    });
  } catch (error) {
    console.error('Error loading sample video:', error);
  }
};
</script>

<style></style>
