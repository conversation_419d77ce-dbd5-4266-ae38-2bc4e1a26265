import { useProjectStore as usePiniaStore } from '@/store/editor';
import { useProjectStore as useZustandStore } from '@/store/editor/react/projectStore';

// 创建状态同步函数
export const syncVueToReact = () => {
  const piniaStore = usePiniaStore();
  const zustandStore = useZustandStore.getState();

  // 监听Pinia store变化并同步到Zustand
  piniaStore.$subscribe((_mutation, state) => {
    zustandStore.setMediaFiles(state.mediaFiles);
    zustandStore.setTextElements(state.textElements);
    zustandStore.setCurrentTime(state.currentTime);
    zustandStore.setIsPlaying(state.isPlaying);
    zustandStore.setIsMuted(state.isMuted);
    zustandStore.setDuration(state.duration);
  });
};

export const syncReactToVue = () => {
  const piniaStore = usePiniaStore();

  // 订阅Zustand store变化
  useZustandStore.subscribe((state, prevState) => {
    if (state.mediaFiles !== prevState.mediaFiles) {
      piniaStore.setMediaFiles(state.mediaFiles);
    }
    if (state.currentTime !== prevState.currentTime) {
      piniaStore.setCurrentTime(state.currentTime);
    }
    if (state.isPlaying !== prevState.isPlaying) {
      piniaStore.setIsPlaying(state.isPlaying);
    }
    if (state.isMuted !== prevState.isMuted) {
      piniaStore.setIsMuted(state.isMuted);
    }
  });
};
