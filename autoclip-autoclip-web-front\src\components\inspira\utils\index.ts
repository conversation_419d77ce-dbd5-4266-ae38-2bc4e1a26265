// Inspira UI 工具函数

// 组件错误处理
export interface ComponentErrorInfo {
  componentName: string
  error: Error
  timestamp: number
}

// 错误监控
export const componentErrorLogger = {
  errors: [] as ComponentErrorInfo[],
  
  logError(componentName: string, error: Error) {
    const errorInfo: ComponentErrorInfo = {
      componentName,
      error,
      timestamp: Date.now()
    }
    this.errors.push(errorInfo)
    console.warn(`[Inspira UI] Component error in ${componentName}:`, error)
  },
  
  getErrors() {
    return this.errors
  },
  
  clearErrors() {
    this.errors = []
  }
}

// 样式工具函数
export const cn = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ')
}

// 动画工具函数
export const createAnimationConfig = (
  duration: number = 300,
  easing: string = 'ease-out',
  delay: number = 0
) => ({
  duration,
  easing,
  delay,
  enabled: true
})