/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppDarkModeToggle: typeof import('./../components/Application/src/AppDarkModeToggle.vue')['default']
    AppLocalePicker: typeof import('./../components/Application/src/AppLocalePicker.vue')['default']
    AppLogo: typeof import('./../components/Application/src/AppLogo.vue')['default']
    AppProvider: typeof import('./../components/Application/src/AppProvider.vue')['default']
    ButtonTest: typeof import('./../components/inspira/Button/ButtonTest.vue')['default']
    ChangePasswordForm: typeof import('./../components/Login/src/RegisterForm/ChangePasswordForm.vue')['default']
    CheckboxOptions: typeof import('./../components/Form/src/components/CheckboxOptions.vue')['default']
    CopperModal: typeof import('./../components/Cropper/src/CopperModal.vue')['default']
    CoverflowCarousel: typeof import('./../components/VideoCarousel/CoverflowCarousel.vue')['default']
    Cropper: typeof import('./../components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./../components/Cropper/src/CropperAvatar.vue')['default']
    Dialog: typeof import('./../components/Dialog/src/Dialog.vue')['default']
    DragDropUpload: typeof import('./../components/DragDropUpload/index.vue')['default']
    Dropdown: typeof import('./../components/Dropdown/src/Dropdown.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ErrorBoundary: typeof import('./../components/inspira/Utils/ErrorBoundary.vue')['default']
    ForgePassword: typeof import('./../components/Login/src/ForgePassword.vue')['default']
    Form: typeof import('./../components/Form/src/Form.vue')['default']
    FormItem: typeof import('./../components/Form/src/FormItem.vue')['default']
    Icon: typeof import('./../components/Icon/Icon.vue')['default']
    IdentifierRegisterForm: typeof import('./../components/Login/src/RegisterForm/IdentifierRegisterForm.vue')['default']
    ImageViewer: typeof import('./../components/ImageViewer/src/ImageViewer.vue')['default']
    InputPassword: typeof import('./../components/InputPassword/src/InputPassword.vue')['default']
    InputTest: typeof import('./../components/inspira/Input/InputTest.vue')['default']
    InspiraButton: typeof import('./../components/inspira/Button/InspiraButton.vue')['default']
    InspiraCheckbox: typeof import('./../components/inspira/Checkbox/InspiraCheckbox.vue')['default']
    InspiraForm: typeof import('./../components/inspira/Form/InspiraForm.vue')['default']
    InspiraInput: typeof import('./../components/inspira/Input/InspiraInput.vue')['default']
    InspiraRadio: typeof import('./../components/inspira/Radio/InspiraRadio.vue')['default']
    InspiraRadioGroup: typeof import('./../components/inspira/Radio/InspiraRadioGroup.vue')['default']
    InternalVideoPlayer: typeof import('./../components/VideoPlayer/InternalVideoPlayer.vue')['default']
    Loading: typeof import('./../components/Loading/src/Loading.vue')['default']
    Login: typeof import('./../components/Login/src/Login.vue')['default']
    LoginForm: typeof import('./../components/Login/src/LoginForm.vue')['default']
    LoginFormInputMigrated: typeof import('./../components/Login/src/LoginFormInputMigrated.vue')['default']
    LoginFormMigrated: typeof import('./../components/Login/src/LoginFormMigrated.vue')['default']
    LoginFormTest: typeof import('./../components/inspira/Button/LoginFormTest.vue')['default']
    PauseAfterPlayVideo: typeof import('./../components/PauseAfterPlayVideo/index.vue')['default']
    RadioOptions: typeof import('./../components/Form/src/components/RadioOptions.vue')['default']
    RainbowButton: typeof import('./../components/inspira/Button/RainbowButton.vue')['default']
    RegisterForm: typeof import('./../components/Login/src/RegisterForm/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectOptions: typeof import('./../components/Form/src/components/SelectOptions.vue')['default']
    SimpleCarousel: typeof import('./../components/VideoCarousel/SimpleCarousel.vue')['default']
    VideoCarousel: typeof import('./../components/VideoCarousel/index.vue')['default']
    VideoInfoDialog: typeof import('./../components/VideoInfoDialog/index.vue')['default']
    VideoPlayer: typeof import('./../components/VideoPlayer/index.vue')['default']
    XButton: typeof import('./../components/XButton/src/XButton.vue')['default']
    XTextButton: typeof import('./../components/XButton/src/XTextButton.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
