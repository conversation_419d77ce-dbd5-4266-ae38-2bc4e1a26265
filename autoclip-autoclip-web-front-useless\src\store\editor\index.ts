'use client';
import { useMessage } from '@/hooks/web/useMessage';
import { openDB } from 'idb';
import { useProjectStore } from './slices/projectStore';
import { useProjectsStore } from './slices/projectsStore';
import { ProjectState } from '@/types/editor';

const { $message } = useMessage();

// Create IndexedDB database for files and projects
const setupDB = async () => {
  if (typeof window === 'undefined') return null;
  const db = await openDB('clipjs-files', 1, {
    upgrade(db) {
      db.createObjectStore('files', { keyPath: 'id' });
      db.createObjectStore('projects', { keyPath: 'id' });
    },
  });
  return db;
};

// File storage functions
export const storeFile = async (file: File, fileId: string) => {
  if (typeof window === 'undefined') return null;
  try {
    const db = await setupDB();
    if (!db) return null;

    const fileData = {
      id: fileId,
      file: file,
    };

    await db.put('files', fileData);
    return fileId;
  } catch (error) {
    $message.error('Error storing file');
    console.error('Error storing file:', error);
    return null;
  }
};

export const getFile = async (fileId: string): Promise<File | null> => {
  if (typeof window === 'undefined') return null;
  try {
    const db = await setupDB();
    if (!db) return null;

    const fileData = await db.get('files', fileId);
    if (!fileData) return null;

    return fileData.file;
  } catch (error) {
    $message.error('Error retrieving file');
    console.error('Error retrieving file:', error);
    return null;
  }
};

export const deleteFile = async (fileId: string) => {
  if (typeof window === 'undefined') return;
  try {
    const db = await setupDB();
    if (!db) return;
    await db.delete('files', fileId);
  } catch (error) {
    $message.error('Error deleting file');
    console.error('Error deleting file:', error);
  }
};

export const listFiles = async () => {
  if (typeof window === 'undefined') return [];
  try {
    const db = await setupDB();
    if (!db) return [];
    return await db.getAll('files');
  } catch (error) {
    $message.error('Error listing files');
    console.error('Error listing files:', error);
    return [];
  }
};

// Project storage functions
export const storeProject = async (project: ProjectState) => {
  if (typeof window === 'undefined') return null;
  try {
    const db = await setupDB();

    if (!db) return null;
    if (!project.id || !project.projectName) {
      return null;
    }

    await db.put('projects', project);

    return project.id;
  } catch (error) {
    $message.error('Error storing project');
    console.error('Error storing project:', error);
    return null;
  }
};

export const getProject = async (projectId: string) => {
  if (typeof window === 'undefined') return null;
  try {
    const db = await setupDB();
    if (!db) return null;
    return await db.get('projects', projectId);
  } catch (error) {
    $message.error('Error retrieving project');
    console.error('Error retrieving project:', error);
    return null;
  }
};

export const deleteProject = async (projectId: string) => {
  if (typeof window === 'undefined') return;
  try {
    const db = await setupDB();
    if (!db) return;
    await db.delete('projects', projectId);
  } catch (error) {
    $message.error('Error deleting project');
    console.error('Error deleting project:', error);
  }
};

export const listProjects = async () => {
  if (typeof window === 'undefined') return [];
  try {
    const db = await setupDB();
    if (!db) return [];
    return await db.getAll('projects');
  } catch (error) {
    console.error('Error listing projects:', error);
    return [];
  }
};

export { useProjectsStore, useProjectStore };
