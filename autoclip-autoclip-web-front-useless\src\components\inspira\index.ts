// Inspira UI 组件导出文件
// 这里集中导出所有 Inspira UI 组件的封装

// 基础组件
export { default as InspiraButton } from './Button/InspiraButton.vue';
export { default as InspiraInput } from './Input/InspiraInput.vue';

// 背景特效组件
export { default as AuroraBackground } from './Background/AuroraBackground.vue';
export { default as FallingStarsBg } from './Background/FallingStarsBg.vue';
export { default as Vortex } from './Background/Vortex.vue';

// 动画组件
export { default as FlipWords } from './Animation/FlipWords.vue';
export { default as TextHighlight } from './Animation/TextHighlight.vue';
export { default as AnimatedTooltip } from './Animation/AnimatedTooltip.vue';
export { default as BlurReveal } from './Animation/BlurReveal.vue';
export { default as BorderBeam } from './Animation/BorderBeam.vue';

// 工具组件
export { default as ErrorBoundary } from './Utils/ErrorBoundary.vue';

// 类型定义
export type { InspiraButtonProps } from './Button/types';
export type { InspiraInputProps } from './Input/types';
export type { ErrorBoundaryProps } from './Utils/types';