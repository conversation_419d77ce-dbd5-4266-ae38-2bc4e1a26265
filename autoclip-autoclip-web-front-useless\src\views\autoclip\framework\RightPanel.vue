<template>
  <div
    class="relative m-4 flex flex-1 flex-col items-center justify-center pb-10 max-md:px-4 md:rounded-3xl md:bg-white md:px-32 md:shadow-lg"
    id="output"
  >
    <processing-overlay
      :is-processing="isProcessing"
      :show-notification="true"
      :progress-info="progressInfo"
    />
    <transition :name="getBasicTransition" mode="out-in" appear>
      <div
        class="flex w-full flex-col items-center justify-center"
        v-if="previewVideoUrl"
      >
        <h2 v-if="getIsMobile" class="text-l mb-2 font-bold text-gray-600">
          输出视频
        </h2>
        <pause-after-play-video
          :url="previewVideoUrl"
          class="w-full border border-gray-200"
          muted
          preload="metadata"
          :autoplay="false"
          :use-video-player="true"
          :playsinline="true"
          :controls="true"
        ></pause-after-play-video>
      </div>
      <div v-else class="w-full max-md:py-4 max-md:text-center">
        <div class="mb-12 flex items-center justify-between">
          <div>
            <h1 class="mb-4 text-4xl font-bold text-[#222]">
              {{ title }}
            </h1>
            <h2 class="text-lg text-gray-600">
              上传你收集的视频，自动裁剪掉休息的片段。<br />
              <span class="text-base text-gray-400"
                >视频处理期间可离开页面，视频处理完后将会通过消息通知您。</span
              >
            </h2>

            <div class="mt-2 text-sm font-medium text-orange-500">
              视频的角度、大小格式、分辨率目前很容易影响裁剪效果，最好水平放置，且不要压缩得太小。
            </div>
          </div>
        </div>

        <div
          class="mb-10 flex w-full cursor-pointer flex-col items-center justify-center rounded-2xl border-2 border-dashed border-gray-200 bg-[#fafbfc] py-16 transition hover:border-blue-400"
          @click="handleFileClick"
        >
          <div class="flex flex-col items-center">
            <svg
              class="mb-4"
              width="56"
              height="56"
              fill="none"
              viewBox="0 0 56 56"
            >
              <rect width="56" height="56" rx="14" fill="#f0f1f5" />
              <path
                d="M28 16v24M16 28h24"
                stroke="#6c63ff"
                stroke-width="2.5"
                stroke-linecap="round"
              />
            </svg>
            <div class="mb-2 text-2xl font-semibold text-gray-700">
              选择视频
            </div>
            <div class="mb-3 text-base text-gray-500">
              快速上传：拖放视频或按
              <span class="rounded bg-gray-100 px-1 font-mono">Ctrl + V</span>
            </div>
          </div>
        </div>
        <div
          class="md:gap-8xl mt-6 flex w-full items-center gap-4 md:justify-between"
        >
          <div class="flex flex-col text-start font-bold max-md:text-sm">
            <span class="text-gray-600"> 没有视频？</span>
            <span class="font-bold text-gray-600"> 试试这些视频： </span>
          </div>
          <div class="flex gap-3">
            <video
              v-for="(video, idx) in exampleVideos"
              :key="idx"
              :src="video.src"
              :poster="video.post"
              @click="handleFileSelected(video.src)"
              class="h-14 cursor-pointer rounded-lg border border-gray-200 object-cover"
              preload="metadata"
              muted
              loop
            ></video>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>
<script setup lang="ts">
import {
  ProcessStatus,
  VideoProcessProgressVO,
} from '@/api/autoclip/auto_clip';
import { RemoteVideoInfo } from '@/assets/videos/urls';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';
import { useAppInject } from '@/hooks/web/useAppInject';
import { getVideoMetadata, VideoInfo } from '@/utils/videoUtils';
import ProcessingOverlay from '@/views/autoclip/framework/components/ProcessingOverlay.vue';
import { ProgressInfo } from '@/views/autoclip/framework/components/types';
import { ref } from 'vue';

const { getIsMobile } = useAppInject();
const { getBasicTransition } = useTransitionSetting();

const props = withDefaults(
  defineProps<{
    exampleVideos: RemoteVideoInfo[];
    inputPreviewVideoUrl?: string;
    outputPreviewVideoUrl?: string;
    isProcessing?: boolean;
    processingProgress?: VideoProcessProgressVO;
    title: string;
  }>(),
  {
    isProcessing: false,
    processingProgress: undefined,
  },
);

const progressInfo: ComputedRef<ProgressInfo | undefined> = computed(() => {
  return {
    progress: Math.round((props.processingProgress?.progress ?? 0) * 100),
    videoDuration: props.processingProgress?.videoDuration ?? 0,
    position: props.processingProgress?.position ?? -1,
    processSpeed: props.processingProgress?.processSpeed ?? 0,
    estimatedRemainingTime:
      props.processingProgress?.estimatedRemainingTime ?? 0,
    processedTime: props.processingProgress?.processedTime ?? 0,
    status: props.processingProgress?.status ?? ProcessStatus.PREPARING,
  };
});

const videoRef = ref<HTMLVideoElement | null>(null);
watch(
  () => props.inputPreviewVideoUrl,
  () => {
    videoRef.value?.load();
  },
);

const previewVideoUrl = computed(() => {
  if (props.isProcessing) {
    return props.inputPreviewVideoUrl;
  } else {
    return props.outputPreviewVideoUrl;
  }
});

const jumpToInput = () => {
  const uploadElement = document.getElementById('input');
  if (uploadElement) {
    uploadElement.scrollIntoView({ behavior: 'smooth' });
  }
};

const emit = defineEmits<{
  (e: 'file-select', video: File[] | VideoInfo): void;
}>();

const handleFileSelected = async (video: File[] | string) => {
  if (typeof video === 'string') {
    const videoElement = document.querySelector(
      `video[src="${video}"]`,
    ) as HTMLVideoElement;
    if (videoElement) {
      const videoInfo: VideoInfo = await getVideoMetadata(videoElement, video);
      emit('file-select', videoInfo);
    } else {
      // 如果找不到视频元素，发送基本信息
      const videoInfo: VideoInfo = {
        url: video,
        name: video.split('/').pop() || 'example-video.mp4',
        type: 'video/mp4',
      };
      emit('file-select', videoInfo);
    }
  } else {
    emit('file-select', video);
  }
  jumpToInput();
};

const handleFileClick = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'video/mp4,video/webm';
  input.onchange = (e) => {
    const files = (e.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      const fileList: File[] = [];
      for (let i = 0; i < files.length; i++) {
        fileList.push(files[i] as File);
      }
      handleFileSelected(fileList);
    }
  };
  input.click();
};
</script>

<style scoped lang="scss"></style>
