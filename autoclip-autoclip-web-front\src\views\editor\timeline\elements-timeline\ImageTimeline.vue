<template>
  <div>
    <div v-for="clip in imageClips" :key="clip.id">
      <div
        :ref="(el) => setTargetRef(clip.id, el)"
        @click="handleClick('media', clip.id)"
        :class="[
          'absolute top-2 flex h-12 cursor-pointer items-center justify-center rounded-md border border-gray-500 border-opacity-50 bg-[#27272A] text-sm text-white',
          {
            'border-blue-500 bg-[#3F3F46]':
              activeElement === 'media' &&
              mediaFiles[activeElementIndex]?.id === clip.id,
          },
        ]"
        :style="{
          left: `${clip.positionStart * timelineZoom}px`,
          width: `${(clip.positionEnd - clip.positionStart) * timelineZoom}px`,
          zIndex: clip.zIndex,
        }"
      >
        <el-image
          class="mr-2 h-7 w-7 min-w-6 flex-shrink-0"
          :src="'https://www.svgrepo.com/show/535454/image.svg'"
          alt="Image"
        />
        <span class="text-x truncate">{{ clip.fileName }}</span>
      </div>

      <Moveable
        v-if="targetRefs[clip.id]"
        :ref="(el) => setMoveableRef(clip.id, el)"
        :target="targetRefs[clip.id]"
        :container="null"
        :renderDirections="
          activeElement === 'media' &&
          mediaFiles[activeElementIndex]?.id === clip.id
            ? ['w', 'e']
            : []
        "
        :draggable="true"
        :throttleDrag="0"
        :rotatable="false"
        :resizable="true"
        :throttleResize="0"
        :class="{
          'moveable-control-box-hidden': !(
            activeElement === 'media' &&
            mediaFiles[activeElementIndex]?.id === clip.id
          ),
        }"
        @dragStart="onDragStart"
        @drag="onDrag($event, clip)"
        @dragEnd="onDragEnd"
        @resizeStart="onResizeStart"
        @resize="onResize($event, clip)"
        @resizeEnd="onResizeEnd"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { useProjectStore } from '@/store/editor';
import { throttle } from 'lodash-es';
import Moveable from 'vue3-moveable';
import type { MediaFile } from '@/types/editor';

const projectStore = useProjectStore();
const targetRefs = ref<Record<string, HTMLElement>>({});
const moveableRefs = ref<Record<string, any>>({});
const mediaFilesRef = ref<MediaFile[]>([]);

const setTargetRef = (
  id: string,
  el: Element | ComponentPublicInstance | null,
) => {
  if (el instanceof HTMLElement) {
    targetRefs.value[id] = el;
  } else if (el === null) {
    delete targetRefs.value[id];
  }
};

const setMoveableRef = (id: string, el: any) => {
  if (el) {
    moveableRefs.value[id] = el;
  } else {
    delete moveableRefs.value[id];
  }
};

const mediaFiles = computed(() => projectStore.getMediaFiles);
const activeElement = computed(() => projectStore.getActiveElement);
const activeElementIndex = computed(() => projectStore.getActiveElementIndex);
const timelineZoom = computed(() => projectStore.getTimelineZoom);

const imageClips = computed(() =>
  mediaFiles.value.filter((clip) => clip.type === 'image'),
);

// Keep a ref for performance optimization
watch(
  mediaFiles,
  (newFiles) => {
    mediaFilesRef.value = newFiles;
  },
  { immediate: true },
);

// Clean up refs when image clips change
watch(
  imageClips,
  async (newClips) => {
    await nextTick();

    const currentIds = new Set(newClips.map((clip) => clip.id));

    Object.keys(targetRefs.value).forEach((id) => {
      if (!currentIds.has(id)) {
        delete targetRefs.value[id];
      }
    });

    Object.keys(moveableRefs.value).forEach((id) => {
      if (!currentIds.has(id)) {
        delete moveableRefs.value[id];
      }
    });
  },
  { deep: true },
);

// Throttled update function
const onUpdateMedia = throttle((id: string, updates: Partial<MediaFile>) => {
  projectStore.updateMediaFile(id, updates);
}, 100);

const handleClick = (element: string, index: string) => {
  if (element === 'media') {
    projectStore.setActiveElement('media');
    const actualIndex = mediaFiles.value.findIndex((clip) => clip.id === index);
    projectStore.setActiveElementIndex(actualIndex);
  }
};

const handleDrag = (clip: MediaFile, target: HTMLElement, left: number) => {
  const constrainedLeft = Math.max(left, 0);
  const newPositionStart = constrainedLeft / timelineZoom.value;
  onUpdateMedia(clip.id, {
    positionStart: newPositionStart,
    positionEnd: newPositionStart - clip.positionStart + clip.positionEnd,
    endTime: newPositionStart - clip.positionStart + clip.endTime,
  });

  target.style.left = `${constrainedLeft}px`;
};

const handleRightResize = (clip: MediaFile, _: HTMLElement, width: number) => {
  const newPositionEnd = width / timelineZoom.value;
  onUpdateMedia(clip.id, {
    positionEnd: clip.positionStart + newPositionEnd,
    endTime: clip.positionStart + newPositionEnd,
  });
};

const handleLeftResize = (clip: MediaFile, _: HTMLElement, width: number) => {
  const newPositionEnd = width / timelineZoom.value;
  const constrainedLeft = Math.max(
    clip.positionStart +
      (clip.positionEnd - clip.positionStart - newPositionEnd),
    0,
  );

  onUpdateMedia(clip.id, {
    positionStart: constrainedLeft,
  });
};

// Moveable event handlers
const onDragStart = () => {};

const onDrag = (e: any, clip: MediaFile) => {
  handleClick('media', clip.id);
  handleDrag(clip, e.target, e.left);
};

const onDragEnd = () => {};

const onResizeStart = () => {};

const onResize = (e: any, clip: MediaFile) => {
  if (e.direction[0] === 1) {
    handleClick('media', clip.id);
    e.delta[0] && (e.target.style.width = `${e.width}px`);
    handleRightResize(clip, e.target, e.width);
  } else if (e.direction[0] === -1) {
    handleClick('media', clip.id);
    e.delta[0] && (e.target.style.width = `${e.width}px`);
    handleLeftResize(clip, e.target, e.width);
  }
};

const onResizeEnd = () => {};

// Update moveable rects when zoom changes
watch(timelineZoom, async () => {
  await nextTick();
  for (const clip of mediaFiles.value) {
    const moveableInstance = moveableRefs.value[clip.id];
    if (moveableInstance && moveableInstance.updateRect) {
      moveableInstance.updateRect();
    }
  }
});
</script>

<style scoped>
.moveable-control-box-hidden {
  display: none;
}
</style>
