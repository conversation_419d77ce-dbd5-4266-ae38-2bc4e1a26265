// Inspira UI 样式支持文件

// Aurora Background 动画支持
@keyframes aurora {
  from {
    background-position:
      50% 50%,
      50% 50%;
  }
  to {
    background-position:
      350% 50%,
      350% 50%;
  }
}

// 定义 Aurora 动画变量
:root {
  --animate-aurora: aurora 60s linear infinite;
}

// Text Highlight 背景渐变支持
.text-highlight-gradient {
  background: linear-gradient(120deg, #a855f7 0%, #3b82f6 100%);
}

// 3D Card 透视支持
.card-3d-perspective {
  perspective: 1000px;
}

.card-3d-preserve {
  transform-style: preserve-3d;
}

// 通用动画缓动函数
.inspira-ease-out {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.inspira-ease-in-out {
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1);
}

// 防止样式冲突的命名空间
.inspira-ui {
  // Inspira UI 组件的样式隔离
  --inspira-primary: #6366f1;
  --inspira-secondary: #8b5cf6;
  --inspira-accent: #06b6d4;
  --inspira-background: #ffffff;
  --inspira-surface: #f8fafc;
  --inspira-text: #1e293b;
  
  // 暗色主题支持
  &.dark {
    --inspira-background: #0f172a;
    --inspira-surface: #1e293b;
    --inspira-text: #f1f5f9;
  }
}

// 响应式断点支持
@media (max-width: 768px) {
  .inspira-mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .inspira-desktop-hidden {
    display: none;
  }
}