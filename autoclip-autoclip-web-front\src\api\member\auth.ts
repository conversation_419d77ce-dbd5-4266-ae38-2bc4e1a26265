import { defHttp } from '@/utils/http/axios';

export enum IdentifierType {
  MOBILE = 0,
  MAIL = 1,
}

export interface LoginPasswordParams {
  identifier: string;
  identifierType: IdentifierType;
  password: string;
  // 社交平台的类型，参见 SocialTypeEnum 枚举值
  socialType?: number;
  // 授权码
  socialCode?: string;
}

export type TokenType = {
  id: number; // 编号
  accessToken: string; // 访问令牌
  refreshToken: string; // 刷新令牌
  userId: number; // 用户编号
  userType: number; //用户类型
  clientId: string; //客户端编号
  expiresTime: number; //过期时间
};

export enum SmsSceneEnum {
  // 会员用户 - 手机号登陆
  MEMBER_LOGIN = 1,
  // 会员用户 - 修改手机
  MEMBER_UPDATE_MOBILE = 2,
  // 会员用户 - 修改密码
  MEMBER_UPDATE_PASSWORD = 3,
  // 会员用户 - 忘记密码
  MEMBER_RESET_PASSWORD = 4,
}

export const getIdentifierType = (identifier: string) => {
  if (identifier.includes('@')) {
    return IdentifierType.MAIL;
  } else {
    return IdentifierType.MOBILE;
  }
};

export interface LoginAuthCodeParams {
  identifier: string;
  identifierType: IdentifierType;
  code: string;
  // 社交平台的类型，参见 SocialTypeEnum 枚举值
  socialType?: number;
  // 授权码
  socialCode?: string;
  // 授权 state
  socialState?: string;
}

const AuthUtil = {
  doLogout: () => {
    return defHttp.request<boolean>({
      url: '/member/auth/logout',
      method: 'POST',
    });
  },
  // 使用手机 + 密码登录
  login: (data: LoginPasswordParams) => {
    return defHttp.request<TokenType>({
      url: '/member/auth/login',
      method: 'POST',
      data,
    });
  },
  // 使用手机 + 验证码登录
  authCodeLogin: (data: LoginAuthCodeParams) => {
    return defHttp.request({
      url: '/member/auth/auth-code-login',
      method: 'POST',
      data,
    });
  },
  // 发送手机验证码
  sendAuthCode: (identifier, identifierType, scene) => {
    return defHttp.request({
      url: '/member/auth/send-auth-code',
      method: 'POST',
      data: {
        identifier,
        identifierType,
        scene,
      },
    });
  },
  // 登出系统
  logout: () => {
    return defHttp.request({
      url: '/member/auth/logout',
      method: 'POST',
    });
  },
  // 刷新令牌
  refreshToken: (refreshToken: string) => {
    return defHttp.request({
      url: '/member/auth/refresh-token',
      method: 'POST',
      params: {
        refreshToken: refreshToken,
      },
    });
  },
  // 社交授权的跳转
  socialAuthRedirect: (type, redirectUri) => {
    return defHttp.request({
      url: '/member/auth/social-auth-redirect',
      method: 'GET',
      params: {
        type,
        redirectUri,
      },
    });
  },
  // 社交快捷登录
  socialLogin: (type, code, state) => {
    return defHttp.request({
      url: '/member/auth/social-login',
      method: 'POST',
      data: {
        type,
        code,
        state,
      },
    });
  },
  // 微信小程序的一键登录
  weixinMiniAppLogin: (phoneCode, loginCode, state) => {
    return defHttp.request({
      url: '/member/auth/weixin-mini-app-login',
      method: 'POST',
      data: {
        phoneCode,
        loginCode,
        state,
      },
    });
  },
  // 创建微信 JS SDK 初始化所需的签名
  createWeixinMpJsapiSignature: (url) => {
    return defHttp.request({
      url: '/member/auth/create-weixin-jsapi-signature',
      method: 'POST',
      params: {
        url,
      },
    });
  },
  //
};

export default AuthUtil;
