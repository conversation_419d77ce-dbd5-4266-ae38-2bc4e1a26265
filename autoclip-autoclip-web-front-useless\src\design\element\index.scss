@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $upload: (
    'dragger-padding-horizontal': 0px,
    'dragger-padding-vertical': 0px,
  ),
  $menu: (
    'active-color': transparent,
    'hover-text-color': inherit,
    'bg-color': transparent,
    'hover-bg-color': inherit,
    'item-hover-fill': inherit,
    'border-color': inherit,
    'item-font-size': 16px,
  ),
  $footer: (
    'padding': 0,
  ),
  $colors: ()
);

.el-menu--horizontal .el-menu-item {
  width: 100%;
}
.el-popper {
  border-radius: 0.5rem;
}
.el-menu--popup {
  border-radius: 0.5rem;
}

.el-button + .el-button {
  margin-left: 0;
}
// .el-menu-item:hover {
//   border-bottom: 1px solid #409eff;
// }
$--el-upload-dragger-padding-horizontal: 0px;
$--el-upload-dragger-padding-vertical: 0px;

//@use 'element-plus/theme-chalk/src/index.scss' as *;
