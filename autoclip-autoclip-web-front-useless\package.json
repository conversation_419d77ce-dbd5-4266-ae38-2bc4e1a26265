{"name": "auto_clip_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "debug": "vite --debug", "build": "vue-tsc -b && cross-env NODE_ENV=production vite build && esno ./internal/script/postBuild.ts", "preview": "vite preview", "lint:tsc": "vue-tsc --noEmit", "lint:prettier": "vue-tsc || prettier --write  \"src/**/*.{ts, js,json,tsx,css,less,scss,vue,html,md}\""}, "dependencies": {"@ffmpeg/ffmpeg": "*", "@ffmpeg/util": "*", "@remotion/bundler": "^4.0.314", "@remotion/cli": "^4.0.314", "@remotion/media-parser": "^4.0.290", "@remotion/player": "^4.0.314", "@unocss/reset": "^66.1.3", "@vue/runtime-core": "^3.5.14", "@vue/shared": "^3.5.14", "@vueuse/core": "^13.2.0", "@zxcvbn-ts/core": "^3.0.4", "axios": "^1.6.8", "clsx": "^2.1.1", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "element-plus": "^2.9.10", "esno": "^4.8.0", "idb": "^8.0.3", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "motion-v": "^1.5.0", "nprogress": "^0.2.0", "pinia": "^3.0.2", "qr-code-styling": "^1.9.2", "qs": "^6.14.0", "radix-vue": "^1.9.17", "react": "^19.1.0", "react-dom": "^19.1.0", "remotion": "^4.0.314", "resize-observer-polyfill": "^1.5.1", "simplex-noise": "^4.0.3", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1", "vue3-moveable": "^0.28.0", "zustand": "^5.0.5"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/json": "^2.2.345", "@iconify/utils": "^2.3.0", "@iconify/vue": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/qs": "^6.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@unocss/preset-icons": "^66.1.3", "@unocss/preset-wind4": "^66.1.3", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.5.2", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/compiler-sfc": "^3.5.16", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "inquirer": "^12.6.1", "less": "^4.3.0", "picocolors": "^1.1.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "sass": "^1.89.0", "sass-embedded": "^1.89.0", "tailwind-merge": "^3.3.1", "typescript": "~5.8.3", "unocss": "^66.1.3", "unplugin-auto-import": "^19.2.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.5.0", "video.js": "^8.22.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-imagemin": "^0.4.0", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-purge-icons": "^0.2.2", "vite-plugin-pwa": "^1.0.0", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.2.8", "vue-types": "^6.0.0"}}