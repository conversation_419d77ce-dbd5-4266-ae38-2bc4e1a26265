<!-- 
  登录表单按钮迁移测试
  测试在实际使用场景中的按钮替换效果
-->
<template>
  <div class="login-form-test">
    <div class="login-container">
      <div class="login-form">
        <!-- 关闭按钮测试 -->
        <InspiraButton
          type="text"
          :circle="true"
          icon="i-mdi:close"
          class="close-button"
          @click="handleClose"
        />

        <h2 class="login-title">登录测试</h2>

        <!-- 表单内容 -->
        <div class="form-content">
          <!-- 输入框（暂时保持 Element Plus） -->
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            size="large"
            class="form-input"
          >
            <template #prefix>
              <Icon icon="i-mdi:account" class="text-gray-400" />
            </template>
          </el-input>

          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            class="form-input"
            show-password
          >
            <template #prefix>
              <Icon icon="i-mdi:lock" class="text-gray-400" />
            </template>
          </el-input>

          <!-- 主登录按钮 - 使用 Inspira UI -->
          <InspiraButton
            type="primary"
            size="large"
            :block="true"
            :loading="loading"
            animation="rainbow"
            class="login-button"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </InspiraButton>

          <!-- 验证码按钮测试 -->
          <div class="verification-section">
            <el-input
              v-model="form.code"
              placeholder="请输入验证码"
              size="large"
              class="code-input"
            >
              <template #suffix>
                <InspiraButton
                  type="text"
                  size="small"
                  :disabled="countdown > 0"
                  class="code-button"
                  @click="handleGetCode"
                >
                  {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
                </InspiraButton>
              </template>
            </el-input>
          </div>

          <!-- 社交登录按钮 -->
          <div class="social-login">
            <p class="social-title">或使用其他方式登录</p>
            <div class="social-buttons">
              <InspiraButton
                v-for="item in socialList"
                :key="item.type"
                type="text"
                :circle="true"
                :icon="item.icon"
                class="social-button"
                @click="handleSocialLogin(item.type)"
              />
            </div>
          </div>

          <!-- 对比区域 -->
          <div class="comparison-section">
            <h3>按钮对比</h3>
            <div class="comparison-row">
              <div class="comparison-item">
                <h4>Element Plus</h4>
                <el-button type="primary" size="large">Element 按钮</el-button>
              </div>
              <div class="comparison-item">
                <h4>Inspira UI</h4>
                <InspiraButton type="primary" size="large" animation="rainbow">
                  Inspira 按钮
                </InspiraButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志</h3>
      <ul>
        <li v-for="(event, index) in eventLog" :key="index">
          {{ event }}
        </li>
      </ul>
      <button @click="clearLog" class="clear-log">清空日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Icon } from '@/components/Icon';
import InspiraButton from './InspiraButton.vue';

// 表单数据
const form = reactive({
  username: '',
  password: '',
  code: '',
});

// 状态管理
const loading = ref(false);
const countdown = ref(0);
const eventLog = ref<string[]>([]);

// 社交登录列表
const socialList = [
  { icon: 'i-mdi:wechat', type: 'wechat', name: '微信' },
  { icon: 'i-mdi:qqchat', type: 'qq', name: 'QQ' },
  { icon: 'i-ant-design:alipay-circle-filled', type: 'alipay', name: '支付宝' },
];

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] ${message}`);
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20);
  }
};

// 事件处理
const handleClose = () => {
  addLog('关闭按钮被点击');
};

const handleLogin = async () => {
  addLog('登录按钮被点击');
  loading.value = true;
  
  // 模拟登录请求
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  loading.value = false;
  addLog('登录完成');
};

const handleGetCode = async () => {
  addLog('获取验证码按钮被点击');
  
  // 开始倒计时
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
      addLog('验证码倒计时结束');
    }
  }, 1000);
  
  addLog('验证码发送成功，开始倒计时');
};

const handleSocialLogin = (type: string) => {
  const social = socialList.find(item => item.type === type);
  addLog(`${social?.name || type} 登录按钮被点击`);
};

const clearLog = () => {
  eventLog.value = [];
};
</script>

<style scoped>
.login-form-test {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-form {
  position: relative;
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #6b7280;
}

.login-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  color: #1f2937;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-input {
  height: 48px;
}

.login-button {
  height: 48px;
  font-weight: 600;
  margin-top: 0.5rem;
}

.verification-section {
  margin-top: 1rem;
}

.code-input {
  height: 48px;
}

.code-button {
  font-size: 12px;
  padding: 0 8px;
}

.social-login {
  margin-top: 2rem;
  text-align: center;
}

.social-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 1rem;
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.social-button {
  width: 40px;
  height: 40px;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  color: #6b7280;
  font-size: 20px;
}

.social-button:hover {
  border-color: var(--inspira-primary);
  color: var(--inspira-primary);
}

.comparison-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.comparison-section h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.comparison-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.comparison-item {
  text-align: center;
}

.comparison-item h4 {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.event-log {
  width: 300px;
  background: white;
  padding: 1rem;
  overflow-y: auto;
}

.event-log h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.event-log ul {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.event-log li {
  padding: 0.25rem 0;
  border-bottom: 1px solid #f3f4f6;
  color: #4b5563;
}

.clear-log {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.75rem;
}

.clear-log:hover {
  background: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form-test {
    flex-direction: column;
  }
  
  .event-log {
    width: 100%;
    max-height: 200px;
  }
  
  .comparison-row {
    grid-template-columns: 1fr;
  }
}
</style>