/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppDarkModeToggle: typeof import('./src/components/Application/src/AppDarkModeToggle.vue')['default']
    AppLocalePicker: typeof import('./src/components/Application/src/AppLocalePicker.vue')['default']
    AppLogo: typeof import('./src/components/Application/src/AppLogo.vue')['default']
    AppProvider: typeof import('./src/components/Application/src/AppProvider.vue')['default']
    ChangePasswordForm: typeof import('./src/components/Login/src/RegisterForm/ChangePasswordForm.vue')['default']
    CopperModal: typeof import('./src/components/Cropper/src/CopperModal.vue')['default']
    Cropper: typeof import('./src/components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./src/components/Cropper/src/CropperAvatar.vue')['default']
    Dialog: typeof import('./src/components/Dialog/src/Dialog.vue')['default']
    Dropdown: typeof import('./src/components/Dropdown/src/Dropdown.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ForgePassword: typeof import('./src/components/Login/src/ForgePassword.vue')['default']
    Form: typeof import('./src/components/Form/src/Form.vue')['default']
    Icon: typeof import('./src/components/Icon/Icon.vue')['default']
    ImageViewer: typeof import('./src/components/ImageViewer/src/ImageViewer.vue')['default']
    InputPassword: typeof import('./src/components/InputPassword/src/InputPassword.vue')['default']
    Loading: typeof import('./src/components/Loading/src/Loading.vue')['default']
    Login: typeof import('./src/components/Login/src/Login.vue')['default']
    LoginForm: typeof import('./src/components/Login/src/LoginForm.vue')['default']
    PhoneRegisterForm: typeof import('./src/components/Login/src/RegisterForm/PhoneRegisterForm.vue')['default']
    RegisterForm: typeof import('./src/components/Login/src/RegisterForm/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VideoPlayer: typeof import('./src/components/VideoPlayer/index.vue')['default']
    XButton: typeof import('./src/components/XButton/src/XButton.vue')['default']
    XTextButton: typeof import('./src/components/XButton/src/XTextButton.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
