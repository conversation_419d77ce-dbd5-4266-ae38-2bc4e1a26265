<template>
  <div class="change-avatar">
    <CropperAvatar
      ref="cropperRef"
      :btnProps="{ preIcon: 'ant-design:cloud-upload-outlined' }"
      :showBtn="false"
      :value="img"
      width="120px"
      @change="handelUpload"
    />
  </div>
</template>
<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes';
import { useUserStore } from '@/store/modules/user';
import { UploadRequestOptions } from 'element-plus/es/components/upload/src/upload';
import UserApi from '@/api/member/user';
import { CropperAvatar } from '@/components/Cropper';
import { useUpload } from '@/hooks/web/useUpload';

defineOptions({ name: 'UserAvatar' });

defineProps({
  img: propTypes.string.def(''),
});

const userStore = useUserStore();

const cropperRef = ref();
const handelUpload = async ({ data }) => {
  const { httpRequest } = useUpload();
  const avatar = (
    (await httpRequest({
      file: data,
      filename: 'avatar.png',
    } as UploadRequestOptions)) as unknown as { data: string }
  ).data;
  await UserApi.updateUser({ avatar });

  // 关闭弹窗，并更新 userStore
  cropperRef.value.close();
  userStore.getUserInfo.avatar = avatar;
};
</script>

<style lang="scss" scoped>
.change-avatar {
  img {
    display: block;
    margin-bottom: 15px;
    border-radius: 50%;
  }
}
</style>
