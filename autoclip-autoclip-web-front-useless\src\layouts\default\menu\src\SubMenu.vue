<template>
  <el-sub-menu :index="menu.path">
    <template #title>
      <div>{{ menu.meta?.title }}</div>
    </template>
    <template v-if="!menu.meta?.hideChildrenInMenu">
      <template v-for="child in menu.children" :key="child.path">
        <sub-menu v-if="child.children" :menu="child" />
        <menu-item
          v-else-if="child"
          :menu="child"
          class="hover:bg-transparent!"
        />
      </template>
    </template>
  </el-sub-menu>
</template>

<script lang="ts" setup>
import type { Menu } from '@/router/types';
import MenuItem from './MenuItem.vue';

defineProps<{
  menu: Menu;
}>();
</script>
