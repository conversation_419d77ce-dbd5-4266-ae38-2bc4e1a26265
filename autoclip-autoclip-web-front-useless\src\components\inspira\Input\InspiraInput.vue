<!-- 
  Inspira UI 输入框组件
  兼容 Element Plus 输入框 API，提供动画增强效果
-->
<template>
  <ErrorBoundary
    fallback-component="el-input"
    :fallback-props="elementPlusProps"
    @error="handleError"
  >
    <div :class="wrapperClass">
      <!-- 前置内容 -->
      <div v-if="$slots.prepend" class="inspira-input__prepend">
        <slot name="prepend" />
      </div>

      <!-- 输入框主体 -->
      <div :class="inputWrapperClass">
        <!-- 前缀图标或内容 -->
        <div v-if="prefixIcon || $slots.prefix" class="inspira-input__prefix">
          <Icon v-if="prefixIcon" :icon="prefixIcon" />
          <slot name="prefix" />
        </div>

        <!-- 输入框 -->
        <input
          ref="inputRef"
          :class="inputClass"
          :type="computedType"
          :value="modelValue"
          :placeholder="placeholder"
          :disabled="disabled"
          :readonly="readonly"
          :maxlength="maxlength"
          :minlength="minlength"
          :autocomplete="autocomplete"
          :autofocus="autofocus"
          :name="name"
          @input="handleInput"
          @change="handleChange"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="handleKeydown"
          @keyup="handleKeyup"
          @keypress="handleKeypress"
        />

        <!-- 后缀图标或内容 -->
        <div v-if="showSuffix" class="inspira-input__suffix">
          <!-- 清空按钮 -->
          <Icon
            v-if="showClear"
            icon="i-mdi:close-circle"
            class="inspira-input__clear"
            @click="handleClear"
          />
          
          <!-- 密码显示切换 -->
          <Icon
            v-if="showPasswordToggle"
            :icon="passwordVisible ? 'i-mdi:eye-off' : 'i-mdi:eye'"
            class="inspira-input__password-toggle"
            @click="togglePasswordVisibility"
          />
          
          <!-- 自定义后缀图标 -->
          <Icon v-if="suffixIcon" :icon="suffixIcon" />
          <slot name="suffix" />
        </div>
      </div>

      <!-- 后置内容 -->
      <div v-if="$slots.append" class="inspira-input__append">
        <slot name="append" />
      </div>

      <!-- 验证消息 -->
      <div v-if="validateMessage" :class="validateMessageClass">
        {{ validateMessage }}
      </div>
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, useSlots } from 'vue';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/Icon';
import ErrorBoundary from '../Utils/ErrorBoundary.vue';
import type { InspiraInputProps, InspiraInputEmits } from './types';

const props = withDefaults(defineProps<InspiraInputProps>(), {
  type: 'text',
  size: 'default',
  disabled: false,
  readonly: false,
  animation: 'focus',
  showPassword: false,
  clearable: false,
  autocomplete: 'off',
  autofocus: false,
  validateStatus: undefined,
});

const emit = defineEmits<InspiraInputEmits>();

// 输入框引用
const inputRef = ref<HTMLInputElement>();

// 内部状态
const isFocused = ref(false);
const passwordVisible = ref(false);

// 计算输入框类型
const computedType = computed(() => {
  if (props.type === 'password') {
    return passwordVisible.value ? 'text' : 'password';
  }
  return props.type;
});

// Element Plus 降级 props
const elementPlusProps = computed(() => ({
  modelValue: props.modelValue,
  type: props.type,
  placeholder: props.placeholder,
  size: props.size,
  disabled: props.disabled,
  readonly: props.readonly,
  maxlength: props.maxlength,
  minlength: props.minlength,
  showPassword: props.showPassword,
  clearable: props.clearable,
  prefixIcon: props.prefixIcon,
  suffixIcon: props.suffixIcon,
  autocomplete: props.autocomplete,
  autofocus: props.autofocus,
  name: props.name,
  class: props.class,
}));

// 获取插槽
const slots = useSlots();

// 是否显示后缀区域
const showSuffix = computed(() => {
  return showClear.value || showPasswordToggle.value || props.suffixIcon || !!slots.suffix;
});

// 是否显示清空按钮
const showClear = computed(() => {
  return (
    props.clearable &&
    !props.disabled &&
    !props.readonly &&
    props.modelValue &&
    String(props.modelValue).length > 0
  );
});

// 是否显示密码切换按钮
const showPasswordToggle = computed(() => {
  return props.showPassword && props.type === 'password';
});

// 包装器样式类
const wrapperClass = computed(() => {
  return cn(
    'inspira-input',
    `inspira-input--${props.size}`,
    {
      'inspira-input--disabled': props.disabled,
      'inspira-input--readonly': props.readonly,
      'inspira-input--focused': isFocused.value,
      [`inspira-input--${props.validateStatus}`]: props.validateStatus,
      [`inspira-input--${props.animation}`]: props.animation !== 'none',
    },
    props.class
  );
});

// 输入框包装器样式类
const inputWrapperClass = computed(() => {
  return cn('inspira-input__wrapper', {
    'inspira-input__wrapper--disabled': props.disabled,
    'inspira-input__wrapper--readonly': props.readonly,
    'inspira-input__wrapper--focused': isFocused.value,
    [`inspira-input__wrapper--${props.validateStatus}`]: props.validateStatus,
  });
});

// 输入框样式类
const inputClass = computed(() => {
  return cn('inspira-input__inner');
});

// 验证消息样式类
const validateMessageClass = computed(() => {
  return cn('inspira-input__message', {
    [`inspira-input__message--${props.validateStatus}`]: props.validateStatus,
  });
});

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value;
  emit('update:modelValue', value);
  emit('input', value, event);
};

const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value;
  emit('change', value, event);
};

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event);
};

const handleKeyup = (event: KeyboardEvent) => {
  emit('keyup', event);
};

const handleKeypress = (event: KeyboardEvent) => {
  emit('keypress', event);
};

const handleClear = () => {
  emit('update:modelValue', '');
  emit('clear');
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value;
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const handleError = (error: Error) => {
  console.warn('InspiraInput error, falling back to el-input:', error);
};

// 暴露方法
const focus = () => {
  inputRef.value?.focus();
};

const blur = () => {
  inputRef.value?.blur();
};

const select = () => {
  inputRef.value?.select();
};

defineExpose({
  focus,
  blur,
  select,
  inputRef,
});
</script>

<style scoped>
.inspira-input {
  @apply inspira-ui;
  
  position: relative;
  display: inline-flex;
  width: 100%;
  flex-direction: column;
}

.inspira-input__wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--inspira-border);
  border-radius: var(--inspira-radius);
  background-color: var(--inspira-background);
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &:hover:not(.inspira-input__wrapper--disabled) {
    border-color: var(--inspira-primary-light);
  }
  
  &--focused {
    border-color: var(--inspira-primary);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--inspira-surface);
  }
  
  &--readonly {
    background-color: var(--inspira-surface);
  }
  
  &--error {
    border-color: #ef4444;
    
    &.inspira-input__wrapper--focused {
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    }
  }
  
  &--success {
    border-color: #10b981;
    
    &.inspira-input__wrapper--focused {
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    }
  }
  
  &--warning {
    border-color: #f59e0b;
    
    &.inspira-input__wrapper--focused {
      box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
    }
  }
}

.inspira-input__inner {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--inspira-text);
  font-size: inherit;
  padding: 8px 12px;
  
  &::placeholder {
    color: var(--inspira-text-secondary);
  }
  
  &:disabled {
    cursor: not-allowed;
  }
  
  &:read-only {
    cursor: default;
  }
}

.inspira-input__prefix,
.inspira-input__suffix {
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: var(--inspira-text-secondary);
  flex-shrink: 0;
}

.inspira-input__clear,
.inspira-input__password-toggle {
  cursor: pointer;
  transition: color var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &:hover {
    color: var(--inspira-primary);
  }
}

.inspira-input__prepend,
.inspira-input__append {
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: var(--inspira-surface);
  border: 1px solid var(--inspira-border);
  color: var(--inspira-text-secondary);
  white-space: nowrap;
}

.inspira-input__prepend {
  border-right: none;
  border-radius: var(--inspira-radius) 0 0 var(--inspira-radius);
}

.inspira-input__append {
  border-left: none;
  border-radius: 0 var(--inspira-radius) var(--inspira-radius) 0;
}

.inspira-input__message {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.4;
  
  &--error {
    color: #ef4444;
  }
  
  &--success {
    color: #10b981;
  }
  
  &--warning {
    color: #f59e0b;
  }
  
  &--validating {
    color: var(--inspira-primary);
  }
}

/* 尺寸变体 */
.inspira-input--small {
  .inspira-input__inner {
    padding: 6px 10px;
    font-size: 14px;
  }
  
  .inspira-input__wrapper {
    height: 32px;
  }
}

.inspira-input--default {
  .inspira-input__inner {
    padding: 8px 12px;
    font-size: 16px;
  }
  
  .inspira-input__wrapper {
    height: 40px;
  }
}

.inspira-input--large {
  .inspira-input__inner {
    padding: 10px 14px;
    font-size: 18px;
  }
  
  .inspira-input__wrapper {
    height: 48px;
  }
}

/* 动画效果 */
.inspira-input--focus {
  .inspira-input__wrapper--focused {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  }
}

.inspira-input--blur {
  .inspira-input__wrapper {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.inspira-input--typing {
  .inspira-input__inner {
    transition: all 0.2s ease-in-out;
  }
  
  .inspira-input__inner:focus {
    transform: scale(1.02);
  }
}

/* 禁用状态下的前置/后置内容 */
.inspira-input--disabled {
  .inspira-input__prepend,
  .inspira-input__append {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* 只读状态 */
.inspira-input--readonly {
  .inspira-input__wrapper {
    cursor: default;
  }
}
</style>