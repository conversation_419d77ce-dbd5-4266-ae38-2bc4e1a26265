# INITIAL.md - UI Migration to Inspira UI

## FEATURE

将 autoclip-autoclip-web-front 项目从 Element Plus UI 框架迁移到 Inspira UI 组件库，以提升用户界面的现代化程度和视觉效果。

### 核心目标
1. **视觉升级**: 使用 Inspira UI 的现代化设计和动画效果提升用户体验
2. **功能保持**: 确保所有现有功能在迁移过程中保持完整
3. **性能优化**: 通过合理的动画和特效配置保持良好的性能表现
4. **渐进迁移**: 采用渐进式迁移策略，降低风险

### 主要迁移内容
- **登录表单**: 使用 RainbowButton 和自定义输入框替换现有 Element Plus 组件
- **背景特效**: 在登录页面集成 AuroraBackground，在主页集成 FallingStarsBg
- **交互组件**: 使用 AnimatedTooltip、TextHighlight 等组件增强用户交互
- **按钮系统**: 全面替换 el-button 为 Inspira UI 按钮组件
- **表单系统**: 保持验证逻辑的同时升级视觉效果

## EXAMPLES

### 参考 Inspira UI 组件实现

#### 1. RainbowButton 组件
```vue
<!-- 来源: inspira-ui/src/components/RainbowButton.vue -->
<template>
  <component
    :is="is"
    :class="cn('rainbow-button', 'group relative inline-flex h-11 cursor-pointer items-center justify-center rounded-xl border-0 bg-[length:200%] px-8 py-2 font-medium text-primary-foreground transition-colors')"
  >
    <slot />
  </component>
</template>
```

**应用场景**: 替换登录表单中的主要操作按钮，如"登录"、"注册"按钮

#### 2. AuroraBackground 组件
```vue
<!-- 来源: inspira-ui/src/components/AuroraBackground.vue -->
<template>
  <main>
    <div class="transition-bg relative flex h-[100vh] flex-col items-center justify-center bg-zinc-50 text-slate-950 dark:bg-zinc-900">
      <div class="absolute inset-0 overflow-hidden">
        <!-- Aurora animation effects -->
      </div>
      <slot />
    </div>
  </main>
</template>
```

**应用场景**: 作为登录页面的背景，提供动态的极光效果

#### 3. 现有 Element Plus 使用模式
```vue
<!-- 来源: autoclip-autoclip-web-front/src/components/Login/src/LoginForm.vue -->
<el-button
  :loading="loading"
  @click="handleLogin"
  class="!h-12 !w-full !font-medium"
  size="large"
>
  登陆
</el-button>

<el-input
  v-model="loginFormProps.identifier"
  placeholder="请输入手机号/邮箱"
  class="!h-12"
  size="large"
>
  <template #prefix>
    <Icon icon="i-mdi:account" class="text-gray-400" />
  </template>
</el-input>
```

**迁移策略**: 保持相同的 props 接口和事件处理，仅替换底层实现

### 组件封装模式

#### 创建兼容性包装器
```vue
<!-- src/components/inspira/Button/InspiraButton.vue -->
<template>
  <RainbowButton
    v-if="type === 'primary' && !hasError"
    :class="buttonClass"
    @click="handleClick"
  >
    <slot />
  </RainbowButton>
  <el-button
    v-else
    :type="type"
    :loading="loading"
    :disabled="disabled"
    :class="buttonClass"
    @click="handleClick"
  >
    <slot />
  </el-button>
</template>

<script setup lang="ts">
// 保持与 el-button 相同的 API
interface Props {
  type?: 'primary' | 'secondary' | 'default';
  loading?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}

// 错误处理和降级逻辑
const hasError = ref(false);
onErrorCaptured(() => {
  hasError.value = true;
  return false;
});
</script>
```

## DOCUMENTATION

### Inspira UI 组件文档
- **项目地址**: `./inspira-ui/` (本地参考项目)
- **组件源码**: `./inspira-ui/src/components/`
- **可用组件**:
  - `RainbowButton.vue` - 彩虹渐变按钮
  - `AuroraBackground.vue` - 极光背景效果
  - `FallingStarsBg.vue` - 流星背景效果
  - `Vortex.vue` - 漩涡背景效果
  - `FlipWords.vue` - 文字翻转动画
  - `TextHighlight.vue` - 文本高亮效果
  - `AnimatedTooltip.vue` - 动画提示框
  - `BlurReveal.vue` - 模糊揭示效果
  - `BorderBeam.vue` - 边框光束效果

### Element Plus 文档
- **官方文档**: https://element-plus.org/
- **当前使用的组件**: el-button, el-input, el-form, el-dialog, el-radio-group, el-checkbox, el-divider, el-link
- **迁移策略**: 保持 API 兼容性，逐步替换实现

### Vue 3 + TypeScript 最佳实践
- **Composition API**: 使用 `<script setup>` 语法
- **TypeScript**: 严格类型检查，定义清晰的接口
- **响应式**: 合理使用 ref、reactive、computed
- **生命周期**: 使用组合式 API 的生命周期钩子

### 构建工具配置
- **Vite**: 已配置支持 Vue 3 + TypeScript
- **UnoCSS**: 原子化 CSS 框架
- **Tailwind**: 实用工具类
- **SCSS**: CSS 预处理器

## OTHER CONSIDERATIONS

### 性能考虑
1. **动画性能**: 确保动画帧率稳定在 60fps
2. **包大小**: 监控 Inspira UI 组件对打包大小的影响
3. **懒加载**: 对于复杂的背景特效组件考虑懒加载
4. **内存使用**: 避免动画导致的内存泄漏

### 兼容性要求
1. **浏览器支持**: 保持与现有项目相同的浏览器兼容性
2. **响应式设计**: 确保在各种设备尺寸下正常显示
3. **无障碍访问**: 维持或改善现有的可访问性标准
4. **国际化**: 支持中英文切换

### 风险控制
1. **回滚机制**: 每个迁移的组件都必须有 Element Plus 降级方案
2. **错误边界**: 使用 Vue 的错误处理机制防止组件崩溃
3. **渐进部署**: 先在非关键页面测试，再应用到核心功能
4. **用户反馈**: 收集用户对新 UI 的反馈并及时调整

### 测试策略
1. **功能测试**: 确保所有现有功能正常工作
2. **视觉测试**: 对比新旧界面的视觉效果
3. **性能测试**: 监控页面加载和动画性能
4. **用户体验测试**: 验证用户操作流程的流畅性

### 开发流程
1. **小步快跑**: 每次只迁移一个组件或页面
2. **充分测试**: 每个迁移步骤都要进行全面测试
3. **代码审查**: 确保代码质量和一致性
4. **文档更新**: 及时更新相关文档和注释

### 特殊注意事项
1. **表单验证**: 登录表单的验证逻辑必须完全保持
2. **状态管理**: 不要影响现有的 Pinia/Zustand 状态管理
3. **路由守卫**: 确保认证相关的路由守卫正常工作
4. **WebSocket**: 不要影响视频处理的 WebSocket 连接
5. **视频编辑器**: 避免影响 React 组件的视频编辑功能