# 通用项目使用流程：基于上下文工程

上下文工程（Context Engineering）是一种先进的AI编程方法，它通过提供完整、体系化的项目上下文，取代了传统的“提示词工程”。这种方法旨在让AI像一位真正的开发者一样，全面理解项目背景、规范和目标，从而高效、高质量地完成复杂的开发任务。

## 核心流程

### 第一步：准备项目

**提供代码示例 (examples/)**：在 `examples/` 文件夹中添加高质量的代码示例。AI在有具体代码模式可参考时，表现会出色得多。这是AI理解项目风格和架构的关键依据。

---

### 第二步：设定全局规则 (`CLAUDE.md`) - 项目的“宪法”

`CLAUDE.md` 是整个项目的“宪法”或“工作手册”，它为AI助手设定了持久的、全局性的行为准则和开发规范。

#### **特点**
-   **全局性**：规则适用于项目中的所有开发任务。
-   **持久性**：一旦设定，通常不会频繁变动。
-   **权威性**：是AI所有行为的最高准则，确保产出的一致性。

#### **编辑方法**
**通常由AI生成，再由人工审查和完善。** 这种方式远比手动编写高效且全面。

1.  **提供核心上下文**：为AI提供足够的信息，如项目的代码示例 (`examples/`)、相关技术（如视频中的`AutoGen`）的官方文档链接等。
2.  **下达生成指令**：向AI（在Claude Code或Cursor中）下达一个高阶指令，引导它创建这个规则文件。例如：
    > “我需要用 AutoGen 框架实现一个智能代码生成系统。请分析当前项目的文件结构、`examples/` 目录下的代码模式以及官方文档，为我生成一份详细的 `CLAUDE.md` 文件，其中应包含项目认知、代码结构、测试要求和安全规范等全局规则。”
3.  **人工审查与微调**：AI会生成一份非常详尽的 `CLAUDE.md`。您的任务是审查这份文件，确保其中的规则符合您的项目要求，并进行必要的微调。

---

### 第三步：描述初始功能需求 (`INITIAL.md`) - 单次任务的“需求说明书”

`INITIAL.md` 是针对**某一个具体开发任务**的需求说明书。每次需要开发新功能时，都应准备一份。

#### **特点**
-   **任务导向**：完全聚焦于本次要开发的单一功能。
-   **动态性**：每次新任务都会创建或修改一份。
-   **具体性**：包含实现该功能所需的所有具体信息。

#### **编辑方法**
**AI生成模板，人工填充具体需求。**

1.  **生成模板**：您可以让AI根据 `CLAUDE.md` 的规则和项目上下文，为您生成一个结构化的 `INITIAL.md` 模板。这个模板会包含 `## FEATURE`, `## EXAMPLES`, `## DOCUMENTATION` 等标准章节。
2.  **人工填充具体需求**：您的角色是**产品经理**，负责清晰、准确地传达任务要求。您需要：
    *   在 `## FEATURE` 部分，明确具体地描述本次要实现的功能。
    *   在 `## EXAMPLES` 部分，引用 `examples/` 文件夹中与本次任务最相关的代码，并说明其参考价值。
    *   在 `## DOCUMENTATION` 部分，提供实现该功能所必需的API文档、技术指南等链接。
    *   在 `## OTHER CONSIDERATIONS` 部分，补充任何AI可能忽略的特殊要求或潜在陷阱。

---

### 第四步：生成详细开发蓝图 (PRP)

PRP (Product Requirements & Patterns) 是 AI 为完成任务而生成的详细“开发蓝图”。

在AI编程工具中运行以下命令，将 `INITIAL.md` 转换成可执行的详细计划：
```
/generate-prp INITIAL.md
```
AI会分析需求，生成一个位于 `PRPs/` 目录下的详细 `.md` 文件。**请务必在执行前审查并校对这份PRP文件**。

### 第五步：执行蓝图以实现功能

PRP校对无误后，运行执行命令来启动全自动开发。将 `your-feature-name.md` 替换为上一步生成的实际文件名。
```
/execute-prp PRPs/your-feature-name.md
```
AI将按照PRP中的计划，自动完成代码编写、测试、修复和验证的全过程。