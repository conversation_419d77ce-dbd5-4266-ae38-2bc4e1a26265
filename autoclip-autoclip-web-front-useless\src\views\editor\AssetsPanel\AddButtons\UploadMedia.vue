<script setup lang="ts">
import { ref, computed } from 'vue';
import { useProjectStore } from '@/store/editor';
import { useMessage } from '@/hooks/web/useMessage';
import { storeFile } from '@/store/editor';
import { buildUUID } from '@/utils/uuid';

const projectStore = useProjectStore();
const filesID = computed(() => projectStore.getFilesID);
const { $message } = useMessage();

const fileInputRef = ref<HTMLInputElement>();

const handleFileChange = async (e: Event) => {
  try {
    const target = e.target as HTMLInputElement;
    const newFiles = Array.from(target.files || []);
    const updatedFiles = [...(filesID.value || [])];

    for (const file of newFiles) {
      const fileId = buildUUID();
      const storedFileId = await storeFile(file, fileId);

      if (storedFileId) {
        updatedFiles.push(fileId);
      } else {
        $message.error(`Failed to store file: ${file.name}`);
      }
    }

    projectStore.setFilesID(updatedFiles);
    target.value = '';

    if (newFiles.length > 0) {
      $message.success(`Successfully uploaded ${newFiles.length} file(s)`);
    }
  } catch (error) {
    console.error('Error uploading files:', error);
    $message.error('Failed to upload files. Please try again.');
  }
};

const triggerFileInput = () => {
  fileInputRef.value?.click();
};
</script>

<template>
  <div>
    <button
      @click="triggerFileInput"
      class="flex cursor-pointer flex-row items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md"
    >
      <el-image
        alt="Upload Media"
        class="h-4 w-4"
        :src="'https://www.svgrepo.com/show/514275/upload-cloud.svg'"
      />
      <span class="text-sm font-medium">Add Media</span>
    </button>
    <input
      ref="fileInputRef"
      type="file"
      accept="video/*,audio/*,image/*"
      multiple
      @change="handleFileChange"
      class="hidden"
    />
  </div>
</template>
