import { defineAsyncComponent, h } from 'vue';
import { ElLoading } from 'element-plus';
import { noop } from '@/utils';

interface Options {
  size?: 'small' | 'default' | 'large';
  delay?: number;
  timeout?: number;
  loading?: boolean;
  retry?: boolean;
}

export function createAsyncComponent(
  loader: () => Promise<any>,
  options: Options = {},
) {
  const {
    size = 'small',
    delay = 100,
    timeout = 30000,
    loading = false,
    retry = true,
  } = options;

  // 转换Element Plus的加载大小
  const elSize =
    size === 'large' ? 'large' : size === 'small' ? 'small' : 'default';

  return defineAsyncComponent({
    loader,
    // 使用Element Plus的Loading组件
    loadingComponent: loading
      ? () =>
          h(ElLoading, {
            target: '.el-loading-target', // 加载目标容器
            size: elSize,
            spinner: 'el-icon-loading', // 自定义加载图标
            background: 'rgba(255, 255, 255, 0.9)', // 背景样式
          })
      : undefined,
    timeout,
    delay,
    onError: !retry
      ? noop
      : (error, retryFn, failFn, attempts) => {
          if (error.message.match(/fetch/) && attempts <= 3) {
            retryFn(); // 重试加载
          } else {
            failFn(); // 终止加载
          }
        },
  });
}
