{"issue": "Issue Feedback", "submitFeedback": "Submit <PERSON>", "myIssues": "My Issues", "issueType": "Issue Type", "issueStatus": "Issue Status", "priority": "Priority", "searchPlaceholder": "Search title or description", "search": "Search", "reset": "Reset", "noIssues": "No issues found", "submitFirstFeedback": "Submit First Feedback", "loading": "Loading...", "submitIssue": "Submit Issue Feedback", "issueTitle": "Issue Title", "issueDescription": "Issue Description", "relatedVideo": "Related Video", "issueScope": "Issue Scope", "externalIssue": "External Issue", "internalIssue": "Internal Issue", "submit": "Submit <PERSON>", "cancel": "Cancel", "issueDetail": "Issue Detail", "basicInfo": "Basic Information", "title": "Title", "type": "Type", "status": "Status", "submitter": "Submitter", "createTime": "Create Time", "assignee": "Assignee", "relatedVideoName": "Related Video", "description": "Description", "resolution": "Resolution", "closeInfo": "Close Information", "closeTime": "Close Time", "closedBy": "Closed By", "close": "Close", "bug": "Bug", "requirement": "Requirement", "pending": "Pending", "processing": "Processing", "resolved": "Resolved", "closed": "Closed", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}