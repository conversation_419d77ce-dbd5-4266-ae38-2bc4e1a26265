<template>
  <el-form-item
    v-bind="item.formItemProps || {}"
    :prop="item.field"
    :label="item.label || ''"
  >
    <template v-if="item.labelMessage" #label>
      <span>{{ item.label }}</span>
      <el-tooltip placement="right" raw-content>
        <template #content>
          <span v-html="item.labelMessage"></span>
        </template>
        <Icon
          icon="ep:warning"
          :size="16"
          color="var(--el-color-primary)"
          class="top-1px ml-2px relative"
        />
      </el-tooltip>
    </template>

    <template
      v-for="(_, slotName) in formItemSlots"
      :key="slotName"
      #[slotName]="slotProps"
    >
      <slot :name="slotName" v-bind="slotProps" />
    </template>

    <slot
      v-if="$slots[item.field]"
      :name="item.field"
      :form-model="formModel"
    />
    <component
      v-else
      :is="componentMap[item.component as string]"
      v-model="formModel[item.field]"
      v-bind="{
        ...(autoSetPlaceholder && setTextPlaceholder(item)),
        ...setComponentProps(item),
        ...(notRenderOptions.includes(item?.component as string) &&
        item?.componentProps?.options
          ? { options: item?.componentProps?.options || [] }
          : {}),
      }"
      :style="item.componentProps?.style"
    >
      <!-- 渲染Select/Radio/Checkbox的options -->
      <SelectOptions v-if="shouldRenderSelectOptions(item)" :item="item">
        <template
          v-for="(_, slotName) in $slots"
          :key="slotName"
          #[slotName]="slotProps"
        >
          <slot :name="slotName" v-bind="slotProps" />
        </template>
      </SelectOptions>

      <RadioOptions v-else-if="shouldRenderRadioOptions(item)" :item="item" />

      <CheckboxOptions
        v-else-if="shouldRenderCheckboxOptions(item)"
        :item="item"
      />

      <!-- 其他插槽 -->
      <template
        v-for="(_, slotName) in slotsMap"
        :key="slotName"
        #[slotName]="slotProps"
      >
        <slot :name="slotName" v-bind="slotProps" />
      </template>
    </component>
  </el-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElFormItem, ElTooltip } from 'element-plus';
import { componentMap } from './componentMap';
import {
  setComponentProps,
  setFormItemSlots,
  setItemComponentSlots,
  setTextPlaceholder,
} from './helper';
import { Icon } from '@/components/Icon';
import { FormSchema } from '@/types/form';

interface Props {
  item: FormSchema;
  formModel: Recordable;
  autoSetPlaceholder: boolean;
}

const props = defineProps<Props>();
const slots = defineSlots();

// 单独给只有options属性的组件做判断
const notRenderOptions = ['SelectV2', 'Cascader', 'Transfer'];

const slotsMap = computed(() => {
  return {
    ...setItemComponentSlots(
      slots,
      props.item?.componentProps?.slots,
      props.item.field,
    ),
  };
});

const formItemSlots = computed(() => {
  return setFormItemSlots(slots, props.item.field);
});

// 判断是否需要渲染不同类型的options
const shouldRenderSelectOptions = (item: FormSchema) => {
  return (
    (item.component === 'Select' || item.component === 'SelectV2') &&
    item?.componentProps?.options &&
    !notRenderOptions.includes(item.component)
  );
};

const shouldRenderRadioOptions = (item: FormSchema) => {
  return (
    (item.component === 'Radio' || item.component === 'RadioButton') &&
    item?.componentProps?.options
  );
};

const shouldRenderCheckboxOptions = (item: FormSchema) => {
  return (
    (item.component === 'Checkbox' || item.component === 'CheckboxButton') &&
    item?.componentProps?.options
  );
};
</script>
