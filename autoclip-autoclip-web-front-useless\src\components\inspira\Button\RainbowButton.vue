<!-- 
  彩虹按钮组件 - 基于 Inspira UI 的 RainbowButton
  提供动态彩虹渐变效果
-->
<template>
  <component
    :is="is"
    :class="computedClass"
    :disabled="disabled"
    @click="handleClick"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { cn } from '@/lib/utils';

interface RainbowButtonProps {
  class?: string;
  is?: string;
  speed?: number;
  disabled?: boolean;
}

const props = withDefaults(defineProps<RainbowButtonProps>(), {
  speed: 2,
  is: 'button',
  disabled: false,
});

const emit = defineEmits<{
  click: [event: MouseEvent];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}>();

const speedInSeconds = computed(() => `${props.speed}s`);

const computedClass = computed(() => {
  return cn(
    'rainbow-button',
    'group relative inline-flex h-11 cursor-pointer items-center justify-center rounded-xl border-0 bg-[length:200%] px-8 py-2 font-medium text-primary-foreground transition-colors [background-clip:padding-box,border-box,border-box] [background-origin:border-box] [border:calc(0.08*1rem)_solid_transparent] focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
    'before:absolute before:bottom-[-20%] before:left-1/2 before:z-0 before:h-1/5 before:w-3/5 before:-translate-x-1/2 before:bg-[linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))] before:bg-[length:200%] before:[filter:blur(calc(0.8*1rem))]',
    'bg-[linear-gradient(#121213,#121213),linear-gradient(#121213_50%,rgba(18,18,19,0.6)_80%,rgba(18,18,19,0)),linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))]',
    'dark:bg-[linear-gradient(#fff,#fff),linear-gradient(#fff_50%,rgba(255,255,255,0.6)_80%,rgba(0,0,0,0)),linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))]',
    props.disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    props.class,
  );
});

const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event);
  }
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};
</script>

<style scoped>
.rainbow-button {
  --color-1: var(--inspira-rainbow-color-1, hsl(0 100% 63%));
  --color-2: var(--inspira-rainbow-color-2, hsl(270 100% 63%));
  --color-3: var(--inspira-rainbow-color-3, hsl(210 100% 63%));
  --color-4: var(--inspira-rainbow-color-4, hsl(195 100% 63%));
  --color-5: var(--inspira-rainbow-color-5, hsl(90 100% 63%));
  --speed: v-bind(speedInSeconds);
  animation: rainbow var(--speed) infinite linear;
}

.rainbow-button:before {
  animation: rainbow var(--speed) infinite linear;
}

.rainbow-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.rainbow-button:active:not(:disabled) {
  transform: translateY(0);
}

.rainbow-button:focus-visible {
  outline: 2px solid var(--inspira-primary);
  outline-offset: 2px;
}

@keyframes rainbow {
  0% {
    background-position: 0;
  }
  100% {
    background-position: 200%;
  }
}

/* 确保文字颜色在彩虹背景上可读 */
.rainbow-button {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .rainbow-button {
    height: 44px;
    padding: 0 20px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .rainbow-button {
    height: 40px;
    padding: 0 16px;
    font-size: 14px;
  }
}
</style>