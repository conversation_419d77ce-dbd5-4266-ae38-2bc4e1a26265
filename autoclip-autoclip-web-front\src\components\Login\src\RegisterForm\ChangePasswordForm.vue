<script setup lang="ts">
import { reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import AuthUtil, { getIdentifierType, SmsSceneEnum } from '@/api/member/auth';
import { useCountdown } from '@vueuse/core';
import UserApi from '@/api/member/user';

interface ChangePasswordForm {
  identifier: string;
  code?: string;
  password: string;
  confirmPassword: string;
}

const props = withDefaults(
  defineProps<{
    identifier: string;
    isRegister?: boolean;
    code?: string;
    size?: 'large' | 'small' | 'default';
    displayLabel?: boolean;
  }>(),
  {
    displayLabel: false,
    size: 'default',
    isRegister: false,
  },
);

type Emits = {
  (e: 'change-password-success'): void;
  (e: 'change-password-failed', errorMessage: string): void;
  (e: 'loading'): void;
};

const emit = defineEmits<Emits>();

const { $notification } = useMessage();
const changePasswordFormRef = ref<FormInstance>();
const changePasswordForm = reactive<ChangePasswordForm>({
  identifier: '',
  code: '',
  password: '',
  confirmPassword: '',
});

watch(
  () => props.code,
  (newVal) => {
    changePasswordForm.code = newVal;
  },
  {
    immediate: true,
  },
);
watch(
  () => props.identifier,
  (newVal) => {
    changePasswordForm.identifier = newVal;
  },
  {
    immediate: true,
  },
);

const validateConfirmPassword = (_rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== changePasswordForm.password) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
};

const changePasswordRules: FormRules = {
  identifier: [
    { required: true, message: '请输入手机号/邮箱', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{4,6}$/, message: '请输入4-6位数字验证码', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' },
  ],
};

const $countdown = useCountdown(0);
const loading = ref(false);

const getVerificationCode = async () => {
  changePasswordFormRef.value?.validate();

  $countdown.start(60);

  AuthUtil.sendAuthCode(
    changePasswordForm.identifier,
    getIdentifierType(changePasswordForm.identifier),
    SmsSceneEnum.MEMBER_UPDATE_PASSWORD,
  )
    .then(() => {
      $notification.success('验证码已发送，请注意查收');
    })
    .catch(() => {
      $countdown.stop();
    });
};

const handleChangePassword = async () => {
  try {
    await changePasswordFormRef.value?.validate();
  } catch (error) {
    return;
  }

  loading.value = true;
  console.log(changePasswordForm);
  UserApi.updateUserPassword({
    password: changePasswordForm.password,
    identifier: changePasswordForm.identifier,
    identifierType: getIdentifierType(changePasswordForm.identifier),
    code: changePasswordForm.code!,
  })
    .then(() => {
      emit('change-password-success');
      $notification.success('密码设置成功');
      loading.value = false;
      resetForm();
    })
    .catch((e) => {
      $notification.error('密码设置失败' + e);
      $countdown.stop();
      loading.value = false;
    });
};

const resetForm = () => {
  changePasswordForm.password = '';
  changePasswordForm.confirmPassword = '';
  changePasswordForm.code = '';
  loading.value = false;
  $countdown.stop();
};

defineExpose({
  resetForm,
  handleChangePassword,
  buttonLoading: loading,
});
</script>

<template>
  <el-form
    ref="changePasswordFormRef"
    :model="changePasswordForm"
    :rules="changePasswordRules"
    @submit.prevent="handleChangePassword"
    class="space-y-4"
    :label-width="'auto'"
  >
    <el-form-item
      v-if="!isRegister"
      prop="identifier"
      class="!mb-4"
      :label="displayLabel ? '手机号/邮箱' : ''"
    >
      <el-input
        v-model="changePasswordForm.identifier"
        type="text"
        placeholder="请输入手机号/邮箱"
        :size="size"
      >
        <template #prefix>
          <Icon icon="i-mdi:account" class="text-gray-400" />
        </template>
      </el-input>
    </el-form-item>
    <el-form-item
      prop="password"
      class="!mb-4"
      :label="displayLabel ? '新密码' : ''"
    >
      <el-input
        v-model="changePasswordForm.password"
        type="password"
        placeholder="请输入密码"
        :size="size"
        show-password
      >
        <template #prefix>
          <Icon icon="i-mdi:lock" class="text-gray-400" />
        </template>
      </el-input>
    </el-form-item>

    <!-- 确认密码 -->
    <el-form-item
      prop="confirmPassword"
      class="!mb-4"
      :label="displayLabel ? '确认密码' : ''"
    >
      <el-input
        v-model="changePasswordForm.confirmPassword"
        type="password"
        placeholder="请再次输入密码"
        :size="size"
        show-password
      >
        <template #prefix>
          <Icon icon="i-mdi:lock-check" class="text-gray-400" />
        </template>
      </el-input>
    </el-form-item>

    <el-form-item
      v-if="!isRegister"
      prop="code"
      :label="displayLabel ? '验证码' : ''"
    >
      <el-input
        v-model="changePasswordForm.code"
        placeholder="请输入验证码"
        :size="size"
      >
        <template #prefix>
          <Icon icon="i-mdi:key-variant" class="text-gray-400" />
        </template>
        <template #suffix>
          <el-button
            :disabled="$countdown.remaining.value > 0"
            @click="getVerificationCode()"
            text
            class="!px-3 !py-1 !text-sm"
          >
            {{
              $countdown.remaining.value > 0
                ? `${$countdown.remaining.value}s后重新获取`
                : '获取验证码'
            }}
          </el-button>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss"></style>
