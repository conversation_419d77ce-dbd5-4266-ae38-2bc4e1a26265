<template>
  <el-config-provider :locale="getElementPlusLocale">
    <AppProvider>
      <RouterView />
    </AppProvider>
  </el-config-provider>
</template>

<script lang="ts" setup>
import AppProvider from '@/components/Application/src/AppProvider.vue';
import { ElConfigProvider } from 'element-plus';
import { useLocale } from './locales/useLocale';
const { getElementPlusLocale } = useLocale();
</script>

<style lang="scss" scoped></style>
