<!-- 
  按钮组件测试页面
  用于验证 InspiraButton 组件的各种功能
-->
<template>
  <div class="button-test-container">
    <h2 class="test-title">Inspira Button 组件测试</h2>
    
    <!-- 基础按钮测试 -->
    <section class="test-section">
      <h3>基础按钮类型</h3>
      <div class="button-group">
        <InspiraButton type="default">默认按钮</InspiraButton>
        <InspiraButton type="primary">主要按钮</InspiraButton>
        <InspiraButton type="success">成功按钮</InspiraButton>
        <InspiraButton type="warning">警告按钮</InspiraButton>
        <InspiraButton type="danger">危险按钮</InspiraButton>
        <InspiraButton type="info">信息按钮</InspiraButton>
        <InspiraButton type="text">文本按钮</InspiraButton>
      </div>
    </section>

    <!-- 尺寸测试 -->
    <section class="test-section">
      <h3>按钮尺寸</h3>
      <div class="button-group">
        <InspiraButton size="small">小按钮</InspiraButton>
        <InspiraButton size="default">默认按钮</InspiraButton>
        <InspiraButton size="large">大按钮</InspiraButton>
      </div>
    </section>

    <!-- 状态测试 -->
    <section class="test-section">
      <h3>按钮状态</h3>
      <div class="button-group">
        <InspiraButton :loading="true">加载中</InspiraButton>
        <InspiraButton :disabled="true">禁用按钮</InspiraButton>
        <InspiraButton type="primary" :loading="loading" @click="handleLoadingTest">
          {{ loading ? '加载中...' : '点击测试加载' }}
        </InspiraButton>
      </div>
    </section>

    <!-- 动画效果测试 -->
    <section class="test-section">
      <h3>动画效果</h3>
      <div class="button-group">
        <InspiraButton type="primary" animation="rainbow">彩虹按钮</InspiraButton>
        <InspiraButton type="primary" animation="hover">悬停动画</InspiraButton>
        <InspiraButton type="primary" animation="click">点击动画</InspiraButton>
        <InspiraButton type="primary" animation="none">无动画</InspiraButton>
      </div>
    </section>

    <!-- 形状测试 -->
    <section class="test-section">
      <h3>按钮形状</h3>
      <div class="button-group">
        <InspiraButton :block="true" type="primary">块级按钮</InspiraButton>
        <InspiraButton :round="true" type="primary">圆角按钮</InspiraButton>
        <InspiraButton :circle="true" type="primary" icon="i-mdi:heart" />
      </div>
    </section>

    <!-- 图标按钮测试 -->
    <section class="test-section">
      <h3>图标按钮</h3>
      <div class="button-group">
        <InspiraButton icon="i-mdi:account" icon-placement="left">左图标</InspiraButton>
        <InspiraButton icon="i-mdi:arrow-right" icon-placement="right">右图标</InspiraButton>
        <InspiraButton icon="i-mdi:close" :circle="true" type="text" />
        <InspiraButton icon="i-mdi:wechat" :circle="true" type="success" />
      </div>
    </section>

    <!-- 与 Element Plus 对比 -->
    <section class="test-section">
      <h3>与 Element Plus 对比</h3>
      <div class="comparison-group">
        <div class="comparison-item">
          <h4>Element Plus</h4>
          <div class="button-group">
            <el-button>默认按钮</el-button>
            <el-button type="primary">主要按钮</el-button>
            <el-button type="success">成功按钮</el-button>
            <el-button :loading="true">加载按钮</el-button>
          </div>
        </div>
        <div class="comparison-item">
          <h4>Inspira UI</h4>
          <div class="button-group">
            <InspiraButton>默认按钮</InspiraButton>
            <InspiraButton type="primary">主要按钮</InspiraButton>
            <InspiraButton type="success">成功按钮</InspiraButton>
            <InspiraButton :loading="true">加载按钮</InspiraButton>
          </div>
        </div>
      </div>
    </section>

    <!-- 事件测试 -->
    <section class="test-section">
      <h3>事件测试</h3>
      <div class="button-group">
        <InspiraButton @click="handleClick" @focus="handleFocus" @blur="handleBlur">
          事件测试按钮
        </InspiraButton>
      </div>
      <div class="event-log">
        <h4>事件日志：</h4>
        <ul>
          <li v-for="(event, index) in eventLog" :key="index">
            {{ event }}
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import InspiraButton from './InspiraButton.vue';

// 测试状态
const loading = ref(false);
const eventLog = ref<string[]>([]);

// 加载测试
const handleLoadingTest = async () => {
  loading.value = true;
  await new Promise(resolve => setTimeout(resolve, 2000));
  loading.value = false;
};

// 事件处理
const handleClick = () => {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] 按钮被点击`);
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10);
  }
};

const handleFocus = () => {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] 按钮获得焦点`);
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10);
  }
};

const handleBlur = () => {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] 按钮失去焦点`);
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10);
  }
};
</script>

<style scoped>
.button-test-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--inspira-text);
}

.test-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  border: 1px solid var(--inspira-border);
  border-radius: var(--inspira-radius);
  background: var(--inspira-surface);
}

.test-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--inspira-text);
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.comparison-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.comparison-item h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--inspira-text-secondary);
}

.event-log {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--inspira-background);
  border-radius: var(--inspira-radius);
  border: 1px solid var(--inspira-border);
}

.event-log h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--inspira-text);
}

.event-log ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.event-log li {
  padding: 0.25rem 0;
  font-size: 0.875rem;
  color: var(--inspira-text-secondary);
  border-bottom: 1px solid var(--inspira-border);
}

.event-log li:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-test-container {
    padding: 1rem;
  }
  
  .comparison-group {
    grid-template-columns: 1fr;
  }
  
  .button-group {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>