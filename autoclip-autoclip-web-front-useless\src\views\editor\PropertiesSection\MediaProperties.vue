<template>
  <div class="space-y-4 rounded-lg bg-white p-4" v-if="mediaFile">
    <div class="grid grid-cols-2 gap-8">
      <!-- Source Video -->
      <div class="space-y-2">
        <h4 class="font-semibold text-gray-900">Source Video</h4>
        <div class="flex items-center space-x-4">
          <div>
            <label class="block text-sm text-gray-700">Start (s)</label>
            <input
              type="number"
              :value="mediaFile.startTime"
              min="0"
              readonly
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    startTime: Number((e.target as HTMLInputElement).value),
                    endTime: mediaFile.endTime,
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">End (s)</label>
            <input
              type="number"
              :value="mediaFile.endTime"
              :min="mediaFile.startTime"
              readonly
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    startTime: mediaFile.startTime,
                    endTime: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
      <!-- Timing Position -->
      <div class="space-y-2">
        <h4 class="font-semibold text-gray-900">Timing Position</h4>
        <div class="flex items-center space-x-4">
          <div>
            <label class="block text-sm text-gray-700">Start (s)</label>
            <input
              type="number"
              :value="mediaFile.positionStart"
              min="0"
              readonly
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    positionStart: Number((e.target as HTMLInputElement).value),
                    positionEnd:
                      Number((e.target as HTMLInputElement).value) +
                      (mediaFile.positionEnd - mediaFile.positionStart),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">End (s)</label>
            <input
              type="number"
              :value="mediaFile.positionEnd"
              :min="mediaFile.positionStart"
              readonly
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    positionEnd: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
      <!-- Visual Properties -->
      <div class="space-y-6">
        <h4 class="font-semibold text-gray-900">Visual Properties</h4>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm text-gray-700">X Position</label>
            <input
              type="number"
              step="10"
              :value="mediaFile.x || 0"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    x: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Y Position</label>
            <input
              type="number"
              step="10"
              :value="mediaFile.y || 0"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    y: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Width</label>
            <input
              type="number"
              step="10"
              :value="mediaFile.width || 100"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    width: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Height</label>
            <input
              type="number"
              step="10"
              :value="mediaFile.height || 100"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    height: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Zindex</label>
            <input
              type="number"
              :value="mediaFile.zIndex || 0"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    zIndex: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm text-gray-700">Opacity</label>
            <input
              type="range"
              min="0"
              max="100"
              :value="mediaFile.opacity"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    opacity: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none"
            />
          </div>
        </div>
      </div>
      <!-- Audio Properties -->
      <div
        v-if="mediaFile.type === 'video' || mediaFile.type === 'audio'"
        class="space-y-2"
      >
        <h4 class="font-semibold text-gray-900">Audio Properties</h4>
        <div class="grid grid-cols-1 gap-4">
          <div>
            <label class="mb-2 block text-sm text-gray-700">Volume</label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              :value="mediaFile.volume"
              @input="
                (e: Event) =>
                  onUpdateMedia(mediaFile.id, {
                    volume: Number((e.target as HTMLInputElement).value),
                  })
              "
              class="w-full rounded border border-gray-300 bg-white text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useProjectStore } from '@/store/editor';
import type { MediaFile } from '@/types/editor';
import { storeToRefs } from 'pinia';

const projectStore = useProjectStore();
const { mediaFiles, activeElementIndex } = storeToRefs(projectStore);
const mediaFile = computed(() => mediaFiles.value[activeElementIndex.value]);

function onUpdateMedia(id: string, updates: Partial<MediaFile>) {
  projectStore.setMediaFiles(
    toRaw(mediaFiles.value).map((media) =>
      media.id === id ? { ...media, ...updates } : media,
    ),
  );
}
</script>
