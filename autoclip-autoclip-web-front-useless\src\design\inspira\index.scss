// Inspira UI 样式配置文件
// 确保与 Element Plus 样式不冲突

// CSS 变量定义
:root {
  // Inspira UI 主题色彩
  --inspira-primary: #6366f1;
  --inspira-primary-light: #818cf8;
  --inspira-primary-dark: #4f46e5;
  --inspira-secondary: #8b5cf6;
  --inspira-accent: #06b6d4;
  --inspira-background: #ffffff;
  --inspira-surface: #f8fafc;
  --inspira-text: #1e293b;
  --inspira-text-secondary: #64748b;
  --inspira-border: #e2e8f0;
  --inspira-shadow: rgba(0, 0, 0, 0.1);
  --inspira-radius: 0.5rem;
  
  // 动画配置
  --inspira-animation-duration: 0.3s;
  --inspira-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --inspira-animation-delay: 0s;
  
  // 彩虹按钮颜色
  --inspira-rainbow-color-1: hsl(0 100% 63%);
  --inspira-rainbow-color-2: hsl(270 100% 63%);
  --inspira-rainbow-color-3: hsl(210 100% 63%);
  --inspira-rainbow-color-4: hsl(195 100% 63%);
  --inspira-rainbow-color-5: hsl(90 100% 63%);
  
  // 极光背景颜色
  --inspira-aurora-blue-300: #93c5fd;
  --inspira-aurora-blue-400: #60a5fa;
  --inspira-aurora-blue-500: #3b82f6;
  --inspira-aurora-indigo-300: #a5b4fc;
  --inspira-aurora-violet-200: #ddd6fe;
}

// 暗色主题
[data-theme="dark"] {
  --inspira-background: #0f172a;
  --inspira-surface: #1e293b;
  --inspira-text: #f1f5f9;
  --inspira-text-secondary: #94a3b8;
  --inspira-border: #334155;
  --inspira-shadow: rgba(0, 0, 0, 0.3);
}

// Inspira UI 命名空间
.inspira-ui {
  // 基础样式重置
  * {
    box-sizing: border-box;
  }
  
  // 按钮基础样式
  .inspira-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: var(--inspira-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
    outline: none;
    user-select: none;
    
    &:focus-visible {
      outline: 2px solid var(--inspira-primary);
      outline-offset: 2px;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
    
    // 尺寸变体
    &--small {
      height: 32px;
      padding: 0 12px;
      font-size: 14px;
    }
    
    &--default {
      height: 40px;
      padding: 0 16px;
      font-size: 16px;
    }
    
    &--large {
      height: 48px;
      padding: 0 20px;
      font-size: 18px;
    }
    
    // 块级按钮
    &--block {
      width: 100%;
    }
    
    // 圆形按钮
    &--round {
      border-radius: 9999px;
    }
    
    // 圆形图标按钮
    &--circle {
      border-radius: 50%;
      width: var(--button-height);
      padding: 0;
    }
  }
  
  // 输入框基础样式
  .inspira-input {
    position: relative;
    display: inline-flex;
    width: 100%;
    
    &__wrapper {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      border: 1px solid var(--inspira-border);
      border-radius: var(--inspira-radius);
      background-color: var(--inspira-background);
      transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
      
      &:hover {
        border-color: var(--inspira-primary-light);
      }
      
      &:focus-within {
        border-color: var(--inspira-primary);
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
      }
      
      &--disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: var(--inspira-surface);
      }
      
      &--error {
        border-color: #ef4444;
        
        &:focus-within {
          box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
        }
      }
      
      &--success {
        border-color: #10b981;
        
        &:focus-within {
          box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }
      }
    }
    
    &__inner {
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      color: var(--inspira-text);
      font-size: inherit;
      padding: 8px 12px;
      
      &::placeholder {
        color: var(--inspira-text-secondary);
      }
      
      &:disabled {
        cursor: not-allowed;
      }
    }
    
    &__prefix,
    &__suffix {
      display: flex;
      align-items: center;
      padding: 0 8px;
      color: var(--inspira-text-secondary);
    }
    
    // 尺寸变体
    &--small {
      .inspira-input__inner {
        padding: 6px 10px;
        font-size: 14px;
      }
    }
    
    &--large {
      .inspira-input__inner {
        padding: 10px 14px;
        font-size: 18px;
      }
    }
  }
  
  // 动画类
  .inspira-fade-enter-active,
  .inspira-fade-leave-active {
    transition: opacity var(--inspira-animation-duration) var(--inspira-animation-easing);
  }
  
  .inspira-fade-enter-from,
  .inspira-fade-leave-to {
    opacity: 0;
  }
  
  .inspira-slide-up-enter-active,
  .inspira-slide-up-leave-active {
    transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  }
  
  .inspira-slide-up-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }
  
  .inspira-slide-up-leave-to {
    opacity: 0;
    transform: translateY(-20px);
  }
  
  // 工具类
  .inspira-loading {
    animation: inspira-spin 1s linear infinite;
  }
  
  @keyframes inspira-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .inspira-pulse {
    animation: inspira-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes inspira-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
}

// 确保与 Element Plus 样式不冲突
.el-button,
.el-input,
.el-form-item {
  // Element Plus 组件保持原有样式
  &:not(.inspira-ui *) {
    // 原有样式不受影响
  }
}