// 404 on a page
import { AppRouteRecordRaw } from '@/router/types';
import { EXCEPTION_COMPONENT, PAGE_NOT_FOUND_NAME } from '@/router/constant';

export const PAGE_NOT_FOUND_ROUTE: AppRouteRecordRaw = {
  path: '/:path(.*)*',
  name: PAGE_NOT_FOUND_NAME,
  component: EXCEPTION_COMPONENT,
  meta: {
    title: 'ErrorPage',
    hideBreadcrumb: true,
    hideMenu: true,
  },
};
export default PAGE_NOT_FOUND_ROUTE;
