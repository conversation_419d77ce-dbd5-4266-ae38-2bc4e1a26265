<template>
  <div class="coverflow-carousel" ref="containerRef">
    <div class="coverflow-container">
      <!-- 导航按钮 -->
      <button
        class="nav-btn nav-btn-left"
        @click="prev"
        :disabled="currentIndex === 0 && !infinite"
        v-if="showNavigation"
      >
        <Icon icon="i-mdi:chevron-left" />
      </button>

      <button
        class="nav-btn nav-btn-right"
        @click="next"
        :disabled="currentIndex === videoList.length - 1 && !infinite"
        v-if="showNavigation"
      >
        <Icon icon="i-mdi:chevron-right" />
      </button>

      <!-- 3D轮播区域 -->
      <div class="coverflow-stage" ref="stageRef">
        <div
          v-for="(video, index) in videoList"
          :key="index"
          class="coverflow-item"
          :class="{
            'coverflow-item--active': index === currentIndex,
            'coverflow-item--prev': index < currentIndex,
            'coverflow-item--next': index > currentIndex,
          }"
          :style="getItemStyle(index)"
          @click="onItemClick(video, index)"
        >
          <div class="video-card">
            <div class="video-wrapper">
              <PauseAfterPlayVideo
                class="coverflow-video"
                :sources="video.sources"
                :autoplay="false"
                :controls="index === currentIndex"
                :muted="true"
                :loop="false"
                :preload="'metadata'"
                :responsive="true"
                :fluid="true"
                :playbackRates="[0.5, 1, 1.25, 1.5, 2]"
                :language="'zh-CN'"
                :url="video.sources[0]?.src"
                :useVideoPlayer="true"
              />

              <!-- 视频信息覆盖层 -->
              <div
                class="video-overlay"
                v-if="video.title || video.description"
              >
                <div class="video-info">
                  <h3 v-if="video.title" class="video-title">
                    {{ video.title }}
                  </h3>
                  <p v-if="video.description" class="video-description">
                    {{ video.description }}
                  </p>
                </div>
              </div>

              <!-- 非激活状态的蒙版 -->
              <div v-if="index !== currentIndex" class="video-mask" />
            </div>
          </div>
        </div>
      </div>

      <!-- 指示器 -->
      <div class="coverflow-indicators" v-if="showIndicators">
        <button
          v-for="(_, index) in videoList"
          :key="index"
          class="indicator"
          :class="{ 'indicator--active': index === currentIndex }"
          @click="slideTo(index)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { VideoSource } from '@/components/VideoPlayer/types';
import PauseAfterPlayVideo from '@/components/PauseAfterPlayVideo/index.vue';
import { Icon } from '@/components/Icon';

interface VideoItem {
  sources: VideoSource[];
  title?: string;
  description?: string;
  poster?: string;
}

interface Props {
  videoList: VideoItem[];
  autoplay?: boolean;
  autoplayDelay?: number;
  infinite?: boolean;
  showNavigation?: boolean;
  showIndicators?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  videoList: () => [],
  autoplay: false,
  autoplayDelay: 4000,
  infinite: true,
  showNavigation: true,
  showIndicators: true,
});

const emit = defineEmits<{
  change: [currentIndex: number, video: VideoItem];
  click: [video: VideoItem, index: number];
}>();

// 响应式状态
const currentIndex = ref(0);
const isTransitioning = ref(false);
const containerRef = ref<HTMLElement>();
const stageRef = ref<HTMLElement>();

// 自动播放
let autoplayTimer: number | null = null;

// 计算每个项目的3D变换样式
const getItemStyle = (index: number) => {
  const offset = index - currentIndex.value;
  const absOffset = Math.abs(offset);

  let transform = '';
  let opacity = 1;
  let zIndex = 0;

  if (offset === 0) {
    // 当前激活项目 - 居中显示
    transform = 'translateX(-50%) translateZ(0) rotateY(0deg) scale(1)';
    zIndex = 10;
    opacity = 1;
  } else if (absOffset === 1) {
    // 相邻项目 - 折叠效果
    const rotateY = offset > 0 ? -45 : 45;
    const translateX = offset > 0 ? -30 : -70;
    const translateZ = -150;
    transform = `translateX(${translateX}%) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(0.85)`;
    zIndex = 5;
    opacity = 0.8;
  } else if (absOffset === 2) {
    // 更远的项目 - 更深的折叠
    const rotateY = offset > 0 ? -60 : 60;
    const translateX = offset > 0 ? -20 : -80;
    const translateZ = -300;
    transform = `translateX(${translateX}%) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(0.7)`;
    zIndex = 3;
    opacity = 0.6;
  } else {
    // 最远的项目 - 几乎完全折叠
    const rotateY = offset > 0 ? -75 : 75;
    const translateX = offset > 0 ? -10 : -90;
    const translateZ = -450;
    transform = `translateX(${translateX}%) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(0.6)`;
    zIndex = 1;
    opacity = 0.4;
  }

  return {
    transform,
    opacity,
    zIndex,
    transition: isTransitioning.value
      ? 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      : 'none',
  };
};

// 滑动到指定索引
const slideTo = (index: number) => {
  if (isTransitioning.value || props.videoList.length === 0) return;

  let targetIndex = index;

  if (props.infinite) {
    if (index < 0) {
      targetIndex = props.videoList.length - 1;
    } else if (index >= props.videoList.length) {
      targetIndex = 0;
    }
  } else {
    targetIndex = Math.max(0, Math.min(index, props.videoList.length - 1));
  }

  if (targetIndex === currentIndex.value) return;

  isTransitioning.value = true;
  currentIndex.value = targetIndex;

  setTimeout(() => {
    isTransitioning.value = false;
  }, 600);

  emit('change', targetIndex, props.videoList[targetIndex]);
};

// 导航方法
const next = () => slideTo(currentIndex.value + 1);
const prev = () => slideTo(currentIndex.value - 1);

// 项目点击事件
const onItemClick = (video: VideoItem, index: number) => {
  if (index !== currentIndex.value) {
    slideTo(index);
  } else {
    emit('click', video, index);
  }
};

// 自动播放控制
const startAutoplay = () => {
  if (props.autoplay && !autoplayTimer && props.videoList.length > 1) {
    autoplayTimer = window.setInterval(() => {
      next();
    }, props.autoplayDelay);
  }
};

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer);
    autoplayTimer = null;
  }
};

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowLeft':
      prev();
      break;
    case 'ArrowRight':
      next();
      break;
  }
};

// 生命周期
onMounted(() => {
  startAutoplay();
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  stopAutoplay();
  document.removeEventListener('keydown', handleKeydown);
});

// 监听鼠标进入离开容器
watchEffect(() => {
  const container = containerRef.value;
  if (!container) return;

  const handleMouseEnter = () => stopAutoplay();
  const handleMouseLeave = () => startAutoplay();

  container.addEventListener('mouseenter', handleMouseEnter);
  container.addEventListener('mouseleave', handleMouseLeave);

  return () => {
    container.removeEventListener('mouseenter', handleMouseEnter);
    container.removeEventListener('mouseleave', handleMouseLeave);
  };
});

// 监听videoList变化
watch(
  () => props.videoList,
  (newList) => {
    if (newList.length === 0) {
      currentIndex.value = 0;
    } else if (currentIndex.value >= newList.length) {
      currentIndex.value = 0;
    }
  },
  { immediate: true },
);

// 暴露方法
defineExpose({
  slideTo,
  next,
  prev,
  getCurrentIndex: () => currentIndex.value,
});
</script>

<style scoped lang="scss">
.coverflow-carousel {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
  user-select: none;
  perspective: 1200px;
}

.coverflow-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.coverflow-stage {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coverflow-item {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 350px;
  height: 280px;
  cursor: pointer;
  transform-origin: center center;
  transform-style: preserve-3d;

  .video-card {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 15px 60px rgba(0, 0, 0, 0.4);
    }
  }

  .video-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 20px;
    overflow: hidden;
  }

  .coverflow-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
  }

  .video-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 1.5rem 1rem 1rem;
    color: white;
    transform: translateY(100%);
    transition: transform 0.3s ease;

    .video-title {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
      line-height: 1.2;
    }

    .video-description {
      font-size: 0.75rem;
      opacity: 0.9;
      line-height: 1.3;
    }
  }

  .video-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  // 激活状态
  &--active {
    .video-overlay {
      transform: translateY(0);
    }

    .video-card {
      box-shadow: 0 20px 80px rgba(0, 0, 0, 0.4);
    }
  }

  // 悬停效果
  &:hover:not(.coverflow-item--active) {
    .video-mask {
      opacity: 0.1;
    }
  }
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.3);
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  &.nav-btn-left {
    left: 30px;
  }

  &.nav-btn-right {
    right: 30px;
  }
}

.coverflow-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 15;

  .indicator {
    width: 14px;
    height: 14px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.8);
      transform: scale(1.2);
    }

    &.indicator--active {
      background: rgba(255, 255, 255, 1);
      transform: scale(1.4);
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .coverflow-carousel {
    height: 400px;
    perspective: 800px;
  }

  .coverflow-item {
    width: 280px;
    height: 220px;
  }

  .nav-btn {
    width: 42px;
    height: 42px;
    font-size: 22px;

    &.nav-btn-left {
      left: 15px;
    }

    &.nav-btn-right {
      right: 15px;
    }
  }

  .video-overlay {
    padding: 1rem 0.75rem 0.75rem;

    .video-title {
      font-size: 0.9rem;
    }

    .video-description {
      font-size: 0.7rem;
    }
  }
}

@media (max-width: 480px) {
  .coverflow-item {
    width: 250px;
    height: 200px;
  }

  .nav-btn {
    width: 38px;
    height: 38px;
    font-size: 20px;

    &.nav-btn-left {
      left: 10px;
    }

    &.nav-btn-right {
      right: 10px;
    }
  }
}
</style>
