import { PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue';
import legacy from '@vitejs/plugin-legacy';
// import purgeIcons from 'vite-plugin-purge-icons';
import { configHtmlPlugin } from './html';
import { configPwaConfig } from './pwa';
import { configCompressPlugin } from './compress';
import { configImageminPlugin } from './imagemin';
import Components from 'unplugin-vue-components/vite';
import AutoImport from 'unplugin-auto-import/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import DevTools from 'vite-plugin-vue-devtools';
import presetIcons from '@unocss/preset-icons';
import UnoCSS from 'unocss/vite';
import { presetWind3 } from 'unocss';
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders';
import ElementPlus from 'unplugin-element-plus/vite';
import react from '@vitejs/plugin-react';

export function createVitePlugins(viteEnv: ViteEnv, isBuild: boolean) {
  const {
    VITE_USE_IMAGEMIN,
    VITE_LEGACY,
    VITE_BUILD_COMPRESS,
    VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE,
  } = viteEnv;

  const vitePlugins: (PluginOption | PluginOption[])[] = [
    react({
      include: /\.(jsx|tsx)$/,
    }),
    // have to
    vue(),
    // support name
    // vueJsx(),
    DevTools(),
    UnoCSS({
      presets: [
        presetIcons({
          collections: {
            'my-icons': FileSystemIconLoader(
              './src/assets/svg',
              // , (svg) =>
              // svg
              //   .replace(/#fff/, 'currentColor') // 替换颜色
              //   .replace(/stroke="[^"]*"/g, 'stroke="currentColor"') // 统一描边色
              //   .replace(/fill="[^"]*"/g, 'fill="currentColor"'),
            ),
          },
          customizations: {
            iconCustomizer(collection, _, props) {
              // 自定义特定图标
              if (collection === 'my-icons') {
                props.width = '1.2em';
                props.height = '1.2em';
              }
            },
          },
        }),
        presetWind3(),
      ],
      rules: [['m-1', { margin: '1px' }]],
      theme: {
        boxShadow: {
          xl: [
            'var(--un-shadow-inset) 0px 0px 8px var(--un-shadow-color, rgb(0 0 0 / 0.1))',
            'var(--un-shadow-inset) 0px 0px 8px var(--un-shadow-color, rgb(0 0 0 / 0.1))',
          ].join(', '),
        },
      },
    }),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/, // .md
      ],
      imports: [
        'vue',
        'vue-router',
        {
          '@/hooks/web/useI18n': ['useI18n'],
          '@/hooks/web/useMessage': ['useMessage'],
          '@/hooks/web/useTable': ['useTable'],
          '@/utils/formRules': ['required'],
        },
      ],
      dts: 'src/types/auto-imports.d.ts',
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
    }),
    ElementPlus({
      useSource: true,
    }),
    Components({
      dts: 'src/types/auto-imports-components.d.ts',
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
    }),
  ];

  // @vitejs/plugin-legacy
  VITE_LEGACY && isBuild && vitePlugins.push(legacy());

  // vite-plugin-html
  vitePlugins.push(configHtmlPlugin(viteEnv, isBuild));

  // vite-plugin-purge-icons
  // vitePlugins.push(purgeIcons());

  // The following plugins only work in the production environment
  if (isBuild) {
    // vite-plugin-imagemin
    VITE_USE_IMAGEMIN && vitePlugins.push(configImageminPlugin());

    // rollup-plugin-gzip
    vitePlugins.push(
      configCompressPlugin(
        VITE_BUILD_COMPRESS,
        VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE,
      ),
    );

    // vite-plugin-pwa
    vitePlugins.push(configPwaConfig(viteEnv));
  } else {
    // vitePlugins.push(fullImportPlugin());
  }

  return vitePlugins;
}
