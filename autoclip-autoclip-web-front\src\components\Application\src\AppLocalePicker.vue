<!--
 * @Author: Vben
 * @Description: Multi-language switching component
-->
<template>
  <el-dropdown
    placement="bottom"
    trigger="click"
    @command="handleMenuEvent"
    popper-class="app-locale-picker-overlay"
  >
    <span class="flex cursor-pointer items-center">
      <Icon icon="i-ion:language" />
      <span v-if="showText" class="ml-1">{{ getLocaleText }}</span>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in localeList"
          :key="item.event"
          :command="item"
        >
          <Icon :icon="item.icon" v-if="item.icon" />
          <span class="ml-1">{{ item.text }}</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script lang="ts" setup>
import type { LocaleType } from '#/config';
import type { DropMenu } from '@/components/Dropdown';
import { ref, watchEffect, unref, computed } from 'vue';
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';
import Icon from '@/components/Icon/Icon.vue';
import { useLocale } from '@/locales/useLocale';
import { localeList } from '@/settings/localeSetting';

const props = defineProps({
  /**
   * Whether to display text
   */
  showText: { type: Boolean, default: true },
  /**
   * Whether to refresh the interface when changing
   */
  reload: { type: Boolean },
});

const selectedKeys = ref<string[]>([]);

const { changeLocale, getLocale } = useLocale();

const getLocaleText = computed(() => {
  const key = selectedKeys.value[0];
  if (!key) {
    return '';
  }
  return localeList.find((item) => item.event === key)?.text;
});

watchEffect(() => {
  selectedKeys.value = [unref(getLocale)];
});

async function toggleLocale(lang: LocaleType | string) {
  await changeLocale(lang as LocaleType);
  selectedKeys.value = [lang as string];
  props.reload && location.reload();
}

function handleMenuEvent(menu: DropMenu) {
  if (unref(getLocale) === menu.event) {
    return;
  }
  toggleLocale(menu.event as string);
}
</script>

<style lang="scss" scoped>
.app-locale-picker-overlay {
  .el-dropdown-menu__item {
    min-width: 160px;
  }
}
</style>
