<template>
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm transition-opacity duration-300"
    :class="{ 'pointer-events-none opacity-0': !visible }"
    v-show="visible"
  >
    <transition name="fade" mode="out-in">
      <LoginForm
        v-if="currentForm === FormType.LOGIN"
        @close="closeForm"
        @switch-form="switchForm"
      ></LoginForm>
      <RegisterForm
        v-else-if="currentForm === FormType.REGISTER"
        @close="closeForm"
        @switch-form="switchForm"
      ></RegisterForm>
      <ForgePassword
        v-else-if="currentForm === FormType.FORGEPASSWORD"
        @close="closeForm"
        @switch-form="switchForm"
      ></ForgePassword>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import { FormType } from './useLogin';
import ForgePassword from '@/components/Login/src/ForgePassword.vue';
import LoginForm from '@/components/Login/src/LoginForm.vue';
import RegisterForm from '@/components/Login/src/RegisterForm/index.vue';

interface Props {
  visible: boolean;
}

// Emits 类型定义
type Emits = {
  (event: 'close'): void;
};
const currentForm = ref<FormType>(FormType.LOGIN);
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const switchForm = (formType: FormType) => {
  currentForm.value = formType;
};

// ESC键关闭
onMounted(() => {
  window.addEventListener('keydown', handleEscapeKey);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleEscapeKey);
});

const closeForm = () => {
  if (props.visible) {
    emit('close');
  }
};

const handleEscapeKey = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && props.visible) {
    emit('close');
  }
};
</script>

<style scoped>
/* 可添加自定义样式 */
</style>
