<template>
  <div
    class="flex w-full items-center border-b border-gray-200 bg-gray-50 py-2"
    ref="containerRef"
  >
    <div class="relative h-8">
      <div
        v-for="marker in tickMarkers"
        :key="marker"
        :ref="elRef(marker)"
        class="absolute flex flex-col items-center"
        :style="{
          left: `${marker * timelineZoom}px`,
          width: '1px',
          height: '100%',
        }"
      >
        <!-- Tick line -->
        <div
          :class="[
            'w-px',
            Number.isInteger(marker) && marker !== 0
              ? 'h-7 bg-gray-600'
              : 'h-2 bg-gray-400',
          ]"
        />

        <!-- Second labels -->
        <span
          v-if="Number.isInteger(marker) && marker !== 0"
          class="mt-1 cursor-default text-[10px] font-medium text-gray-600"
        >
          {{ marker }}s
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useProjectStore } from '@/store/editor';

const elRef = (marker: number) => {
  return (el: HTMLDivElement) => {
    if (el instanceof HTMLDivElement) {
      markerRefs.value[marker] = el;
    }
  };
};

const projectStore = useProjectStore();
const containerRef = ref<HTMLDivElement | null>(null);
const markerRefs = ref<Record<number, HTMLDivElement>>({});

const duration = computed(() => projectStore.getDuration);
const currentTime = computed(() => projectStore.getCurrentTime);
const timelineZoom = computed(() => projectStore.getTimelineZoom);
const enableMarkerTracking = computed(
  () => projectStore.getEnableMarkerTracking,
);

const secondInterval = 0.2; // Every 0.2s
const totalSeconds = computed(() => Math.max(duration.value + 2, 61));
const tickMarkers = computed(() =>
  Array.from(
    { length: totalSeconds.value / secondInterval },
    (_, i) => i * secondInterval,
  ),
);

// Watch for time changes to track the marker
watch(currentTime, (newTime) => {
  const roundedTime = Math.floor(newTime);
  const el = markerRefs.value[roundedTime];
  if (el && el.scrollIntoView && enableMarkerTracking.value) {
    el.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest',
    });
  }
});
</script>

<style scoped>
.timeline-header {
  width: 100%;
  overflow-x: auto;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}
</style>
