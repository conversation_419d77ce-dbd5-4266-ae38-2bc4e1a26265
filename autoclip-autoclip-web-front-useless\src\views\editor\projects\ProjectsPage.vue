<script setup lang="ts">
import { useMessage } from '@/hooks/web/useMessage';
import { deleteProject, listProjects, storeProject } from '@/store/editor';
import { useProjectsStore } from '@/store/editor/slices/projectsStore';
import type { ProjectState } from '@/types/editor';
import { storeToRefs } from 'pinia';
import { computed, nextTick, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { buildUUID } from '@/utils/uuid';

const router = useRouter();
const projectsStore = useProjectsStore();
const { $message } = useMessage();

const { projects } = storeToRefs(projectsStore);

const isCreating = ref(false);
const newProjectName = ref('');
const inputRef = ref<HTMLInputElement>();
const isLoading = ref(true);

const sortedProjects = computed(() => {
  const curProjects = [...projects.value].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );
  return curProjects;
});

const loadProjects = async () => {
  isLoading.value = true;
  try {
    const storedProjects = await listProjects();
    projectsStore.rehydrateProjects(storedProjects);
  } catch (error) {
    $message.error('Failed to load projects');
    console.error('Error loading projects:', error);
  } finally {
    isLoading.value = false;
  }
};

const handleCreateProject = async () => {
  if (!newProjectName.value.trim()) return;

  const newProject: ProjectState = {
    id: buildUUID(),
    projectName: newProjectName.value,
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    mediaFiles: [],
    textElements: [],
    currentTime: 0,
    isPlaying: false,
    isMuted: false,
    duration: 0,
    activeSection: 'media',
    activeElement: 'text',
    activeElementIndex: 0,
    filesID: [],
    zoomLevel: 1,
    timelineZoom: 100,
    enableMarkerTracking: true,
    resolution: { width: 1920, height: 1080 },
    fps: 30,
    aspectRatio: '16:9',
    history: [],
    future: [],
    exportSettings: {
      resolution: '1080p',
      quality: 'high',
      speed: 'fastest',
      fps: 30,
      format: 'mp4',
      includeSubtitles: false,
    },
  };

  try {
    await storeProject(newProject);
    projectsStore.addProject(newProject);
    newProjectName.value = '';
    isCreating.value = false;
    $message.success('Project created successfully');
  } catch (error) {
    $message.error('Failed to create project');
    console.error('Error creating project:', error);
  }
};

const handleDeleteProject = async (projectId: string) => {
  try {
    await deleteProject(projectId);
    projectsStore.deleteProject(projectId);

    // Reload projects from DB
    const storedProjects = await listProjects();
    projectsStore.rehydrateProjects(storedProjects);

    $message.success('Project deleted successfully');
  } catch (error) {
    $message.error('Failed to delete project');
    console.error('Error deleting project:', error);
  }
};

const navigateToProject = (projectId: string) => {
  projectsStore.setCurrentProject(projectId);
  router.push(`/editor/projects/${projectId}`);
};

const startCreating = async () => {
  isCreating.value = true;
  await nextTick();
  inputRef.value?.focus();
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    handleCreateProject();
  } else if (e.key === 'Escape') {
    isCreating.value = false;
  }
};

onMounted(() => {
  loadProjects();
});
</script>

<template>
  <div class="min-h-screen w-full bg-white">
    <div>
      <br />
      <br />
      <h2
        class="font-display mx-auto max-w-4xl text-center text-5xl font-medium tracking-tight text-gray-900 sm:text-4xl"
      >
        <span class="inline-block">Projects</span>
      </h2>

      <!-- Loading State -->
      <div
        v-if="isLoading"
        class="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90"
      >
        <div
          class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-6 shadow-lg"
        >
          <div
            class="border-t-opacity-100 h-16 w-16 animate-spin rounded-full border-4 border-r-blue-600 border-t-blue-600 border-opacity-30"
          ></div>
          <p class="mt-4 text-lg text-gray-900">Loading projects...</p>
        </div>
      </div>

      <!-- Projects Grid -->
      <div v-else class="flex items-center justify-center py-12">
        <div
          class="grid w-2/3 grid-cols-1 gap-4 py-4 sm:w-1/2 md:w-1/3 lg:w-1/4 lg:grid-cols-1 lg:gap-5"
        >
          <!-- Add Project Button -->
          <button @click="startCreating" class="group">
            <div
              class="flex transform flex-col gap-4 rounded-lg border border-gray-300 bg-white p-4 shadow-md transition-transform hover:shadow-lg group-hover:scale-105 group-hover:border-gray-400"
            >
              <figure
                class="flex w-full items-center justify-between rounded-full border border-gray-200 bg-gray-50 p-2"
              >
                <div class="flex items-center space-x-4">
                  <div
                    class="flex size-9 items-center justify-center rounded-full bg-gray-100"
                  >
                    <img
                      alt="Add Project"
                      class="opacity-70"
                      height="18"
                      src="https://www.svgrepo.com/show/421119/add-create-new.svg"
                      width="18"
                    />
                  </div>
                  <h5 class="text-lg font-medium text-gray-900">Add Project</h5>
                </div>
              </figure>
            </div>
          </button>

          <!-- List Projects -->
          <div v-for="project in sortedProjects" :key="project.id" class="">
            <button
              @click="navigateToProject(project.id)"
              class="group block h-full w-full"
            >
              <div
                class="flex transform flex-col gap-4 rounded-lg border border-gray-300 bg-white p-4 shadow-md transition-transform hover:shadow-lg group-hover:scale-105 group-hover:border-gray-400"
              >
                <figure
                  class="flex w-full items-center justify-between rounded-full border border-gray-200 bg-gray-50 p-2"
                >
                  <!-- Project Name -->
                  <div class="flex min-w-0 flex-1 items-center space-x-3">
                    <div
                      class="flex size-9 flex-shrink-0 items-center justify-center rounded-full bg-gray-100"
                    >
                      <img
                        :alt="project.projectName"
                        class="opacity-70"
                        height="18"
                        src="https://www.svgrepo.com/show/522461/video.svg"
                        width="18"
                      />
                    </div>
                    <h5
                      class="truncate text-base font-medium text-gray-900 sm:text-lg"
                      :title="project.projectName"
                    >
                      {{ project.projectName }}
                    </h5>
                  </div>
                  <!-- Delete Button -->
                  <button
                    @click.stop="handleDeleteProject(project.id)"
                    class="ml-2 flex-shrink-0 text-red-500 transition-colors hover:text-red-600"
                    aria-label="Delete project"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </button>
                </figure>
                <div class="flex flex-col items-start gap-1 py-1 text-sm">
                  <p class="text-pretty text-gray-600">
                    <span class="font-medium text-gray-900">Created:</span>
                    {{ new Date(project.createdAt).toLocaleDateString() }}
                  </p>
                  <p class="text-pretty text-gray-600">
                    <span class="font-medium text-gray-900"
                      >Last Modified:</span
                    >
                    {{ new Date(project.lastModified).toLocaleDateString() }}
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Project Modal -->
    <div class="container mx-auto px-4 py-8">
      <div
        v-if="isCreating"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
      >
        <div
          class="w-96 rounded-lg border border-gray-300 bg-white p-6 shadow-lg"
        >
          <h3 class="mb-4 text-xl font-bold text-gray-900">
            Create New Project
          </h3>
          <input
            ref="inputRef"
            v-model="newProjectName"
            type="text"
            @keydown="handleKeyDown"
            placeholder="Project Name"
            class="mb-4 w-full rounded border border-gray-300 bg-white p-2 text-gray-900 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div class="flex justify-end gap-2">
            <button
              @click="isCreating = false"
              class="rounded border border-gray-300 bg-gray-100 px-4 py-2 text-gray-700 shadow-sm transition-colors hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              @click="handleCreateProject"
              class="rounded bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
            >
              Create
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
