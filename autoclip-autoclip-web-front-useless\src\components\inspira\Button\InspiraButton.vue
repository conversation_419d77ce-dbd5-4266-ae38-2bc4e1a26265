<!-- 
  Inspira UI 按钮组件
  兼容 Element Plus 按钮 API，提供 Inspira UI 的视觉效果
-->
<template>
  <ErrorBoundary
    fallback-component="el-button"
    :fallback-props="elementPlusProps"
    @error="handleError"
  >
    <!-- 使用 RainbowButton（主要按钮且启用动画时） -->
    <RainbowButton
      v-if="shouldUseRainbow"
      :class="computedClass"
      :speed="speed"
      :disabled="disabled"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <Icon v-if="loading" icon="i-mdi:loading" class="inspira-loading mr-2" />
      <Icon v-else-if="icon && iconPlacement === 'left'" :icon="icon" class="mr-2" />
      <slot name="icon" />
      <slot />
      <Icon v-if="icon && iconPlacement === 'right'" :icon="icon" class="ml-2" />
    </RainbowButton>

    <!-- 使用普通 Inspira 按钮 -->
    <button
      v-else
      :class="computedClass"
      :disabled="disabled || loading"
      :type="nativeType"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <Icon v-if="loading" icon="i-mdi:loading" class="inspira-loading mr-2" />
      <Icon v-else-if="icon && iconPlacement === 'left'" :icon="icon" class="mr-2" />
      <slot name="icon" />
      <slot />
      <Icon v-if="icon && iconPlacement === 'right'" :icon="icon" class="ml-2" />
    </button>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/Icon';
import ErrorBoundary from '../Utils/ErrorBoundary.vue';
import RainbowButton from './RainbowButton.vue';
import type { InspiraButtonProps, InspiraButtonEmits } from './types';

const props = withDefaults(defineProps<InspiraButtonProps>(), {
  type: 'default',
  size: 'default',
  loading: false,
  disabled: false,
  animation: 'hover',
  speed: 2,
  block: false,
  round: false,
  circle: false,
  iconPlacement: 'left',
});

const emit = defineEmits<InspiraButtonEmits>();

// 原生按钮类型
const nativeType = computed(() => {
  return props.type === 'primary' ? 'submit' : 'button';
});

// 决定是否使用 RainbowButton
const shouldUseRainbow = computed(() => {
  return (
    props.type === 'primary' && 
    props.animation === 'rainbow' && 
    !props.loading &&
    !props.disabled
  );
});

// Element Plus 降级 props
const elementPlusProps = computed(() => ({
  type: props.type,
  size: props.size,
  loading: props.loading,
  disabled: props.disabled,
  round: props.round,
  circle: props.circle,
  class: props.class,
}));

// 计算样式类
const computedClass = computed(() => {
  const baseClasses = [
    'inspira-button',
    `inspira-button--${props.size}`,
    `inspira-button--${props.type}`,
  ];

  if (props.block) baseClasses.push('inspira-button--block');
  if (props.round) baseClasses.push('inspira-button--round');
  if (props.circle) baseClasses.push('inspira-button--circle');
  if (props.loading) baseClasses.push('inspira-button--loading');
  if (props.disabled) baseClasses.push('inspira-button--disabled');
  if (props.animation !== 'none') baseClasses.push(`inspira-button--${props.animation}`);

  return cn(baseClasses, props.class);
});

// 事件处理
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

const handleError = (error: Error) => {
  console.warn('InspiraButton error, falling back to el-button:', error);
};
</script>

<style scoped>
.inspira-button {
  @apply inspira-ui;
  
  /* 基础样式 */
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: var(--inspira-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  outline: none;
  user-select: none;
  white-space: nowrap;
  
  /* 类型样式 */
  &--default {
    background-color: var(--inspira-background);
    border-color: var(--inspira-border);
    color: var(--inspira-text);
    
    &:hover:not(:disabled) {
      border-color: var(--inspira-primary);
      color: var(--inspira-primary);
    }
  }
  
  &--primary {
    background-color: var(--inspira-primary);
    border-color: var(--inspira-primary);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--inspira-primary-light);
      border-color: var(--inspira-primary-light);
    }
  }
  
  &--success {
    background-color: #10b981;
    border-color: #10b981;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #059669;
      border-color: #059669;
    }
  }
  
  &--warning {
    background-color: #f59e0b;
    border-color: #f59e0b;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #d97706;
      border-color: #d97706;
    }
  }
  
  &--danger {
    background-color: #ef4444;
    border-color: #ef4444;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #dc2626;
      border-color: #dc2626;
    }
  }
  
  &--info {
    background-color: var(--inspira-accent);
    border-color: var(--inspira-accent);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: #0891b2;
      border-color: #0891b2;
    }
  }
  
  &--text {
    background-color: transparent;
    border-color: transparent;
    color: var(--inspira-primary);
    
    &:hover:not(:disabled) {
      background-color: rgba(99, 102, 241, 0.1);
    }
  }
  
  /* 尺寸样式 */
  &--small {
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
    --button-height: 32px;
  }
  
  &--default {
    height: 40px;
    padding: 0 16px;
    font-size: 16px;
    --button-height: 40px;
  }
  
  &--large {
    height: 48px;
    padding: 0 20px;
    font-size: 18px;
    --button-height: 48px;
  }
  
  /* 状态样式 */
  &--loading {
    pointer-events: none;
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &--block {
    width: 100%;
  }
  
  &--round {
    border-radius: 9999px;
  }
  
  &--circle {
    border-radius: 50%;
    width: var(--button-height, 40px);
    padding: 0;
  }
  
  /* 动画效果 */
  &--hover {
    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  &--click {
    &:active:not(:disabled) {
      transform: scale(0.95);
    }
  }
  
  /* 焦点样式 */
  &:focus-visible {
    outline: 2px solid var(--inspira-primary);
    outline-offset: 2px;
  }
}

/* 加载动画 */
.inspira-loading {
  animation: inspira-spin 1s linear infinite;
}

@keyframes inspira-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>