<template>
  <div class="px-1rem py-1rem md:px-4rem md:py-2rem h-full w-full">
    <el-card class="mx-auto w-full" v-loading="loading">
      <template #header>
        <div
          class="flex flex-row items-start justify-between gap-4 sm:items-center"
        >
          <span class="text-lg font-medium">视频处理记录</span>
          <el-button @click="toggleFilterPanel">
            {{ showFilterPanel ? '隐藏筛选' : '显示筛选' }}
          </el-button>
        </div>
      </template>

      <!-- 过滤面板 -->
      <transition name="fade">
        <div
          v-show="showFilterPanel"
          class="mb-4 p-6 border-b-1"
        >
          <el-form
            :model="filterForm"
            :label-width="getIsMobile ? '80px' : '100px'"
            class="space-y-2"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <el-form-item label="视频名称">
                <el-input
                  :size="getIsMobile ? 'small' : 'default'"
                  v-model="filterForm.videoName"
                  placeholder="请输入视频名称"
                  clearable
                />
              </el-form-item>
              <el-form-item label="处理状态">
                <el-select
                  :size="getIsMobile ? 'small' : 'default'"
                  v-model="filterForm.status"
                  placeholder="请选择状态"
                  clearable
                  class="w-full"
                >
                  <el-option label="准备中" :value="ProcessStatus.PREPARING" />
                  <el-option label="处理中" :value="ProcessStatus.PROCESSING" />
                  <el-option label="已完成" :value="ProcessStatus.COMPLETED" />
                  <el-option label="失败" :value="ProcessStatus.FAILED" />
                </el-select>
              </el-form-item>
              <el-form-item label="运动类型">
                <el-select
                  :size="getIsMobile ? 'small' : 'default'"
                  v-model="filterForm.sportType"
                  placeholder="请选择类型"
                  clearable
                  class="w-full"
                >
                  <el-option label="乒乓球" :value="SportType.PINGPONG" />
                  <el-option label="羽毛球" :value="SportType.BADMINTON" />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  :size="getIsMobile ? 'small' : 'default'"
                  v-model="dateRange"
                  :type="getIsMobile ? 'daterange' : 'datetimerange'"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :format="getIsMobile ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
                  :value-format="
                    getIsMobile ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'
                  "
                  class="w-full"
                  clearable
                />
              </el-form-item>
              <el-form-item label="最小时长">
                <el-input-number
                  :size="getIsMobile ? 'small' : 'default'"
                  v-model="filterForm.minVideoDuration"
                  placeholder="最小时长(秒)"
                  :min="0"
                  class="w-full"
                />
              </el-form-item>
              <el-form-item label="最大时长">
                <el-input-number
                  :size="getIsMobile ? 'small' : 'default'"
                  v-model="filterForm.maxVideoDuration"
                  placeholder="最大时长(秒)"
                  :min="0"
                  class="w-full"
                />
              </el-form-item>
            </div>

            <div class="flex flex-col justify-center gap-3 sm:flex-row">
              <el-button @click="handleFilter" class="w-full sm:w-auto"
                >筛选</el-button
              >
              <el-button @click="handleReset" class="w-full sm:w-auto"
                >重置</el-button
              >
              <el-button @click="toggleFilterPanel" class="w-full sm:w-auto"
                >取消</el-button
              >
            </div>
          </el-form>
        </div>
      </transition>

      <div class="-mx-2 w-full overflow-x-auto md:mx-0">
        <el-table
          :data="records"
          :default-sort="{ prop: 'createTime', order: 'descending' }"
          v-loading="loading"
          :table-layout="getIsMobile ? 'fixed' : 'auto'"
          class="w-full min-w-full text-sm md:text-base"
        >
          <template #empty>
            <el-empty description="暂无数据" class="w-full py-8">
              <template #description>
                <div class="mb-4 text-gray-500">暂无数据</div>
                <el-button
                  type="primary"
                  @click="handleGoClip"
                  class="w-full sm:w-auto"
                >
                  去剪辑?
                </el-button>
              </template>
            </el-empty>
          </template>
          <el-table-column
            prop="videoName"
            label="视频名称"
            :width="getIsMobile ? 120 : 140"
            show-overflow-tooltip
            :fit="false"
            align="center"
          />
          <el-table-column
            label="处理状态"
            :min-width="getIsMobile ? 80 : 240"
            align="center"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="视频类型"
            :min-width="getIsMobile ? 80 : 240"
            align="center"
          >
            <template #default="{ row }">
              {{ row.sportType === SportType.PINGPONG ? '乒乓球' : '羽毛球' }}
            </template>
          </el-table-column>
          <el-table-column
            sortable
            prop="progress"
            label="处理进度"
            :width="getIsMobile ? 100 : 200"
            align="center"
          >
            <template #default="{ row }">
              <el-progress
                :percentage="Math.min(Math.max(row.progress * 100, 0), 100)"
                :status="getProgressStatus(row.status)"
                :stroke-width="getIsMobile ? 8 : 6"
                :show-text="!getIsMobile"
              />
            </template>
          </el-table-column>
          <el-table-column
            sortable
            prop="extraInfo"
            label="额外信息"
            :min-width="getIsMobile ? 80 : 120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag type="info" v-if="row.extraInfo">
                {{ row.extraInfo }}
              </el-tag>
              <span class="color-gray-500" v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column
            sortable
            prop="videoDuration"
            label="视频时长(秒)"
            :min-width="getIsMobile ? 80 : 120"
            align="center"
          />
          <el-table-column
            label="剪辑配置"
            :min-width="getIsMobile ? 80 : 180"
            align="center"
          >
            <template #default="{ row }">
              <el-link
                v-if="row.videoClipConfigReqVo"
                type="primary"
                @click="handleVideoClipConfig(row.videoClipConfigReqVo)"
                >查看</el-link
              >
              <span class="color-gray-500" v-else>暂无</span>
            </template>
          </el-table-column>
          <el-table-column
            label="输入视频"
            :min-width="getIsMobile ? 60 : 80"
            align="center"
          >
            <template #default="{ row }">
              <el-link
                v-if="row.inputVideoId"
                type="primary"
                @click="handleInputVideo(row.inputVideoId)"
                >查看</el-link
              >
              <span class="color-gray-500" v-else>暂无</span>
            </template>
          </el-table-column>
          <el-table-column
            label="输出视频"
            :min-width="getIsMobile ? 60 : 80"
            align="center"
          >
            <template #default="{ row }">
              <el-link
                v-if="row.outputVideoId"
                type="primary"
                @click="handleOutputVideo(row.outputVideoId)"
                >查看</el-link
              >
              <span class="color-gray-500" v-else>暂无</span>
            </template>
          </el-table-column>

          <el-table-column
            sortable
            prop="createTime"
            label="创建时间"
            align="center"
          >
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="mt-6 flex justify-end">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="getIsMobile ? [10, 20] : [10, 20, 50, 100]"
          :total="pagination.total"
          :layout="
            getIsMobile
              ? 'total, sizes, prev, pager, next'
              : 'total, sizes, prev, pager, next'
          "
          :size="getIsMobile ? 'small' : 'default'"
          class="flex flex-wrap justify-center gap-2"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 输入视频对话框 -->
    <VideoInfoDialog
      v-model="inputVideoDialogVisible"
      title="输入视频"
      :video-info="currentInputVideo"
    />

    <!-- 输出视频对话框 -->
    <VideoInfoDialog
      v-model="outputVideoDialogVisible"
      title="输出视频"
      :video-info="currentOutputVideo"
    />

    <!-- 视频配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="视频处理配置"
      :width="getIsMobile ? '95%' : '60%'"
      destroy-on-close
    >
      <div v-if="currentConfig" class="p-4">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="模式">
            {{ ModeDescriptionMapping[currentConfig.mode] }}
          </el-descriptions-item>
          <el-descriptions-item label="比赛类型">
            {{ MatchTypeDescriptionMapping[currentConfig.matchType] }}
          </el-descriptions-item>
          <el-descriptions-item label="精彩球剪辑">
            {{ currentConfig.greatBallEditing ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="移除回放">
            {{ currentConfig.removeReplay ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="获取比赛片段">
            {{ currentConfig.getMatchSegments ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="
              'maxFireBallTime' in currentConfig &&
              currentConfig.maxFireBallTime != null
            "
            label="最大发球时间"
          >
            {{ currentConfig.maxFireBallTime }}秒
          </el-descriptions-item>
          <el-descriptions-item
            v-if="
              'mergeFireBallAndPlayBall' in currentConfig &&
              currentConfig.mergeFireBallAndPlayBall != null
            "
            label="合并发球和击球"
          >
            {{ currentConfig.mergeFireBallAndPlayBall ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="单回合前保留时间">
            {{ currentConfig.reserveTimeBeforeSingleRound }}秒
          </el-descriptions-item>
          <el-descriptions-item label="单回合后保留时间">
            {{ currentConfig.reserveTimeAfterSingleRound }}秒
          </el-descriptions-item>
          <el-descriptions-item label="单回合最小时长">
            {{ currentConfig.minimumDurationSingleRound }}秒
          </el-descriptions-item>
          <el-descriptions-item label="精彩球最小时长">
            {{ currentConfig.minimumDurationGreatBall }}秒
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import type {
  BadmintonVideoClipConfigReqVo,
  PingPongVideoClipConfigReqVo,
  VideoInfoRespVO,
  VideoProcessRecordVO,
} from '@/api/autoclip/auto_clip';
import {
  getVideoInfoRespVO,
  getVideoProcessRecords,
  MatchTypeDescriptionMapping,
  ModeDescriptionMapping,
  ProcessStatus,
  SportType,
} from '@/api/autoclip/auto_clip';
import VideoInfoDialog from '@/components/VideoInfoDialog/index.vue';
import { useAppInject } from '@/hooks/web/useAppInject';
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const loading = ref(false);
const records = ref<VideoProcessRecordVO[]>([]);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 过滤相关
const showFilterPanel = ref(false);
const dateRange = ref<[string, string] | undefined>(undefined);
const filterForm = ref({
  videoName: '',
  status: undefined as number | undefined,
  sportType: undefined as number | undefined,
  createTimeStart: '',
  createTimeEnd: '',
  minVideoDuration: undefined as number | undefined,
  maxVideoDuration: undefined as number | undefined,
  minProgress: undefined as number | undefined,
  maxProgress: undefined as number | undefined,
});

const { getIsMobile } = useAppInject();

// 对话框控制
const inputVideoDialogVisible = ref(false);
const outputVideoDialogVisible = ref(false);
const configDialogVisible = ref(false);

// 当前选中的数据
const currentInputVideo = ref<VideoInfoRespVO | null>(null);
const currentOutputVideo = ref<VideoInfoRespVO | null>(null);
const currentConfig = ref<
  BadmintonVideoClipConfigReqVo | PingPongVideoClipConfigReqVo | null
>(null);

const handleInputVideo = async (inputVideoId: number) => {
  try {
    const response = await getVideoInfoRespVO(inputVideoId);
    currentInputVideo.value = response;
    inputVideoDialogVisible.value = true;
  } catch (error) {
    console.error('Failed to load input video:', error);
  }
};

const handleOutputVideo = async (outputVideoId: number) => {
  try {
    const response = await getVideoInfoRespVO(outputVideoId);
    currentOutputVideo.value = response;
    outputVideoDialogVisible.value = true;
  } catch (error) {
    console.error('Failed to load output video:', error);
  }
};

const handleVideoClipConfig = (
  videoClipConfigReqVo:
    | BadmintonVideoClipConfigReqVo
    | PingPongVideoClipConfigReqVo,
) => {
  currentConfig.value = videoClipConfigReqVo;
  configDialogVisible.value = true;
};

const router = useRouter();
const handleGoClip = () => {
  router.push({ path: '/auto_clip' });
};

const getStatusType = (
  status: ProcessStatus,
): 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap = {
    [ProcessStatus.PREPARING]: 'info',
    [ProcessStatus.PROCESSING]: 'warning',
    [ProcessStatus.COMPLETED]: 'success',
    [ProcessStatus.FAILED]: 'danger',
  };
  return typeMap[status] as 'success' | 'warning' | 'info' | 'danger';
};

const getProgressStatus = (status: ProcessStatus) => {
  if (status === ProcessStatus.FAILED) return 'exception';
  if (status === ProcessStatus.COMPLETED) return 'success';
  if (status === ProcessStatus.PROCESSING) return 'warning';
  return '';
};

const getStatusText = (status: ProcessStatus) => {
  const textMap = {
    [ProcessStatus.PREPARING]: '准备中',
    [ProcessStatus.PROCESSING]: '处理中',
    [ProcessStatus.COMPLETED]: '已完成',
    [ProcessStatus.FAILED]: '失败',
  };
  return textMap[status] || '未知';
};

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

const loadRecords = async () => {
  loading.value = true;
  try {
    // 构建过滤参数
    const params: any = {
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize,
    };

    // 添加过滤条件
    if (filterForm.value.videoName) {
      params.videoName = filterForm.value.videoName;
    }
    if (filterForm.value.status !== undefined) {
      params.status = filterForm.value.status;
    }
    if (filterForm.value.sportType !== undefined) {
      params.sportType = filterForm.value.sportType;
    }
    if (filterForm.value.createTimeStart) {
      params.createTimeStart = filterForm.value.createTimeStart;
    }
    if (filterForm.value.createTimeEnd) {
      params.createTimeEnd = filterForm.value.createTimeEnd;
    }
    if (filterForm.value.minVideoDuration !== undefined) {
      params.minVideoDuration = filterForm.value.minVideoDuration;
    }
    if (filterForm.value.maxVideoDuration !== undefined) {
      params.maxVideoDuration = filterForm.value.maxVideoDuration;
    }
    if (filterForm.value.minProgress !== undefined) {
      params.minProgress = filterForm.value.minProgress;
    }
    if (filterForm.value.maxProgress !== undefined) {
      params.maxProgress = filterForm.value.maxProgress;
    }

    const response = await getVideoProcessRecords(params);
    records.value = response.list;
    pagination.value.total = response.total || 0;
  } catch (error) {
    console.error('Failed to load records:', error);
  } finally {
    loading.value = false;
  }
};

// 过滤相关方法
const toggleFilterPanel = () => {
  showFilterPanel.value = !showFilterPanel.value;
};

const handleFilter = () => {
  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    filterForm.value.createTimeStart = dateRange.value[0];
    filterForm.value.createTimeEnd = dateRange.value[1];
  } else {
    filterForm.value.createTimeStart = '';
    filterForm.value.createTimeEnd = '';
  }

  // 重置到第一页
  pagination.value.current = 1;
  loadRecords();
};

const handleReset = () => {
  filterForm.value = {
    videoName: '',
    status: undefined,
    sportType: undefined,
    createTimeStart: '',
    createTimeEnd: '',
    minVideoDuration: undefined,
    maxVideoDuration: undefined,
    minProgress: undefined,
    maxProgress: undefined,
  };
  dateRange.value = undefined;
  pagination.value.current = 1;
  loadRecords();
};

const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val;
  loadRecords();
};

const handleCurrentChange = (val: number) => {
  pagination.value.current = val;
  loadRecords();
};

onMounted(() => {
  loadRecords();
});
</script>

<style scoped>
:deep(.el-progress__text) {
  text-align: left;
  margin-left: 10px;
}

:deep(.el-link__inner) {
  cursor: pointer;
  border-bottom: 1px solid #409eff;
}
:deep(.el-input-number) {
  width: 100%;
}
:deep(.el-range-editor.el-input__wrapper) {
  padding: 0;
  padding-left: 5px;
}

/* 移动端表格样式 */
@media (max-width: 768px) {
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table__header th) {
    padding: 8px 4px;
    font-size: 12px;
  }

  :deep(.el-table__body td) {
    padding: 8px 4px;
    font-size: 12px;
  }
}
</style>
