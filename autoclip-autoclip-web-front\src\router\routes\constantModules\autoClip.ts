import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

export const autoClip: AppRouteModule = {
  path: '/auto_clip',
  name: 'AutoClip',
  component: LAYOUT,
  redirect: '/auto_clip/ping_pong',
  meta: { title: '自动剪辑', icon: 'login', hideFooter: true, hideMenu: false },
  children: [
    {
      path: '/auto_clip/ping_pong',
      name: 'AutoClipPingPong',
      component: () => import('@/views/autoclip/pingpong/index.vue'),
      meta: {
        hideMenu: false,
        title: '乒乓球',
      },
    },
    {
      path: '/auto_clip/badminton',
      name: 'AutoClipBadminton',
      component: () => import('@/views/autoclip/badminton/index.vue'),
      meta: {
        hideMenu: false,
        title: '羽毛球',
      },
    },
  ],
};
export default autoClip;
