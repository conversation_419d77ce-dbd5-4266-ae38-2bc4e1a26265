<!-- 
  示例：Inspira UI 按钮组件包装器
  目标：保持与 el-button 完全相同的 API，同时提供 Inspira UI 的视觉效果
-->
<template>
  <!-- 使用 Inspira UI 的 RainbowButton（主要按钮且无错误时） -->
  <RainbowButton
    v-if="shouldUseInspira"
    :class="computedClass"
    :disabled="disabled"
    @click="handleClick"
  >
    <Icon v-if="loading" icon="i-mdi:loading" class="animate-spin mr-2" />
    <slot />
  </RainbowButton>

  <!-- 降级到 Element Plus 按钮 -->
  <el-button
    v-else
    :type="type"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :class="computedClass"
    @click="handleClick"
  >
    <slot />
  </el-button>
</template>

<script setup lang="ts">
import { computed, ref, onErrorCaptured } from 'vue';
import { RainbowButton } from '@/components/inspira';
import { Icon } from '@/components/Icon';

// 保持与 el-button 完全相同的 Props 接口
interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
  size?: 'large' | 'default' | 'small';
  loading?: boolean;
  disabled?: boolean;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default',
  loading: false,
  disabled: false,
});

// 保持与 el-button 相同的事件
const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

// 错误状态管理
const hasError = ref(false);

// 错误捕获 - 如果 Inspira 组件出错，自动降级到 Element Plus
onErrorCaptured((error) => {
  console.warn('Inspira Button component error, falling back to Element Plus:', error);
  hasError.value = true;
  return false; // 阻止错误继续传播
});

// 决定是否使用 Inspira UI
const shouldUseInspira = computed(() => {
  return (
    !hasError.value && 
    props.type === 'primary' && // 只有主要按钮使用 RainbowButton
    !props.loading // 加载状态时使用 Element Plus（更稳定）
  );
});

// 计算样式类
const computedClass = computed(() => {
  const baseClass = props.class || '';
  
  if (shouldUseInspira.value) {
    // Inspira UI 按钮的样式调整
    return `${baseClass} inspira-button`;
  } else {
    // Element Plus 按钮保持原有样式
    return baseClass;
  }
});

// 点击事件处理
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>

<style scoped>
/* Inspira UI 按钮的额外样式调整 */
.inspira-button {
  /* 确保与 Element Plus 按钮尺寸一致 */
  min-height: var(--el-component-size);
  
  /* 保持与项目主题的一致性 */
  --color-1: var(--el-color-primary);
  --color-2: var(--el-color-primary-light-3);
  --color-3: var(--el-color-primary-light-5);
  --color-4: var(--el-color-primary-light-7);
  --color-5: var(--el-color-primary-light-9);
}

/* 加载状态的图标动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>