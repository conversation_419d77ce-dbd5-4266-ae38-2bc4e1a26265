FROM node:22-slim AS builder

# --max-old-space-size
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NODE_OPTIONS=--max-old-space-size=8192
ENV TZ=Asia/Shanghai

RUN npm i -g corepack

WORKDIR /app

COPY ./package.json /app

COPY ./pnpm-lock.yaml /app

RUN pnpm install --frozen-lockfile || exit 1

ADD autoclip-web-front.tar.gz /app

RUN pnpm run build || exit 1

RUN echo "Builder Success 🎉"

FROM nginx:stable-alpine AS production

RUN rm -rf /etc/nginx/conf.d/default.conf

COPY --from=builder /app/dist /usr/share/nginx/html

COPY --from=builder /app/docker/internal/default.conf /etc/nginx/nginx.conf

EXPOSE 6584

CMD ["nginx", "-g", "daemon off;"]
