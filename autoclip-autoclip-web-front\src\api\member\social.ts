import { defHttp } from '@/utils/http/axios';

const SocialApi = {
  // 获得社交用户
  getSocialUser: (type) => {
    return defHttp.request({
      url: '/member/social-user/get',
      method: 'GET',
      params: {
        type,
      },
    });
  },
  // 社交绑定
  socialBind: (type, code, state) => {
    return defHttp.request({
      url: '/member/social-user/bind',
      method: 'POST',
      data: {
        type,
        code,
        state,
      },
    });
  },
  // 社交绑定
  socialUnbind: (type, openid) => {
    return defHttp.request({
      url: '/member/social-user/unbind',
      method: 'DELETE',
      data: {
        type,
        openid,
      },
    });
  },
  // 获取订阅消息模板列表
  getSubscribeTemplateList: () =>
    defHttp.request({
      url: '/member/social-user/get-subscribe-template-list',
      method: 'GET',
    }),
  // 获取微信小程序码
  getWxaQrcode: async (path, query) => {
    return await defHttp.request({
      url: '/member/social-user/wxa-qrcode',
      method: 'POST',
      data: {
        scene: query,
        path,
        checkPath: false, // TODO 开发环境暂不检查 path 是否存在
      },
    });
  },
};

export default SocialApi;
