<script setup lang="ts">
import { Location, Menu as IconMenu } from '@element-plus/icons-vue';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';

const { getBasicTransition } = useTransitionSetting();
</script>

<template>
  <transition :name="getBasicTransition" mode="out-in" appear>
    <el-menu
      class="h-full bg-white max-sm:!fixed"
      :router="true"
      mode="vertical"
    >
      <el-menu-item index="/user/profile/admin">
        <el-icon>
          <location />
        </el-icon>
        <span>基本信息</span>
      </el-menu-item>
      <el-menu-item index="/user/profile/security">
        <el-icon>
          <icon-menu />
        </el-icon>
        <span>安全设置</span>
      </el-menu-item>
    </el-menu>
  </transition>
</template>

<style scoped lang="scss"></style>
