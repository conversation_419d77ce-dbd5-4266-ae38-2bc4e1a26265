<!-- 
  示例：Vue 错误边界组件
  目标：捕获 Inspira UI 组件的错误，自动降级到 Element Plus 组件
-->
<template>
  <div class="error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误时渲染降级组件 -->
    <component
      v-else
      :is="fallbackComponent"
      v-bind="fallbackProps"
      @click="handleFallbackClick"
    >
      <slot />
    </component>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, computed } from 'vue';

interface Props {
  // 降级组件名称（如 'el-button'）
  fallbackComponent: string;
  // 传递给降级组件的 props
  fallbackProps?: Record<string, any>;
  // 是否在控制台显示错误信息
  showError?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  fallbackProps: () => ({}),
  showError: true,
});

const emit = defineEmits<{
  error: [error: Error];
  fallback: [componentName: string];
  click: [event: MouseEvent];
}>();

// 错误状态
const hasError = ref(false);
const lastError = ref<Error | null>(null);

// 错误捕获处理
onErrorCaptured((error: Error, instance, info) => {
  hasError.value = true;
  lastError.value = error;
  
  if (props.showError) {
    console.warn(`Component error caught by ErrorBoundary:`, {
      error: error.message,
      component: instance?.type?.name || 'Unknown',
      info,
      fallbackTo: props.fallbackComponent
    });
  }
  
  // 发出错误事件
  emit('error', error);
  emit('fallback', props.fallbackComponent);
  
  // 阻止错误继续传播
  return false;
});

// 降级组件的点击处理
const handleFallbackClick = (event: MouseEvent) => {
  emit('click', event);
};

// 重置错误状态的方法（可以通过 ref 调用）
const resetError = () => {
  hasError.value = false;
  lastError.value = null;
};

// 暴露方法给父组件
defineExpose({
  resetError,
  hasError: computed(() => hasError.value),
  lastError: computed(() => lastError.value),
});
</script>

<style scoped>
.error-boundary {
  /* 确保错误边界不影响布局 */
  display: contents;
}
</style>