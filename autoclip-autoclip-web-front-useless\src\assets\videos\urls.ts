export interface RemoteVideoInfo {
  src: string;
  name: string;
  type: string;
  post: string;
}

const server_url = 'https://restcut-1258599900.cos.ap-guangzhou.myqcloud.com';

const FAN_ZHEN_DONG_VS_MA_LONG: RemoteVideoInfo = {
  src: `${server_url}/public/FanZhenDong_VS_MaLong.mp4`,
  name: 'FanZhenDong_VS_MaLong',
  type: 'video/mp4',
  post: `${server_url}/public_frame/malong_fanzhendong.png`,
};

const badminton_example_videos: Record<string, RemoteVideoInfo> = {
  BLUE: {
    src: `${server_url}/public/blue.mp4`,
    name: 'blue',
    type: 'video/mp4',
    post: `${server_url}/public_frame/blue.png`,
  },
  GREEN: {
    src: `${server_url}/public/green.mp4`,
    name: 'green',
    type: 'video/mp4',
    post: `${server_url}/public_frame/green.png`,
  },
  RED: {
    src: `${server_url}/public/red.mp4`,
    name: 'red',
    type: 'video/mp4',
    post: `${server_url}/public_frame/red.png`,
  },
  YELLOW: {
    src: `${server_url}/public/yellow.mp4`,
    name: 'yellow',
    type: 'video/mp4',
    post: `${server_url}/public_frame/yellow.png`,
  },
  GREEN2: {
    src: `${server_url}/public/green2.mp4`,
    name: 'green2',
    type: 'video/mp4',
    post: `${server_url}/public_frame/green2.png`,
  },
};

const pingpong_example_videos: Record<string, RemoteVideoInfo> = {
  HS: {
    src: `${server_url}/public/hs.mp4`,
    name: 'hs',
    type: 'video/mp4',
    post: `${server_url}/public_frame/sx_hh.png`,
  },

  HH_MHY: {
    src: `${server_url}/public/hh-mhy_.mp4`,
    name: 'hh-mhy_',
    type: 'video/mp4',
    post: `${server_url}/public_frame/mhy_hh.png`,
  },
  WCQ_ZB: {
    src: `${server_url}/public/wcq_zb_.mp4`,
    name: 'wcq_zb_',
    type: 'video/mp4',
    post: `${server_url}/public_frame/wcq_zb.png`,
  },
};

export const getPingPongRandomSortVideos: (
  count: number,
) => RemoteVideoInfo[] = (count: number) => {
  const videoUrls = Object.values(pingpong_example_videos);
  const shuffled = [...videoUrls].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getBadmintonRandomSortVideos: (
  count: number,
) => RemoteVideoInfo[] = (count: number) => {
  const videoUrls = Object.values(badminton_example_videos);
  const shuffled = [...videoUrls].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export {
  FAN_ZHEN_DONG_VS_MA_LONG,
  pingpong_example_videos,
  badminton_example_videos,
};
