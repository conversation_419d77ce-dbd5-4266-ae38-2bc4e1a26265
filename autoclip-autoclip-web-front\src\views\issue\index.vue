<template>
  <div class="issue-page w-full bg-gray-50">
    <!-- 页面头部 -->
    <div class="border-b bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              {{ t('routes.issue.issue') }}
            </h1>
            <p class="mt-1 text-sm text-gray-500">
              {{ t('routes.issue.submitFeedback') }}，我们帮您解决问题
            </p>
          </div>
          <el-button
            type="primary"
            @click="showCreateDialog = true"
            class="flex items-center"
          >
            <el-icon class="mr-2"><Plus /></el-icon>
            {{ t('routes.issue.submitFeedback') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <!-- 筛选栏 -->
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <div class="flex flex-wrap items-center gap-4">
          <el-select
            v-model="filterForm.type"
            :placeholder="t('routes.issue.issueType')"
            clearable
            class="w-40"
          >
            <el-option :label="t('routes.issue.bug')" :value="1" />
            <el-option :label="t('routes.issue.requirement')" :value="2" />
          </el-select>
          <el-select
            v-model="filterForm.status"
            :placeholder="t('routes.issue.issueStatus')"
            clearable
            class="w-40"
          >
            <el-option :label="t('routes.issue.pending')" :value="1" />
            <el-option :label="t('routes.issue.processing')" :value="2" />
            <el-option :label="t('routes.issue.resolved')" :value="3" />
            <el-option :label="t('routes.issue.closed')" :value="4" />
          </el-select>

          <el-input
            v-model="filterForm.keyword"
            :placeholder="t('routes.issue.searchPlaceholder')"
            class="w-64"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="loadIssues">{{
            t('routes.issue.search')
          }}</el-button>
          <el-button @click="resetFilter">{{
            t('routes.issue.reset')
          }}</el-button>
        </div>
      </div>

      <!-- 问题列表 -->
      <div class="rounded-lg bg-white shadow-sm">
        <div class="border-b p-6">
          <h2 class="text-lg font-semibold text-gray-900">
            {{ t('routes.issue.myIssues') }}
          </h2>
        </div>

        <div v-if="loading" class="p-8 text-center">
          <el-icon class="animate-spin text-2xl text-blue-500"
            ><Loading
          /></el-icon>
          <p class="mt-2 text-gray-500">{{ t('routes.issue.loading') }}</p>
        </div>

        <div v-else-if="issues.length === 0" class="p-8 text-center">
          <el-icon class="mb-4 text-4xl text-gray-300"><Document /></el-icon>
          <p class="text-gray-500">{{ t('routes.issue.noIssues') }}</p>
          <el-button
            type="primary"
            @click="showCreateDialog = true"
            class="mt-4"
          >
            {{ t('routes.issue.submitFirstFeedback') }}
          </el-button>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="issue in issues"
            :key="issue.id"
            class="cursor-pointer p-6 transition-colors hover:bg-gray-50"
            @click="showIssueDetail(issue)"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="mb-2 flex items-center gap-3">
                  <h3 class="text-lg font-medium text-gray-900">
                    {{ issue.title }}
                  </h3>
                  <el-tag :type="getStatusType(issue.status)" size="small">
                    {{ issue.statusName }}
                  </el-tag>
                  <el-tag
                    :type="issue.type === 1 ? 'danger' : 'warning'"
                    size="small"
                  >
                    {{ issue.typeName }}
                  </el-tag>
                </div>
                <p class="line-clamp-2 text-sm text-gray-600">
                  {{ issue.description }}
                </p>
                <div class="mt-3 flex items-center gap-4 text-xs text-gray-500">
                  <span
                    >{{ t('routes.issue.createTime') }}:
                    {{ formatDate(issue.createTime) }}</span
                  >
                  <span v-if="issue.videoName"
                    >{{ t('routes.issue.relatedVideoName') }}:
                    {{ issue.videoName }}</span
                  >
                </div>
              </div>
              <el-icon class="text-gray-400"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建问题对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="t('routes.issue.submitIssue')"
      :width="isMobile ? '90%' : '600px'"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item :label="t('routes.issue.issueTitle')" prop="title">
          <el-input
            v-model="createForm.title"
            :placeholder="t('routes.issue.issueTitle')"
          />
        </el-form-item>

        <el-form-item :label="t('routes.issue.issueType')" prop="type">
          <el-radio-group v-model="createForm.type">
            <el-radio :value="1">{{ t('routes.issue.bug') }}</el-radio>
            <el-radio :value="2">{{ t('routes.issue.requirement') }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          :label="t('routes.issue.issueDescription')"
          prop="description"
        >
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="4"
            :placeholder="t('routes.issue.issueDescription')"
          />
        </el-form-item>

        <el-form-item :label="t('routes.issue.relatedVideo')" prop="videoId">
          <el-input
            v-model="createForm.videoId"
            :placeholder="t('routes.issue.relatedVideo') + '（可选）'"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="showCreateDialog = false">{{
            t('routes.issue.cancel')
          }}</el-button>
          <el-button type="primary" @click="submitIssue" :loading="submitting">
            {{ t('routes.issue.submit') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 问题详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="t('routes.issue.issueDetail')"
      :width="isMobile ? '90%' : '800px'"
      :close-on-click-modal="false"
    >
      <div v-if="currentIssue" class="space-y-6">
        <!-- 基本信息 -->
        <div class="rounded-lg bg-gray-50 p-4">
          <h3 class="mb-3 text-lg font-semibold">
            {{ t('routes.issue.basicInfo') }}
          </h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500">{{ t('routes.issue.title') }}:</span>
              <span class="ml-2 font-medium">{{ currentIssue.title }}</span>
            </div>
            <div>
              <span class="text-gray-500">{{ t('routes.issue.type') }}:</span>
              <el-tag
                :type="currentIssue.type === 1 ? 'danger' : 'warning'"
                size="small"
                class="ml-2"
              >
                {{ currentIssue.typeName }}
              </el-tag>
            </div>
            <div>
              <span class="text-gray-500">{{ t('routes.issue.status') }}:</span>
              <el-tag
                :type="getStatusType(currentIssue.status)"
                size="small"
                class="ml-2"
              >
                {{ currentIssue.statusName }}
              </el-tag>
            </div>
            <div>
              <span class="text-gray-500"
                >{{ t('routes.issue.createTime') }}:</span
              >
              <span class="ml-2">{{
                formatDate(currentIssue.createTime)
              }}</span>
            </div>

            <div v-if="currentIssue.videoName">
              <span class="text-gray-500"
                >{{ t('routes.issue.relatedVideoName') }}:</span
              >
              <span class="ml-2">{{ currentIssue.videoName }}</span>
            </div>
          </div>
        </div>

        <!-- 问题描述 -->
        <div>
          <h3 class="mb-3 text-lg font-semibold">
            {{ t('routes.issue.description') }}
          </h3>
          <div class="rounded-lg bg-gray-50 p-4">
            <p class="whitespace-pre-wrap text-gray-700">
              {{ currentIssue.description }}
            </p>
          </div>
        </div>

        <!-- 关闭信息 -->
        <div v-if="currentIssue.closedTime">
          <h3 class="mb-3 text-lg font-semibold">
            {{ t('routes.issue.closeInfo') }}
          </h3>
          <div class="rounded-lg bg-gray-50 p-4">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500"
                  >{{ t('routes.issue.closeTime') }}:</span
                >
                <span class="ml-2">{{
                  formatDate(currentIssue.closedTime)
                }}</span>
              </div>
              <div v-if="currentIssue.closedByName">
                <span class="text-gray-500"
                  >{{ t('routes.issue.closedBy') }}:</span
                >
                <span class="ml-2">{{ currentIssue.closedByName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="showDetailDialog = false">{{
            t('routes.issue.close')
          }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage} from 'element-plus';
import {
  Plus,
  Search,
  Loading,
  Document,
  ArrowRight,
} from '@element-plus/icons-vue';
import { useI18n } from '@/hooks/web/useI18n';
import IssueApi from '@/api/issue/index';
import type { IssueRespVO, IssueCreateReqVO } from '@/types/issue';
import { useWindowSize } from '@vueuse/core';

const { t } = useI18n();

const { width } = useWindowSize();
const isMobile = computed(() => width.value < 768);

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const issues = ref<IssueRespVO[]>([]);
const currentIssue = ref<IssueRespVO | null>(null);
const createFormRef = ref();

// 筛选表单
const filterForm = reactive({
  type: undefined as number | undefined,
  status: undefined as number | undefined,
  keyword: '',
});

// 创建表单
const createForm = reactive<IssueCreateReqVO>({
  title: '',
  description: '',
  type: 1,
  videoId: undefined,
});

// 表单验证规则
const createRules = {
  title: [
    {
      required: true,
      message: t('routes.issue.issueTitle') + '不能为空',
      trigger: 'blur',
    },
    {
      min: 2,
      max: 100,
      message: '标题长度在 2 到 100 个字符',
      trigger: 'blur',
    },
  ],
  description: [
    {
      required: true,
      message: t('routes.issue.issueDescription') + '不能为空',
      trigger: 'blur',
    },
    {
      min: 10,
      max: 2000,
      message: '描述长度在 10 到 2000 个字符',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: t('routes.issue.issueType') + '不能为空',
      trigger: 'change',
    },
  ],
};

// 获取问题列表
const loadIssues = async () => {
  loading.value = true;
  try {
    // 只传递有值的参数
    const params: any = {};
    if (filterForm.type) params.type = filterForm.type;
    if (filterForm.status) params.status = filterForm.status;
    if (filterForm.keyword) params.keyword = filterForm.keyword;
    const result = await IssueApi.getMyIssues(params);
    issues.value = result.list || [];
  } catch (error) {
    ElMessage.error('获取问题列表失败');
    console.error('获取问题列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 重置筛选
const resetFilter = () => {
  filterForm.type = undefined;
  filterForm.status = undefined;
  filterForm.keyword = '';
  loadIssues();
};

// 提交问题
const submitIssue = async () => {
  if (!createFormRef.value) return;

  try {
    await createFormRef.value.validate();
    submitting.value = true;

    await IssueApi.createIssue(createForm);
    ElMessage.success('问题反馈提交成功');
    showCreateDialog.value = false;

    // 重置表单
    Object.assign(createForm, {
      title: '',
      description: '',
      type: 1,
      videoId: undefined,
    });

    // 重新加载列表
    loadIssues();
  } catch (error) {
    if (error !== false) {
      ElMessage.error('提交失败，请重试');
      console.error('提交问题失败:', error);
    }
  } finally {
    submitting.value = false;
  }
};

// 显示问题详情
const showIssueDetail = async (issue: IssueRespVO) => {
  try {
    const detail = await IssueApi.getIssue(issue?.id);
    currentIssue.value = detail;
    showDetailDialog.value = true;
  } catch (error) {
    ElMessage.error('获取问题详情失败');
    console.error('获取问题详情失败:', error);
  }
};

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'info';
    case 2:
      return 'warning';
    case 3:
      return 'success';
    case 4:
      return 'danger';
    default:
      return 'info';
  }
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN');
};

// 页面加载时获取数据
onMounted(() => {
  loadIssues();
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.issue-page {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}
</style>
