<template>
  <el-button v-bind="getBindValue" @click="onClick">
    <Icon v-if="preIcon" :icon="preIcon" class="mr-1px" />
    {{ title ? title : '' }}
    <Icon v-if="postIcon" :icon="postIcon" class="mr-1px" />
  </el-button>
</template>

<script lang="ts" setup>
defineOptions({ name: 'XButton' });
import { ButtonProps } from 'element-plus';

const props = withDefaults(
  defineProps<
    Partial<ButtonProps> & {
      preIcon?: string;
      postIcon?: string;
      title?: string;
      onClick?: (...args: any[]) => any;
      modelValue?: boolean;
      loading?: boolean;
      type?:
        | ''
        | 'text'
        | 'default'
        | 'success'
        | 'warning'
        | 'info'
        | 'primary'
        | 'danger';
      link?: boolean;
      circle?: boolean;
      round?: boolean;
      plain?: boolean;
    }
  >(),
  {
    modelValue: false,
    loading: false,
    type: 'primary',
    link: false,
    circle: false,
    round: false,
    plain: false,
    onClick: () => {},
    preIcon: '',
    postIcon: '',
    title: '',
  },
);

const getBindValue = computed<Partial<ButtonProps>>(
  (): Partial<ButtonProps> => {
    const delArr: string[] = ['title', 'preIcon', 'postIcon', 'onClick'];
    const attrs = useAttrs();
    const obj = { ...attrs, ...props };
    for (const key in obj) {
      if (delArr.indexOf(key) !== -1) {
        delete obj[key];
      }
    }
    return obj;
  },
);
</script>
<style lang="scss" scoped>
:deep(.el-button.is-text) {
  padding: 8px 4px;
  margin-left: 0;
}

:deep(.el-button.is-link) {
  padding: 8px 4px;
  margin-left: 0;
}
</style>
