<template>
  <el-dialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    :width="isMobile ? '90%' : '60%'"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @closed="dialogVisible = false"
    :title="title"
    destroy-on-close
  >
    <div v-if="videoInfo" class="flex flex-col items-center">
      <video-player
        :src="videoInfo.fileUrl"
        controls
        class="w-full"
      ></video-player>
      <el-collapse class="mt-4 w-full" expand-icon-position="left">
        <el-collapse-item title="详细视频信息" name="1">
          <el-descriptions :column="2" border class="mt-4 w-full">
            <el-descriptions-item label="文件名称">
              {{ videoInfo.fileName }}
            </el-descriptions-item>
            <el-descriptions-item label="文件类型">
              {{ videoInfo.fileType }}
            </el-descriptions-item>
            <el-descriptions-item label="视频类型">
              {{ videoInfo.videoProcessType }}
            </el-descriptions-item>
            <el-descriptions-item label="视频时长">
              {{ videoInfo.duration }}秒
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">
              {{ (videoInfo.size / 1024 / 1024).toFixed(2) }}MB
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(videoInfo.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="过期时间">
              {{ formatDate(videoInfo.expireTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import type { VideoInfoRespVO } from '@/api/autoclip/auto_clip';
import dayjs from 'dayjs';

const props = defineProps<{
  modelValue: boolean;
  title: string;
  videoInfo: VideoInfoRespVO | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

const dialogVisible = ref(props.modelValue);
const isMobile = ref(window.innerWidth <= 768);

watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
  },
);

watch(dialogVisible, (val) => {
  emit('update:modelValue', val);
});

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

onMounted(() => {
  window.addEventListener('resize', () => {
    isMobile.value = window.innerWidth <= 768;
  });
});
</script>
