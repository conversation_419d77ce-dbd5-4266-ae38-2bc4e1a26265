<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const handleClick = () => {
  router.push('/');
};
</script>

<template>
  <button
    @click="handleClick"
    class="flex h-auto flex-col items-center justify-center rounded border border-solid border-transparent bg-white px-2 py-2 text-sm font-medium text-gray-800 transition-colors hover:bg-[#ccc] sm:w-auto sm:px-5 sm:text-base dark:hover:bg-[#ccc]"
  >
    <img
      alt="Home"
      class="h-auto max-h-[30px] w-auto max-w-[30px]"
      height="30"
      width="30"
      src="https://www.svgrepo.com/show/535437/home.svg"
    />
    <span class="text-xs">Home</span>
  </button>
</template>
