<!-- 
  Inspira UI 单选框组件
  兼容 Element Plus Radio API，提供动画增强效果
-->
<template>
  <ErrorBoundary
    fallback-component="el-radio"
    :fallback-props="elementPlusProps"
    @error="handleError"
  >
    <label :class="computedClass" @click="handleClick">
      <!-- 隐藏的原生 radio -->
      <input
        ref="radioRef"
        type="radio"
        :name="name"
        :value="value"
        :checked="isChecked"
        :disabled="disabled"
        class="inspira-radio__input"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 自定义单选框外观 -->
      <span :class="radioClass">
        <span :class="radioInnerClass"></span>
      </span>
      
      <!-- 标签文本 -->
      <span v-if="$slots.default || label" :class="labelClass">
        <slot>{{ label }}</slot>
      </span>
    </label>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed, ref, inject } from 'vue';
import { cn } from '@/lib/utils';
import ErrorBoundary from '../Utils/ErrorBoundary.vue';
import type { InspiraRadioProps, InspiraRadioEmits } from './types';

const props = withDefaults(defineProps<InspiraRadioProps>(), {
  size: 'default',
  disabled: false,
  animation: 'scale',
});

const emit = defineEmits<InspiraRadioEmits>();

// 单选框引用
const radioRef = ref<HTMLInputElement>();

// 从 RadioGroup 注入的值
const radioGroup = inject('radioGroup', null) as any;

// 计算是否选中
const isChecked = computed(() => {
  if (radioGroup) {
    return radioGroup.modelValue === props.value;
  }
  return props.modelValue === props.value;
});

// 计算是否禁用
const isDisabled = computed(() => {
  return props.disabled || (radioGroup && radioGroup.disabled);
});

// 计算尺寸
const computedSize = computed(() => {
  return radioGroup?.size || props.size;
});

// Element Plus 降级 props
const elementPlusProps = computed(() => ({
  modelValue: props.modelValue,
  value: props.value,
  label: props.label,
  disabled: props.disabled,
  size: props.size,
  name: props.name,
  class: props.class,
}));

// 计算样式类
const computedClass = computed(() => {
  return cn(
    'inspira-radio',
    `inspira-radio--${computedSize.value}`,
    {
      'inspira-radio--checked': isChecked.value,
      'inspira-radio--disabled': isDisabled.value,
      [`inspira-radio--${props.animation}`]: props.animation !== 'none',
    },
    props.class
  );
});

const radioClass = computed(() => {
  return cn('inspira-radio__circle', {
    'inspira-radio__circle--checked': isChecked.value,
    'inspira-radio__circle--disabled': isDisabled.value,
  });
});

const radioInnerClass = computed(() => {
  return cn('inspira-radio__inner', {
    'inspira-radio__inner--checked': isChecked.value,
  });
});

const labelClass = computed(() => {
  return cn('inspira-radio__label', {
    'inspira-radio__label--checked': isChecked.value,
    'inspira-radio__label--disabled': isDisabled.value,
  });
});

// 事件处理
const handleClick = () => {
  if (isDisabled.value) return;
  
  if (radioGroup) {
    radioGroup.changeEvent(props.value);
  } else {
    emit('update:modelValue', props.value);
  }
  
  emit('change', props.value);
};

const handleChange = (event: Event) => {
  // 原生 change 事件已经在 handleClick 中处理
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

const handleError = (error: Error) => {
  console.warn('InspiraRadio error, falling back to el-radio:', error);
};

// 暴露方法
const focus = () => {
  radioRef.value?.focus();
};

const blur = () => {
  radioRef.value?.blur();
};

defineExpose({
  focus,
  blur,
  radioRef,
});
</script>

<style scoped>
.inspira-radio {
  @apply inspira-ui;
  
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-right: 1rem;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &:last-child {
    margin-right: 0;
  }
  
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  &:hover:not(.inspira-radio--disabled) {
    .inspira-radio__circle {
      border-color: var(--inspira-primary);
    }
  }
}

.inspira-radio__input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  
  &:focus + .inspira-radio__circle {
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
  }
}

.inspira-radio__circle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: 2px solid var(--inspira-border);
  border-radius: 50%;
  background-color: var(--inspira-background);
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &--checked {
    border-color: var(--inspira-primary);
    background-color: var(--inspira-primary);
  }
  
  &--disabled {
    border-color: var(--inspira-border);
    background-color: var(--inspira-surface);
  }
}

.inspira-radio__inner {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: transparent;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  transform: scale(0);
  
  &--checked {
    background-color: white;
    transform: scale(1);
  }
}

.inspira-radio__label {
  margin-left: 8px;
  color: var(--inspira-text);
  font-size: 14px;
  line-height: 1.5;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &--checked {
    color: var(--inspira-primary);
    font-weight: 500;
  }
  
  &--disabled {
    color: var(--inspira-text-secondary);
  }
}

/* 尺寸变体 */
.inspira-radio--small {
  .inspira-radio__circle {
    width: 14px;
    height: 14px;
  }
  
  .inspira-radio__inner {
    width: 4px;
    height: 4px;
  }
  
  .inspira-radio__label {
    font-size: 12px;
    margin-left: 6px;
  }
}

.inspira-radio--large {
  .inspira-radio__circle {
    width: 18px;
    height: 18px;
  }
  
  .inspira-radio__inner {
    width: 8px;
    height: 8px;
  }
  
  .inspira-radio__label {
    font-size: 16px;
    margin-left: 10px;
  }
}

/* 动画效果 */
.inspira-radio--scale {
  &:active:not(.inspira-radio--disabled) {
    .inspira-radio__circle {
      transform: scale(0.95);
    }
  }
}

.inspira-radio--bounce {
  .inspira-radio__circle--checked {
    animation: inspira-bounce 0.3s ease-out;
  }
}

.inspira-radio--pulse {
  .inspira-radio__circle--checked {
    animation: inspira-pulse 0.5s ease-out;
  }
}

.inspira-radio--ripple {
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--inspira-primary);
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
    pointer-events: none;
  }
  
  &.inspira-radio--checked::after {
    animation: inspira-ripple 0.6s ease-out;
  }
}

/* 动画定义 */
@keyframes inspira-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes inspira-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

@keyframes inspira-ripple {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}
</style>