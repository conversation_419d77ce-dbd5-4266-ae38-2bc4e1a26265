// 问题反馈相关类型定义

// 问题类型枚举
export enum IssueType {
  BUG = 1,
  REQUIREMENT = 2
}

// 问题状态枚举
export enum IssueStatus {
  PENDING = 1,
  PROCESSING = 2,
  RESOLVED = 3,
  CLOSED = 4
}

// 问题反馈响应VO
export interface IssueRespVO {
  id: number
  title: string
  description: string
  type: IssueType
  typeName: string
  status: IssueStatus
  statusName: string
  videoId?: number
  videoName?: string
  closedTime?: string
  closedByName?: string
  createTime: string
  updateTime: string
  resolution?: string
}

// 创建问题请求VO
export interface IssueCreateReqVO {
  title: string
  description: string
  type: IssueType
  videoId?: number
}

// 更新问题状态请求VO
export interface IssueUpdateStatusReqVO {
  status: IssueStatus
}

// 添加解决方案请求VO
export interface IssueResolutionReqVO {
  resolution: string
}

// 问题列表查询参数
export interface IssueQueryParams {
  page?: number
  pageSize?: number
  type?: IssueType
  status?: IssueStatus
  keyword?: string
}

// 分页结果
export interface PageResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
} 