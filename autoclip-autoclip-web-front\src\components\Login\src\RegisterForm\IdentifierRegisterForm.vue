<template>
  <el-form
    ref="registerFormRef"
    :model="registerForm"
    :rules="registerRules"
    @submit.prevent="handleRegister"
    class="space-y-4"
  >
    <el-form-item prop="identifier" class="!mb-4">
      <el-input
        v-model="registerForm.identifier"
        placeholder="请输入手机号/邮箱"
      >
        <template #prefix>
          <div class="i-mdi:account text-gray-400"></div>
        </template>
      </el-input>
    </el-form-item>

    <el-form-item prop="code" class="!mb-4">
      <el-input v-model="registerForm.code" placeholder="请输入验证码">
        <template #prefix>
          <div class="i-mdi:key-variant text-gray-400"></div>
        </template>
        <template #suffix>
          <el-button
            :disabled="$countdown.remaining.value > 0"
            @click="getRegisterVerificationCode"
            text
            class="!px-3 !py-1 !text-sm"
          >
            {{
              $countdown.remaining.value > 0
                ? `${$countdown.remaining.value}s后重新获取`
                : '获取验证码'
            }}
          </el-button>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="agreeTerms" class="!mb-4">
      <el-checkbox
        v-model="registerForm.agreeTerms"
        class="text-sm text-gray-600"
      >
        我已阅读并同意
        <el-link type="primary" :underline="'never'" class="!mx-1"
          >用户协议
        </el-link>
        和
        <el-link type="primary" :underline="'never'" class="!mx-1"
          >隐私政策
        </el-link>
      </el-checkbox>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import AuthUtil, { getIdentifierType, SmsSceneEnum } from '@/api/member/auth';
import { useMessage } from '@/hooks/web/useMessage';
import { useUserStore } from '@/store/modules/user';
import { useCountdown } from '@vueuse/core';
import type { FormInstance, FormRules } from 'element-plus';
import { reactive, ref } from 'vue';
import { FormType } from '../useLogin';
import UserApi from '@/api/member/user';

type Emits = {
  (event: 'close'): void;
  (
    event: 'register-success',
    isUserExists: boolean,
    code: string,
    identifier: string,
  ): void;
  (event: 'register-failed', errorMessage: string): void;
  (event: 'switch-form', form: FormType): void;
};

interface RegisterForm {
  identifier: string;
  code: string;
  agreeTerms: boolean;
}

const emit = defineEmits<Emits>();
const registerFormRef = ref<FormInstance>();
const buttonLoading = ref(false);

const registerForm = reactive<RegisterForm>({
  identifier: '',
  code: '',
  agreeTerms: false,
});

const validateAgreeTerms = (_rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请同意用户协议和隐私政策'));
  } else {
    callback();
  }
};

const registerRules: FormRules = {
  identifier: [
    { required: true, message: '请输入手机号/邮箱', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入正确的手机号或邮箱',
      trigger: 'blur',
    },
  ],
  agreeTerms: [{ validator: validateAgreeTerms, trigger: 'change' }],
};

const { $notification } = useMessage();

const $countdown = useCountdown(0);

const getRegisterVerificationCode = async () => {
  registerFormRef.value?.validate();

  const identifier = registerForm.identifier;

  $countdown.start(60);
  AuthUtil.sendAuthCode(
    identifier,
    getIdentifierType(identifier),
    SmsSceneEnum.MEMBER_LOGIN,
  )
    .then(() => {
      $notification.success('验证码已发送，请注意查收');
    })
    .catch(() => {
      $countdown.stop();
    });
};

const resetForm = () => {
  registerForm.identifier = '';
  registerForm.code = '';
  registerForm.agreeTerms = false;

  registerFormRef.value?.resetFields();

  $countdown.stop();
  buttonLoading.value = false;
};

const handleRegister = async () => {
  if (!registerFormRef.value) return;

  try {
    await registerFormRef.value.validate();
  } catch (error) {
    return;
  }

  buttonLoading.value = true;

  const userStore = useUserStore();

  UserApi.isUserExists({
    identifier: registerForm.identifier,
    identifierType: getIdentifierType(registerForm.identifier),
  })
    .then((isUserExists) => {
      return userStore
        .login(
          {
            identifier: registerForm.identifier,
            identifierType: getIdentifierType(registerForm.identifier),
            code: registerForm.code,
          },
          true,
        )
        .then(() => {
          return isUserExists;
        });
    })
    .then((isUserExists) => {
      emit(
        'register-success',
        isUserExists,
        registerForm.code,
        registerForm.identifier,
      );

      if (isUserExists) {
        $notification.success('该手机号/邮箱已注册, 直接登录');
      } else {
        $notification.success('注册成功');
      }

      resetForm();
    })
    .catch(() => {
      $countdown.stop();
      buttonLoading.value = false;
    });
};

// 暴露重置表单方法
defineExpose({
  resetForm,
  phone: registerForm.identifier,
  handleRegister,
  buttonLoading,
});
</script>

<style scoped></style>
