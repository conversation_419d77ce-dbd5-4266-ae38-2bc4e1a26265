// Inspira Input 组件类型定义

export interface InspiraInputProps {
  // 输入值
  modelValue: string | number;
  
  // 输入类型
  type?: 'text' | 'password' | 'email' | 'tel' | 'url' | 'number' | 'search';
  
  // 占位符
  placeholder?: string;
  
  // 尺寸
  size?: 'large' | 'default' | 'small';
  
  // 禁用状态
  disabled?: boolean;
  
  // 只读状态
  readonly?: boolean;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'focus' | 'blur' | 'typing' | 'none';
  
  // 是否显示密码切换按钮
  showPassword?: boolean;
  
  // 是否可清空
  clearable?: boolean;
  
  // 最大长度
  maxlength?: number;
  
  // 最小长度
  minlength?: number;
  
  // 前缀图标
  prefixIcon?: string;
  
  // 后缀图标
  suffixIcon?: string;
  
  // 自动完成
  autocomplete?: string;
  
  // 自动聚焦
  autofocus?: boolean;
  
  // 表单名称
  name?: string;
  
  // 验证状态
  validateStatus?: 'success' | 'warning' | 'error' | 'validating';
  
  // 验证消息
  validateMessage?: string;
}

export interface InspiraInputEmits {
  'update:modelValue': [value: string | number];
  input: [value: string | number, event: Event];
  change: [value: string | number, event: Event];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
  clear: [];
  keydown: [event: KeyboardEvent];
  keyup: [event: KeyboardEvent];
  keypress: [event: KeyboardEvent];
}

export interface InspiraInputSlots {
  prefix: () => any;
  suffix: () => any;
  prepend: () => any;
  append: () => any;
}