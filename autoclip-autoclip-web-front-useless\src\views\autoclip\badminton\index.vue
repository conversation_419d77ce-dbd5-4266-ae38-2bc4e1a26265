<template>
  <AutoClipFramework
    right-panel-title="羽毛球比赛视频剪辑"
    left-panel-title="羽毛球视频剪辑配置"
    :example-videos="exampleVideos"
    :option-items="optionItems"
    :on-post-video="postVideo"
    :on-process-video="onProcessVideo"
  />
</template>

<script setup lang="ts">
import {
  auto_badminton_clip_video,
  BadmintonVideoClipConfigReqVo,
  MatchType,
  ModeEnum,
  PingPongVideoClipConfigReqVo,
} from '@/api/autoclip/auto_clip';
import { getFilePresignedUrl } from '@/api/infra/file';
import { getBadmintonRandomSortVideos } from '@/assets/videos/urls';
import { useAppInject } from '@/hooks/web/useAppInject';
import { VideoInfo } from '@/utils/videoUtils';
import AutoClipFramework from '@/views/autoclip/framework/index.vue';
import axios from 'axios';
import { UploadFile, UploadRequestOptions } from 'element-plus';
import { ref } from 'vue';
import { OptionItem } from '../framework/types';

const { getIsMobile } = useAppInject();
const exampleVideos = getBadmintonRandomSortVideos(getIsMobile.value ? 2 : 4);

defineOptions({
  name: 'Badminton',
});

const onProcessVideo = async (videoInfo: VideoInfo) => {
  const config = optionItems.value.reduce((acc, item) => {
    acc[item.key] = item.value as never;
    return acc;
  }, {} as BadmintonVideoClipConfigReqVo);
  return auto_badminton_clip_video({
    fileInfo: {
      url: videoInfo.url,
      name: videoInfo.name,
      type: videoInfo.type,
      size: videoInfo.size || 0,
    },
    videoClipConfig: config,
  });
};

const postVideo = async (
  options: UploadRequestOptions,
  inputFile: UploadFile,
) => {
  return getFilePresignedUrl({
    name: options.file.name,
    directory: '',
  })
    .then((res) => {
      return axios
        .put(res.uploadUrl, options.file, {
          headers: {
            'Content-Type': options.file.type,
          },
          onUploadProgress: (progressEvent) => {
            inputFile.status = 'uploading';
            inputFile.percentage = (progressEvent.progress || 0) * 100;
          },
        })
        .then(() => {
          inputFile.status = 'success';
          return Promise.resolve(res);
        });
    })
    .then((res) => {
      const config = optionItems.value.reduce((acc, item) => {
        acc[item.key] = item.value as never;
        return acc;
      }, {} as PingPongVideoClipConfigReqVo);
      return auto_badminton_clip_video({
        fileInfo: {
          configId: res.configId,
          url: res.uploadUrl,
          path: res.path,
          name: options.file.name,
          type: options.file.type,
          size: options.file.size,
        },
        videoClipConfig: config,
      });
    });
};

const optionItems = ref<OptionItem<BadmintonVideoClipConfigReqVo>[]>([
  {
    type: 'select',
    label: '比赛类型',
    key: 'matchType',
    selectOptions: [
      {
        label: '双打比赛',
        value: MatchType.DOUBLES_MATCH,
        disabled: true,
      },
      {
        label: '单打比赛',
        value: MatchType.SINGLES_MATCH,
      },
    ],
    value: MatchType.SINGLES_MATCH,
  },
  {
    type: 'select',
    label: '剪辑模式',
    key: 'mode',
    selectOptions: [
      {
        label: '后台剪辑',
        value: ModeEnum.BACKEND_CLIP,
      },
      {
        label: '自定义剪辑',
        value: ModeEnum.CUSTOM_CLIP,
      },
    ],
    value: ModeEnum.BACKEND_CLIP,
  },
  {
    type: 'switch',
    label: '精彩球剪辑',
    key: 'greatBallEditing',
    value: true,
  },
  {
    type: 'switch',
    label: '移除回放',
    tooltip: '一般专业比赛才有',
    key: 'removeReplay',
    value: true,
  },
  {
    type: 'switch',
    label: '获取比赛片段',
    key: 'getMatchSegments',
    value: true,
  },
  {
    type: 'slider',
    label: '单回合前保留时间',
    key: 'reserveTimeBeforeSingleRound',
    tooltip: '单回合比赛开始前预留的时间（秒）',
    sliderOptions: {
      min: 0,
      max: 5,
      step: 0.1,
    },
    value: 1,
  },
  {
    type: 'slider',
    label: '单回合后保留时长(秒)',
    key: 'reserveTimeAfterSingleRound',
    tooltip: '单回合比赛结束后预留的时间（秒）',
    sliderOptions: {
      min: 0,
      max: 5,
      step: 0.1,
    },
    value: 1,
  },
  {
    type: 'slider',
    label: '单回合最小时长(秒)',
    key: 'minimumDurationSingleRound',
    tooltip: '单回合最小时长（秒）',
    sliderOptions: {
      min: 3.0,
      max: 15.0,
      step: 0.1,
    },
    value: 5,
  },
  {
    type: 'slider',
    label: '精彩球最小时长(秒)',
    key: 'minimumDurationGreatBall',
    tooltip: '精彩球最小时长（秒）',
    sliderOptions: {
      min: 5.0,
      max: 60.0,
      step: 0.1,
    },
    value: 10,
  },
]);
</script>

<style scoped lang="scss"></style>
