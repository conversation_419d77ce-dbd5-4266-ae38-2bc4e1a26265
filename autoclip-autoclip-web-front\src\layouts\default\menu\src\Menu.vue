<template>
  <div>
    <el-menu
      ref="menuRef"
      :default-active="activeMenu"
      v-bind="elMenuConfig"
      @select="handleSelect"
      class="py-3 text-2xl"
    >
      <template v-for="item in menuList" :key="item.path">
        <template v-if="!item.meta?.hideMenu">
          <sub-menu v-if="!item.meta?.hideChildrenInMenu" :menu="item" />
          <menu-item v-else :menu="item" />
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { AppRouteModule } from '@/router/types';
import { routes } from '@/router/routes';
import SubMenu from '@/layouts/default/menu/src/SubMenu.vue';
import MenuItem from '@/layouts/default/menu/src/MenuItem.vue';
import { ElMenu, MenuProps } from 'element-plus';

const emit = defineEmits<{
  (e: 'select', key: string): void;
}>();

withDefaults(
  defineProps<{
    elMenuConfig?: Partial<MenuProps>;
  }>(),
  {
    elMenuConfig: () => ({}),
  },
);

const router = useRouter();
const route = useRoute();

const menuList = computed<AppRouteModule[]>(() => {
  routes.sort((a, b) => {
    return (a.meta?.orderNo ?? 0) - (b.meta?.orderNo ?? 0);
  });
  return routes;
});

const activeMenu = computed(() => {
  const { meta, path } = route;
  if (meta.currentActiveMenu) {
    return meta.currentActiveMenu as string;
  }
  return path;
});

const handleSelect = (key: string) => {
  emit('select', key);

  router.push(key);
};
</script>

<style lang="scss" scoped>
:deep(.el-menu--horizontal .el-menu-item) {
  width: 6rem;
}
:deep(.el-popper) {
  border-radius: 10px !important;
}
:deep(.el-menu--popup) {
  border-radius: 10px !important;
}
:deep(.el-menu--horizontal .el-menu-item:hover) {
  border-bottom: 1px solid #409eff;
}
</style>
