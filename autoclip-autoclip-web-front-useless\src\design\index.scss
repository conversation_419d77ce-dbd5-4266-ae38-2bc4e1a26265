@use 'transition/index.scss' as *;
@use 'var/index.scss' as *;
@use 'public.scss' as *;
@use './theme.scss' as *;
@use './inspira/index.scss' as *;

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: visible !important;
  overflow-x: hidden !important;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

a {
  color: #454545;
}

* {
  border-color: #e3e3e3;
  color: #333333;
}

.dark {
  * {
    color: #ffffff;
  }
}

ul {
  padding: 0;
}

li {
  list-style: none;
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none !important;
}
