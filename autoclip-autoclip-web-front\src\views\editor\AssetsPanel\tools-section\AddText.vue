<script setup lang="ts">
import { ref, computed } from 'vue';
import { useProjectStore } from '@/store/editor';
import { useMessage } from '@/hooks/web/useMessage';
import type { TextElement } from '@/types/editor';
import { buildUUID } from '@/utils/uuid';

const projectStore = useProjectStore();
const textElements = computed(() => projectStore.getTextElements);
const { $message } = useMessage();

const textConfig = ref<Partial<TextElement>>({
  text: 'Example',
  positionStart: 0,
  positionEnd: 10,
  x: 600,
  y: 500,
  fontSize: 200,
  color: '#ff0000',
  backgroundColor: 'transparent',
  align: 'center',
  zIndex: 0,
  opacity: 100,
  rotation: 0,
  animation: 'none',
});

const onAddText = (textElement: TextElement) => {
  projectStore.setTextElements([...textElements.value, textElement]);
};

const handleAddText = () => {
  const lastEnd =
    textElements.value.length > 0
      ? Math.max(...textElements.value.map((f) => f.positionEnd))
      : 0;

  const newTextElement: TextElement = {
    id: buildUUID(),
    text: textConfig.value.text || '',
    positionStart: lastEnd || 0,
    positionEnd: lastEnd + 10 || 10,
    x: textConfig.value.x || 0,
    y: textConfig.value.y || 0,
    width: textConfig.value.width,
    height: textConfig.value.height,
    font: textConfig.value.font || 'Arial',
    fontSize: textConfig.value.fontSize || 24,
    color: textConfig.value.color || '#ffffff',
    backgroundColor: textConfig.value.backgroundColor || 'transparent',
    align: textConfig.value.align || 'center',
    zIndex: textConfig.value.zIndex || 0,
    opacity: textConfig.value.opacity || 100,
    rotation: textConfig.value.rotation || 0,
    fadeInDuration: textConfig.value.fadeInDuration,
    fadeOutDuration: textConfig.value.fadeOutDuration,
    animation: textConfig.value.animation || 'none',
  };

  onAddText(newTextElement);

  // Reset form
  textConfig.value = {
    text: 'Example',
    positionStart: lastEnd,
    positionEnd: lastEnd + 10,
    x: 500,
    y: 600,
    fontSize: 200,
    color: '#ff0000',
    backgroundColor: 'transparent',
    align: 'center',
    zIndex: 0,
    opacity: 100,
    rotation: 0,
    animation: 'none',
  };

  $message.success('Text added successfully.');
};
</script>

<template>
  <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
    <div class="space-y-4">
      <!-- Text Content -->
      <div>
        <label class="mb-2 block text-sm font-semibold text-gray-900"
          >Text Content</label
        >
        <textarea
          v-model="textConfig.text"
          rows="3"
          class="w-full rounded-md border border-gray-200 bg-gray-50 p-3 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter your text here..."
        />
      </div>

      <!-- Start and End Time -->
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >Start Time (s)</label
          >
          <input
            v-model.number="textConfig.positionStart"
            type="number"
            class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="0"
            step="0.1"
          />
        </div>
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >End Time (s)</label
          >
          <input
            v-model.number="textConfig.positionEnd"
            type="number"
            class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="0"
            step="0.1"
          />
        </div>
      </div>

      <!-- Position -->
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >X Position</label
          >
          <input
            v-model.number="textConfig.x"
            type="number"
            class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >Y Position</label
          >
          <input
            v-model.number="textConfig.y"
            type="number"
            class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <!-- Font Size and Z-Index -->
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >Font Size</label
          >
          <input
            v-model.number="textConfig.fontSize"
            type="number"
            class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="1"
          />
        </div>
        <div>
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >Z-Index</label
          >
          <input
            v-model.number="textConfig.zIndex"
            type="number"
            class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="0"
          />
        </div>
      </div>

      <!-- Font Type -->
      <div>
        <label class="mb-1 block text-xs font-medium text-gray-600"
          >Font Type</label
        >
        <select
          v-model="textConfig.font"
          class="w-full rounded-md border border-gray-200 bg-gray-50 p-2 text-gray-900 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="Arial">Arial</option>
          <option value="Inter">Inter</option>
          <option value="Lato">Lato</option>
        </select>
      </div>

      <!-- Text Color and Add Button -->
      <div class="flex items-end gap-3">
        <div class="flex-1">
          <label class="mb-1 block text-xs font-medium text-gray-600"
            >Text Color</label
          >
          <input
            v-model="textConfig.color"
            type="color"
            class="h-10 w-full rounded-md border border-gray-200 bg-gray-50 transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <button
          @click="handleAddText"
          class="rounded-md bg-blue-600 px-6 py-2 font-medium text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Add Text
        </button>
      </div>
    </div>
  </div>
</template>
