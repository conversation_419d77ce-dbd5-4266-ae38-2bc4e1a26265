events {
  worker_connections 1024;
}

http {
  server {
    include       /etc/nginx/mime.types;  # 引入 MIME 类型定义
    default_type  application/octet-stream;  # 默认类型，避免 text/plain
    listen 6584;
    server_name localhost;

    location / {
      root /usr/share/nginx/html;
      try_files $uri $uri/ /index.html;
      index index.html;
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
      if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain charset=UTF-8';
        add_header 'Content-Length' 0;
        return 204;
      }
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
  }
}
