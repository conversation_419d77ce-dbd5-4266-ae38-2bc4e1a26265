<!-- 
  Inspira UI 单选框组组件
  兼容 Element Plus RadioGroup API，提供动画增强效果
-->
<template>
  <ErrorBoundary
    fallback-component="el-radio-group"
    :fallback-props="elementPlusProps"
    @error="handleError"
  >
    <div :class="computedClass" role="radiogroup">
      <slot />
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed, provide, reactive } from 'vue';
import { cn } from '@/lib/utils';
import ErrorBoundary from '../Utils/ErrorBoundary.vue';
import type { InspiraRadioGroupProps, InspiraRadioGroupEmits } from './types';

const props = withDefaults(defineProps<InspiraRadioGroupProps>(), {
  disabled: false,
  size: 'default',
  animation: 'scale',
  direction: 'horizontal',
  gap: '1rem',
});

const emit = defineEmits<InspiraRadioGroupEmits>();

// Element Plus 降级 props
const elementPlusProps = computed(() => ({
  modelValue: props.modelValue,
  disabled: props.disabled,
  size: props.size,
  textColor: props.textColor,
  fill: props.fill,
  class: props.class,
}));

// 计算样式类
const computedClass = computed(() => {
  return cn(
    'inspira-radio-group',
    `inspira-radio-group--${props.size}`,
    `inspira-radio-group--${props.direction}`,
    {
      'inspira-radio-group--disabled': props.disabled,
      [`inspira-radio-group--${props.animation}`]: props.animation !== 'none',
    },
    props.class
  );
});

// 计算间距样式
const gapStyle = computed(() => {
  const gapValue = typeof props.gap === 'number' ? `${props.gap}px` : props.gap;
  return props.direction === 'horizontal' 
    ? { columnGap: gapValue }
    : { rowGap: gapValue };
});

// 提供给子组件的上下文
const radioGroupContext = reactive({
  modelValue: computed(() => props.modelValue),
  disabled: computed(() => props.disabled),
  size: computed(() => props.size),
  animation: computed(() => props.animation),
  changeEvent: (value: string | number | boolean) => {
    emit('update:modelValue', value);
    emit('change', value);
  },
});

provide('radioGroup', radioGroupContext);

const handleError = (error: Error) => {
  console.warn('InspiraRadioGroup error, falling back to el-radio-group:', error);
};
</script>

<style scoped>
.inspira-radio-group {
  @apply inspira-ui;
  
  display: flex;
  align-items: center;
  gap: v-bind('gapStyle.columnGap || gapStyle.rowGap');
  
  &--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  &--vertical {
    flex-direction: column;
    align-items: flex-start;
  }
  
  &--disabled {
    opacity: 0.5;
    pointer-events: none;
  }
  
  /* 尺寸变体 */
  &--small {
    gap: 0.5rem;
  }
  
  &--large {
    gap: 1.5rem;
  }
  
  /* 动画效果 */
  &--scale {
    :deep(.inspira-radio) {
      transition: transform var(--inspira-animation-duration) var(--inspira-animation-easing);
      
      &:hover:not(.inspira-radio--disabled) {
        transform: scale(1.05);
      }
    }
  }
  
  &--bounce {
    :deep(.inspira-radio--checked) {
      animation: inspira-group-bounce 0.3s ease-out;
    }
  }
  
  &--pulse {
    :deep(.inspira-radio--checked) {
      animation: inspira-group-pulse 0.5s ease-out;
    }
  }
  
  &--ripple {
    :deep(.inspira-radio) {
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background-color: rgba(99, 102, 241, 0.2);
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
      }
      
      &:hover:not(.inspira-radio--disabled)::before {
        width: 100%;
        height: 100%;
      }
    }
  }
}

/* 动画定义 */
@keyframes inspira-group-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes inspira-group-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inspira-radio-group--horizontal {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}
</style>