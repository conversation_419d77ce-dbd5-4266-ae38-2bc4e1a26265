<script setup lang="ts">
import type { Component } from 'vue';
import { computed } from 'vue';
import { IconifyIcon } from './src';
import { isFunction, isHttpUrl, isObject, isString } from '@/utils/is';

const props = defineProps<{
  fallback?: boolean;
  icon?: Component | Function | string;
}>();

const isRemoteIcon = computed(() => {
  return isString(props.icon) && isHttpUrl(props.icon);
});

const isComponent = computed(() => {
  const { icon } = props;
  return !isString(icon) && (isObject(icon) || isFunction(icon));
});
</script>

<template>
  <div
    v-if="isString(icon) && (icon as String).startsWith('i-')"
    :class="icon"
  />
  <IconifyIcon
    v-else-if="isString(icon)"
    v-bind="$attrs"
    :icon="icon as string"
  />
  <component :is="icon as Component" v-else-if="isComponent" v-bind="$attrs" />
  <img
    v-else-if="isRemoteIcon && isString(icon)"
    :src="icon as string"
    v-bind="$attrs"
    alt=""
  />
</template>
