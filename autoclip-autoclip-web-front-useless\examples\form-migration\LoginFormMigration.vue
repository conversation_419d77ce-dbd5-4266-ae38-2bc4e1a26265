<!-- 
  示例：登录表单迁移
  目标：展示如何在保持表单验证逻辑的同时，迁移到 Inspira UI 组件
-->
<template>
  <div class="login-form-container">
    <!-- 背景特效（可选） -->
    <AuroraBackground v-if="useBackgroundEffect" class="login-background">
      <div class="login-form-content">
        <LoginFormContent />
      </div>
    </AuroraBackground>
    
    <!-- 无背景特效的版本 -->
    <div v-else class="login-form-content">
      <LoginFormContent />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { AuroraBackground } from '@/components/inspira';

// 控制是否使用背景特效
const useBackgroundEffect = ref(true);

// 登录表单内容组件
const LoginFormContent = defineComponent({
  setup() {
    // 表单数据（保持与原有逻辑一致）
    const loginForm = reactive({
      identifier: '',
      password: '',
      remember: false,
    });

    // 表单验证规则（完全保持原有逻辑）
    const loginRules = computed(() => ({
      identifier: [
        { required: true, message: '请输入手机号或邮箱', trigger: 'blur' },
        { 
          pattern: /^1[3-9]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
          message: '请输入正确的手机号或邮箱',
          trigger: 'blur'
        }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ]
    }));

    // 表单状态
    const loading = ref(false);
    const formRef = ref();

    // 登录处理（保持原有逻辑）
    const handleLogin = async () => {
      if (!formRef.value) return;
      
      try {
        await formRef.value.validate();
        loading.value = true;
        
        // 原有的登录逻辑
        // await userStore.login(loginForm);
        
        console.log('Login successful');
      } catch (error) {
        console.error('Login failed:', error);
      } finally {
        loading.value = false;
      }
    };

    return () => (
      <div class="login-form-wrapper">
        <h2 class="login-title">
          {/* 使用 Inspira UI 的文本动画 */}
          <FlipWords words={['登录', 'Login']} class="text-2xl font-bold" />
        </h2>

        {/* 保持 Element Plus 的表单验证，但使用 Inspira UI 的输入组件 */}
        <el-form
          ref={formRef}
          model={loginForm}
          rules={loginRules.value}
          class="login-form"
        >
          {/* 用户名输入框 - 使用自定义的 Inspira Input */}
          <el-form-item prop="identifier">
            <ErrorBoundary
              fallbackComponent="el-input"
              fallbackProps={{
                modelValue: loginForm.identifier,
                placeholder: '请输入手机号/邮箱',
                size: 'large',
                class: 'login-input'
              }}
              onError={(error) => console.warn('Input component error:', error)}
            >
              <InspiraInput
                v-model={loginForm.identifier}
                placeholder="请输入手机号/邮箱"
                size="large"
                class="login-input"
                animation="focus"
              >
                {{
                  prefix: () => <Icon icon="i-mdi:account" class="text-gray-400" />
                }}
              </InspiraInput>
            </ErrorBoundary>
          </el-form-item>

          {/* 密码输入框 */}
          <el-form-item prop="password">
            <ErrorBoundary
              fallbackComponent="el-input"
              fallbackProps={{
                modelValue: loginForm.password,
                type: 'password',
                placeholder: '请输入密码',
                size: 'large',
                showPassword: true,
                class: 'login-input'
              }}
            >
              <InspiraInput
                v-model={loginForm.password}
                type="password"
                placeholder="请输入密码"
                size="large"
                showPassword
                class="login-input"
                animation="focus"
              >
                {{
                  prefix: () => <Icon icon="i-mdi:lock" class="text-gray-400" />
                }}
              </InspiraInput>
            </ErrorBoundary>
          </el-form-item>

          {/* 记住密码 */}
          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model={loginForm.remember}>
                记住密码
              </el-checkbox>
              <el-link type="primary" class="forgot-password">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>

          {/* 登录按钮 - 使用 Inspira UI 的 RainbowButton */}
          <el-form-item>
            <ErrorBoundary
              fallbackComponent="el-button"
              fallbackProps={{
                type: 'primary',
                loading: loading.value,
                size: 'large',
                class: 'login-button'
              }}
              onClick={handleLogin}
            >
              <InspiraButton
                type="primary"
                loading={loading.value}
                size="large"
                class="login-button"
                onClick={handleLogin}
              >
                登录
              </InspiraButton>
            </ErrorBoundary>
          </el-form-item>
        </el-form>
      </div>
    );
  }
});
</script>

<style scoped>
.login-form-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.login-background {
  /* AuroraBackground 的自定义样式 */
  --aurora-opacity: 0.3;
}

.login-form-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
}

.login-form-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--el-text-color-primary);
}

.login-form {
  width: 100%;
}

.login-input {
  height: 48px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-weight: 500;
}

.forgot-password {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form-content {
    padding: 1rem;
  }
  
  .login-form-wrapper {
    padding: 1.5rem;
  }
}
</style>