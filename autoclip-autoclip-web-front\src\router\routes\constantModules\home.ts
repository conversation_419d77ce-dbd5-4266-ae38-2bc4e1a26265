import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const { t } = useI18n();

export const login: AppRouteModule = {
  path: '/',
  name: 'home',
  component: LAYOUT,
  redirect: '/home',
  meta: {
    title: t('主页'),
    icon: 'login',
    hideChildrenInMenu: true,
    orderNo: -1,
  },
  children: [
    {
      path: '/home',
      name: 'homeIndex',
      component: () => import('@/views/home/<USER>'),
      meta: {
        showFooter: true,
        hidden: true,
        title: '首页',
        noTagsView: true,
      },
    },
  ],
};

export default login;
