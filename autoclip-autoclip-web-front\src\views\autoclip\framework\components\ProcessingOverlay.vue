<template>
  <div
    v-if="isProcessing"
    class="absolute inset-0 z-10 flex items-center justify-center bg-white/50 backdrop-blur-[2px]"
  >
    <div class="flex flex-col items-center gap-4">
      <div
        class="h-12 w-12 animate-spin rounded-full border-4 border-[#6c63ff] border-t-transparent"
      ></div>
      <div class="flex flex-col items-center gap-2">
        <div class="animate-pulse text-gray-700">
          {{
            progressInfo?.status === ProcessStatus.PREPARING
              ? `视频等待处理中...${progressInfo?.position > 0 ? `前方有 ${progressInfo?.position} 个视频在排队` : ''}`
              : '视频处理中...'
          }}
        </div>
        <div
          v-if="showNotification"
          class="animate-pulse text-sm text-gray-600"
        >
          你可以离开页面，视频处理完后将会通过消息通知您。
        </div>
        <div
          v-if="progressInfo?.status !== undefined"
          class="w-80% mt-2 flex flex-col justify-center"
        >
          <el-progress
            :percentage="
              progressInfo.progress > 100 ? 100 : progressInfo.progress
            "
            :show-text="true"
            :striped="true"
            :stroke-width="8"
            text-inside
            striped-flow
            class="h-1"
          />
          <div class="mt-2 flex flex-row justify-between gap-4 px-4">
            <div class="flex flex-col justify-between">
              <div class="text-nowrap text-sm text-gray-600 max-md:text-xs">
                视频时长：{{ progressInfo.videoDuration }} s
              </div>
              <div class="text-nowrap text-sm text-gray-600 max-md:text-xs">
                预计剩余时间：{{ estimatedRemainingTime }} s
              </div>
            </div>
            <div class="flex flex-col justify-between">
              <div class="text-nowrap text-sm text-gray-600 max-md:text-xs">
                处理速度：{{ progressInfo.processSpeed.toFixed(2) }} s/s
              </div>

              <div class="text-nowrap text-sm text-gray-600 max-md:text-xs">
                {{ getProcessedTimeInfo(progressInfo.status).text }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ProcessStatus } from '@/api/autoclip/auto_clip';
import { ProgressInfo } from './types';

const props = defineProps<{
  isProcessing: boolean;
  showNotification?: boolean;
  progressInfo?: ProgressInfo;
}>();

const processedTime = ref(0);
const estimatedRemainingTime = ref(0);

watch(
  () => props.progressInfo,
  (newVal) => {
    if (newVal) {
      processedTime.value = newVal.processedTime;
      estimatedRemainingTime.value = newVal.estimatedRemainingTime;
    }
  },
);

const getProcessedTimeInfo = (status: ProcessStatus) => {
  switch (status) {
    case ProcessStatus.PREPARING:
      return {
        text: `等待处理时间: ${processedTime.value} s`,
      };
    default:
      return {
        text: `已处理时间：${processedTime.value} s`,
      };
  }
};

const interval = ref<NodeJS.Timeout | null>(null);

watch(
  () => props.isProcessing,
  (newVal) => {
    if (newVal) {
      interval.value = setInterval(() => {
        processedTime.value = processedTime.value + 1;
        estimatedRemainingTime.value = Math.max(
          estimatedRemainingTime.value - 1,
          0,
        );
      }, 1000);
    } else {
      if (interval.value) {
        clearInterval(interval.value);
      }
      processedTime.value = 0;
      estimatedRemainingTime.value = 0;
    }
  },
);
</script>

<style scoped>
:deep(.el-progress-bar__inner) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
:deep(.el-progress-bar__innerText) {
  font-size: 8px;
  font-weight: 600;
  width: 2rem;
}
</style>
