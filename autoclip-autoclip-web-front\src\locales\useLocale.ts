/**
 * Multi-language related operations
 */
import type { LocaleType } from '#/config';

import { useLocaleStoreWithOut } from '@/store/modules/locale';
import { unref, computed, ref } from 'vue';
import { loadLocalePool, setHtmlPageLang } from './helper';
import type { Language } from 'element-plus/es/locale';
import { i18n } from '@/locales/setupI18n';

interface LangModule {
  message: Recordable;
  dateLocale: Recordable;
  dateLocaleName: string;
}

function setI18nLanguage(locale: LocaleType) {
  const localeStore = useLocaleStoreWithOut();

  if (i18n.mode === 'legacy') {
    i18n.global.locale = locale;
  } else {
    (i18n.global.locale as any).value = locale;
  }
  localeStore.setLocaleInfo({ locale });
  setHtmlPageLang(locale);
}

async function loadLocaleMessage(locale: LocaleType) {
  const langModule = ((await import(`./lang/${locale}.ts`)) as any)
    .default as LangModule;
  if (!langModule) return;

  const { message } = langModule;

  i18n.global.setLocaleMessage(locale, message);
  loadLocalePool.push(locale);
}

export function useLocale() {
  const localeStore = useLocaleStoreWithOut();
  const getLocale = computed(() => localeStore.getLocale);
  const getShowLocalePicker = computed(() => localeStore.getShowPicker);

  const elementPlusLocale = ref<Language>({} as Language);

  const getElementPlusLocale = computed((): Language => {
    const locale = unref(getLocale);
    const langMap: Record<string, () => Promise<{ default: Language }>> = {
      zh_CN: () => import('element-plus/es/locale/lang/zh-cn'),
      en: () => import('element-plus/es/locale/lang/en'),
    };

    (langMap[locale]?.() ?? langMap['zh_CN']()).then((module) => {
      elementPlusLocale.value = module.default;
    });
    return elementPlusLocale.value;
  });

  // Switching the language will change the locale of useI18n
  // And submit to configuration modification
  async function changeLocale(locale: LocaleType) {
    const currentLocale = unref(i18n.global.locale);
    if (currentLocale === locale) {
      return locale;
    }

    if (loadLocalePool.includes(locale)) {
      setI18nLanguage(locale);
      return locale;
    }
    await loadLocaleMessage(locale);
    setI18nLanguage(locale);
    return locale;
  }

  return {
    getLocale,
    getShowLocalePicker,
    changeLocale,
    getElementPlusLocale,
  };
}
