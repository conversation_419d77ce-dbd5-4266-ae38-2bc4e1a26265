<template>
  <div class="relative">
    <input
      v-if="isEditing"
      ref="inputRef"
      type="text"
      :value="projectStore.getProjectName"
      @input="handleChange"
      @blur="handleBlur"
      @keydown="handleKeyDown"
      class="mt-4 w-full rounded border border-gray-300 bg-white px-2 py-1 text-2xl font-bold capitalize tracking-wider text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
      autofocus
    />
    <p
      v-else
      @click="handleClick"
      class="mt-4 flex cursor-pointer items-center rounded px-2 py-1 text-2xl font-bold capitalize tracking-wider text-gray-900 hover:bg-gray-100"
    >
      {{ projectStore.projectName }}
      <svg
        class="ml-2 h-4 w-4 text-gray-500"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
        />
      </svg>
    </p>
  </div>
</template>

<script setup lang="ts">
import { useProjectStore } from '@/store/editor';
import { nextTick, ref, watch } from 'vue';

const projectStore = useProjectStore();

const isEditing = ref(false);
const inputRef = ref<HTMLInputElement | null>(null);

watch(isEditing, async (newValue) => {
  if (newValue && inputRef.value) {
    await nextTick();
    inputRef.value.focus();
    inputRef.value.select();
  }
});

const handleClick = () => {
  isEditing.value = true;
};

const handleBlur = () => {
  isEditing.value = false;
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    isEditing.value = false;
  }
  if (e.key === 'Escape') {
    isEditing.value = false;
  }
};

const handleChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  projectStore.setProjectName(target.value);
};
</script>
