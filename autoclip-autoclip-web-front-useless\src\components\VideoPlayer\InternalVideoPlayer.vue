<template>
  <video ref="videoRef" :class="videoClass">
    <source
      v-for="source in sources"
      :key="source.src"
      :src="source.src"
      :type="source.type"
    />
    <p class="vjs-no-js">
      To view this video please enable JavaScript, and consider upgrading to a
      web browser that
      <a href="https://videojs.com/html5-video-support/" target="_blank">
        supports HTML5 video </a
      >.
    </p>
  </video>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import videojs from 'video.js';
import type Player from 'video.js/dist/types/player';
import {
  VideoPlayerEmits,
  VideoPlayerProps,
  VideoSource,
} from '@/components/VideoPlayer/types';

// 定义Props和默认值
const props = withDefaults(defineProps<VideoPlayerProps>(), {
  sources: () => [],
  fluid: true,
  responsive: true,
  preload: 'metadata',
  autoplay: false,
  muted: false,
  loop: false,
  controls: true,
  playbackRates: () => [0.5, 1, 1.25, 1.5, 2],
  language: 'zh-CN',
  videoClass: 'video-js',
  options: () => ({}),
});

// 定义事件
const emit = defineEmits<VideoPlayerEmits>();

// 组件引用
const videoRef = ref<HTMLVideoElement>();
let player: Player | null = null;

// 初始化Video.js播放器
const initPlayer = async () => {
  if (!videoRef.value) return;

  const videoOptions = {
    fluid: props.fluid,
    responsive: props.responsive,
    width: props.width,
    height: props.height,
    preload: props.preload,
    autoplay: props.autoplay,
    muted: props.muted,
    loop: props.loop,
    controls: props.controls,
    playbackRates: props.playbackRates,
    language: props.language,
    languages: props.languages,
    sources: props.sources,
    poster: props.poster,
    ...props.options,
  };

  // 创建播放器实例
  player = videojs(videoRef.value, videoOptions);

  // 绑定事件
  bindEvents();

  // 播放器准备就绪
  player.ready(() => {
    emit('ready', player!);
  });
};

// 绑定播放器事件
const bindEvents = () => {
  if (!player) return;

  player.on('play', () => emit('play'));
  player.on('playing', () => emit('playing'));
  player.on('pause', () => emit('pause'));
  player.on('ended', () => emit('ended'));
  player.on('error', (error) => emit('error', error));
  player.on('loadstart', () => emit('loadstart'));
  player.on('loadeddata', () => emit('loadeddata'));
  player.on('timeupdate', (currentTime) => emit('timeupdate', currentTime));
  player.on('volumechange', (volume) => emit('volumechange', volume));
  player.on('loadedmetadata', () => emit('loadedmetadata'));
  player.on('canplay', () => emit('canplay'));
  player.on('canplaythrough', () => emit('canplaythrough'));
  player.on('waiting', () => emit('waiting'));
  player.on('seeking', () => emit('seeking'));
  player.on('seeked', () => emit('seeked'));
};

const destroyPlayer = () => {
  if (player && !player.isDisposed()) {
    player.dispose();
    player = null;
  }
};

const reinitPlayer = async () => {
  destroyPlayer();
  await initPlayer();
};

watch(
  () => props.sources,
  () => {
    if (player && !player.isDisposed()) {
      player.src(props.sources);
    }
  },
  { deep: true },
);

// 监听poster变化
watch(
  () => props.poster,
  (newPoster) => {
    if (player && !player.isDisposed() && newPoster) {
      player.poster(newPoster);
    }
  },
);

// 组件挂载
onMounted(() => {
  initPlayer();
});

// 组件卸载
onBeforeUnmount(() => {
  destroyPlayer();
});

// 暴露播放器实例和方法给父组件
defineExpose({
  player: () => player,
  play: () => {
    player?.play();
  },
  pause: () => player?.pause(),
  currentTime: (time?: number) => {
    if (time !== undefined) {
      player?.currentTime(time);
    }
    return player?.currentTime();
  },
  duration: () => player?.duration(),
  volume: (vol?: number) => {
    if (vol !== undefined) {
      player?.volume(vol);
    }
    return player?.volume();
  },
  muted: (mute?: boolean) => {
    if (mute !== undefined) {
      player?.muted(mute);
    }
    return player?.muted();
  },
  paused: () => player?.paused(),
  ended: () => player?.ended(),
  src: (source?: VideoSource[]) => {
    if (source) {
      player?.src(source);
    }
  },
  poster: (posterUrl?: string) => {
    if (posterUrl) {
      player?.poster(posterUrl);
    }
    return player?.poster();
  },
  fullscreen: (enable?: boolean) => {
    if (enable !== undefined) {
      if (enable) {
        player?.requestFullscreen();
      } else {
        player?.exitFullscreen();
      }
    }
    return player?.isFullscreen();
  },
  dispose: destroyPlayer,
  reinit: reinitPlayer,
});
</script>

<style lang="scss">
@import 'video.js/dist/video-js.css';
</style>
