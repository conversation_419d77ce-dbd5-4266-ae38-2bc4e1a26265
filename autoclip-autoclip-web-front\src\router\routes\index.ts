import type { AppRouteModule } from '@/router/types';

const modules = import.meta.glob('./constantModules/**/*.ts', {
  eager: true,
  import: 'default',
});

const constantRoutes: AppRouteModule[] = [];

Object.keys(modules).forEach((key) => {
  const module = modules[key];
  const mod = module || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  constantRoutes.push(...modList);
});

export const routes = [...constantRoutes];

export const asyncRoutes: AppRouteModule[] = [...constantRoutes];
