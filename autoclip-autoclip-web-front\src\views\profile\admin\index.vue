<template>
  <el-card class="w-full" :shadow="'never'">
    <div class="text-center">
      <UserAvatar :img="userInfo?.avatar" />
    </div>
    <Form
      ref="formRef"
      :labelWidth="getIsMobile ? 0 : 200"
      :rules="rules"
      :schema="schema"
    >
      <template #sex="form">
        <el-radio-group v-model="form['sex']">
          <el-radio :value="1">{{ t('profile.user.man') }}</el-radio>
          <el-radio :value="2">{{ t('profile.user.woman') }}</el-radio>
        </el-radio-group>
      </template>
    </Form>
    <div style="text-align: center">
      <XButton :title="t('common.save')" type="primary" @click="submit()" />
      <XButton :title="t('common.reset')" type="danger" @click="init()" />
    </div>
  </el-card>
</template>
<script lang="ts" setup>
import type { FormRules } from 'element-plus';
import { ElForm } from 'element-plus';
import { FormSchema } from '@/types/form';
import { Form, FormExpose } from '@/components/Form';

import { useUserStore } from '@/store/modules/user';
import { XButton } from '@/components/XButton';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@/hooks/web/useMessage';
import UserApi, { UserInfo } from '@/api/member/user';
import UserAvatar from '@/views/profile/admin/UserAvatar.vue';
import { useAppInject } from '@/hooks/web/useAppInject';

defineOptions({ name: 'BasicInfo' });

const { getIsMobile } = useAppInject();
const { t } = useI18n();
const { $message } = useMessage(); // 消息弹窗
const userStore = useUserStore();
const userInfo = computed(() => {
  return userStore.getUserInfo;
});

const rules = reactive<FormRules>({
  nickname: [
    {
      message: t('profile.rules.nickname'),
      trigger: 'change',
    },
  ],
});
const schema = reactive<FormSchema[]>([
  {
    field: 'nickname',
    label: t('profile.user.nickname'),
    component: 'Input',
    value: userStore.getUserInfo.nickname,
  },
  {
    field: 'sex',
    label: t('profile.user.sex'),
    component: 'InputNumber',
    value: userStore.getUserInfo.sex,
  },
  {
    field: 'point',
    label: t('profile.user.point'),
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    value: userStore.getUserInfo.point,
  },
  {
    field: 'experience',
    label: t('profile.user.experience'),
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    value: userStore.getUserInfo.experience,
  },
  {
    field: 'level',
    label: t('profile.user.level'),
    component: 'Input',
    value: userStore.getUserInfo.level?.name || '暂无等级',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'mobile',
    label: t('profile.user.mobile'),
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    value: userStore.getUserInfo.mobile || '暂未绑定手机号',
  },
  {
    field: 'email',
    label: t('profile.user.email'),
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    value: userStore.getUserInfo.email || '暂未绑定邮箱',
  },
]);
const formRef = ref<FormExpose>(); // 表单 Ref
const submit = () => {
  // @ts-ignore
  const elForm = unref(formRef)?.getElFormRef() as typeof ElForm;
  if (!elForm) return;
  elForm.validate(async (valid) => {
    if (valid) {
      const data = unref(formRef)?.formModel as UserInfo;
      UserApi.updateUser(data)
        .then(() => {
          $message.success(t('common.updateSuccess'));
          return userStore.getUserInfoAction();
        })
        .then(() => {
          init();
        })
        .catch(() => {
          $message.success(t('common.updateFailed'));
        });
    }
  });
};
const init = async () => {
  const res = userStore.getUserInfo;
  unref(formRef)?.setValues(res);
  return res;
};
</script>

<style scoped lang="scss"></style>
