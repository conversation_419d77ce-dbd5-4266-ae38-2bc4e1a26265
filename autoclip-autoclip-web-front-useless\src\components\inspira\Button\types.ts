// Inspira Button 组件类型定义

export interface InspiraButtonProps {
  // 按钮类型
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
  
  // 按钮尺寸
  size?: 'large' | 'default' | 'small';
  
  // 加载状态
  loading?: boolean;
  
  // 禁用状态
  disabled?: boolean;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'rainbow' | 'hover' | 'click' | 'none';
  
  // 动画速度（秒）
  speed?: number;
  
  // 是否为块级元素
  block?: boolean;
  
  // 是否为圆形按钮
  round?: boolean;
  
  // 是否为圆角按钮
  circle?: boolean;
  
  // 图标
  icon?: string;
  
  // 图标位置
  iconPlacement?: 'left' | 'right';
}

export interface InspiraButtonEmits {
  click: [event: MouseEvent];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
}

export interface InspiraButtonSlots {
  default: () => any;
  icon: () => any;
}