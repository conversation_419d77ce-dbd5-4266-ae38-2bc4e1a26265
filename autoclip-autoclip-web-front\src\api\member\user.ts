import { defHttp } from '@/utils/http/axios';
import { IdentifierType } from './auth';

export interface UserInfo {
  id: number;
  nickname: string;
  avatar: string;
  mobile: string;
  email: string;
  sex: number;
  // 积分
  point: number;
  experience: number;
  level: Level;
  // 是否成为推广员
  brokerageEnabled: boolean;
}

export interface Level {
  id: number;
  name: string;
  level: number;
  icon: string;
}

/**
 * private String password;
 *
 *     @Schema(description = "手机验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
 *     @NotEmpty(message = "手机验证码不能为空")
 *     @Length(min = 4, max = 6, message = "手机验证码长度为 4-6 位")
 *     @Pattern(regexp = "^[0-9]+$", message = "手机验证码必须都是数字")
 *     private String code;
 */

export interface UpdateUserPasswordParams {
  password: string;
  code: string;
}

export interface IdentifierParams {
  identifier: string;
  identifierType: IdentifierType;
}

const UserApi = {
  isUserExists: (identifierParams: IdentifierParams) => {
    return defHttp.request<boolean>({
      url: '/member/user/exists',
      method: 'GET',
      params: identifierParams,
    });
  },
  // 获得基本信息
  getUserInfo: () => {
    return defHttp.request<UserInfo>({
      url: '/member/user/get',
      method: 'GET',
    });
  },
  // 修改基本信息
  updateUser: (data) => {
    return defHttp.request({
      url: '/member/user/update',
      method: 'PUT',
      data,
    });
  },
  // 修改用户手机
  updateUserMobile: (data) => {
    return defHttp.request({
      url: '/member/user/update-mobile',
      method: 'PUT',
      data,
    });
  },
  // 基于微信小程序的授权码，修改用户手机
  updateUserMobileByWeixin: (code) => {
    return defHttp.request({
      url: '/member/user/update-mobile-by-weixin',
      method: 'PUT',
      data: {
        code,
      },
    });
  },
  // 修改密码
  updateUserPassword: (data: UpdateUserInfoParams) => {
    return defHttp.request({
      url: '/member/user/update-password',
      method: 'PUT',
      data,
    });
  },
  // 重置密码
  resetUserPassword: (data: ResetUserPasswordParams) => {
    return defHttp.request({
      url: '/member/user/reset-password',
      method: 'PUT',
      data,
    });
  },
};
interface ResetUserPasswordParams {
  password: string;
  code: string;
  identifier: string;
  identifierType: IdentifierType;
}
interface UpdateUserInfoParams {
  identifier: string;
  identifierType: IdentifierType;
  password: string;
  code: string;
}

export default UserApi;
