<template>
  <DragDropUpload
    accept="video/*"
    @file-selected="handleFileSelected"
    @drag-enter="handleDragEnter"
    @drag-leave="handleDragLeave"
  >
    <MessageReceiver
      ref="websocketRef"
      class="!relative"
      @handle-progress="handleProgress"
    />
    <div
      class="flex overflow-y-auto bg-[#f7f8fa] max-md:flex-col md:h-[calc(100vh-6rem)]"
    >
      <PingPongLeftPanel
        ref="leftPanelRef"
        :title="leftPanelTitle"
        :option-items="optionItems"
        :on-post-video="onPostVideo"
        :on-process-video="onProcessVideo"
      />
      <PingPongRightPanel
        :example-videos="exampleVideos"
        @file-select="handleFileSelected"
        :title="rightPanelTitle"
        :input-preview-video-url="inputPreviewVideoUrl"
        :outputPreviewVideoUrl="outputPreviewVideoUrl"
        :is-processing="isProcessing"
        :processing-progress="progressRef"
      />
 </div>
    <el-dialog
      v-model="showFeedbackDialog"
      title="和我们一起成长😊"
      :width="isMobile ? '95%' : '400px'"
      :show-close="true"
      class="feedback-dialog"
    >
      <div class="text-center">
        <p class="mb-2 text-gray-700">感谢您的使用🙏目前软件处于内测阶段</p>
        <p class="my-2 text-gray-700">
          主页面下方有群二维码，如果有任何问题和需要一定要加群反馈，我们一定会帮你及时解决✊
        </p>
        <p class="my-2 text-gray-700">网站随时可能出现意外😨加群关注网站新动态，享受免费省时的视频剪辑🎉</p>
        <img
          v-if="feedbackQrUrl"
          :src="feedbackQrUrl"
          alt="群二维码"
          class="mx-auto h-48 w-48 rounded-lg border border-gray-200"
        />
        <p class="my-2 text-gray-700">如果群二维码失效，请加微信：Howe520_</p>
      </div>
    </el-dialog>
  </DragDropUpload>
</template>

<script setup lang="ts">
import {
  ProcessStatus,
  VideoInfoRespVO,
  VideoProcessProgressVO,
} from '@/api/autoclip/auto_clip';
import { RemoteVideoInfo } from '@/assets/videos/urls';
import { useMessage } from '@/hooks/web/useMessage';
import { VideoInfo } from '@/utils/videoUtils';
import PingPongLeftPanel from '@/views/autoclip/framework/LeftPanel.vue';
import MessageReceiver from '@/views/autoclip/framework/MessageReceiver.vue';
import PingPongRightPanel from '@/views/autoclip/framework/RightPanel.vue';
import {
  genFileId,
  UploadFile,
  UploadRawFile,
  UploadRequestOptions,
} from 'element-plus';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { OptionItem } from './types';
import { generateQRUrl } from '@/utils/qrcode';
import { InfraFileApi } from '@/api/enum/infraFile';
import { useWindowSize } from '@vueuse/core';

defineOptions({
  name: 'AutoClipFramework',
});
defineProps<{
  rightPanelTitle: string;
  leftPanelTitle: string;
  exampleVideos: RemoteVideoInfo[];
  optionItems: OptionItem<any>[];
  onPostVideo: (
    options: UploadRequestOptions,
    inputFile: UploadFile,
  ) => Promise<VideoInfoRespVO>;
  onProcessVideo: (videoInfo: VideoInfo) => Promise<VideoInfoRespVO>;
}>();

const { $message } = useMessage();
const { width } = useWindowSize();
const isMobile = computed(() => width.value < 768);
const outputPreviewVideoUrl = ref('');

const showFeedbackDialog = ref(false);
const feedbackQrUrl = ref('');

const isProcessing = computed(() => {
  return leftPanelRef.value?.isProcessing;
});
const leftPanelRef = ref<InstanceType<typeof PingPongLeftPanel>>();
const inputPreviewVideoUrl = computed(() => {
  return leftPanelRef.value?.inputPreviewVideoUrl;
});

const handleFileSelected = (files: File[] | VideoInfo) => {
  if ('url' in files) {
    leftPanelRef.value?.handleStart(files);
  } else {
    leftPanelRef.value?.clearFiles?.();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    leftPanelRef.value?.handleStart?.(file);
  }
};

const isDragging = ref(false);

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = true;
};
const progressRef = ref<VideoProcessProgressVO>();

const handleProgress = async (progress: VideoProcessProgressVO) => {
  if (progress.status === ProcessStatus.COMPLETED) {
    if (leftPanelRef != null && leftPanelRef.value != null) {
      leftPanelRef.value.isProcessing = false;
    }
    $message.success(`视频${progress.name}处理完成`);
    outputPreviewVideoUrl.value = progress.url;
    progressRef.value = undefined;

    // 显示群聊反馈通知
    const qrUrl = await generateQRUrl(InfraFileApi.GROUP_CHAT_SHORT_LINK);
    setTimeout(() => {
      if (qrUrl) {
        showFeedbackDialog.value = true;
        feedbackQrUrl.value = qrUrl;
      }
    }, 1000);
  } else if (progress.status === ProcessStatus.FAILED) {
    $message.error(`视频${progress.name}处理失败`);
    progressRef.value = undefined;
    setTimeout(async () => {
      const qrUrl = await generateQRUrl(InfraFileApi.GROUP_CHAT_SHORT_LINK);
      if (qrUrl) {
        showFeedbackDialog.value = true;
        feedbackQrUrl.value = qrUrl;
      }
    }, 1000);
  } else {
    if (leftPanelRef != null && leftPanelRef.value != null) {
      progressRef.value = progress;
      leftPanelRef.value.isProcessing = true;
    }
  }
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
};
const websocketRef = ref<InstanceType<typeof MessageReceiver>>();
onMounted(async () => {
  websocketRef.value?.open();
});
onUnmounted(() => {
  websocketRef.value?.close();
});
</script>

<style scoped>
.toggle-checkbox {
  width: 32px;
  height: 18px;
  accent-color: #6c63ff;
}

/* Element Plus Slider 白色主题自定义样式 */
:deep(.el-slider) {
  padding: 8px 0;
}

:deep(.el-slider__runway) {
  height: 4px;
  background: #c5c6c9;
  /* 浅灰色 */
  border-radius: 2px;
}

:deep(.el-slider__bar) {
  height: 4px;
  background: #6c63ff;
  /* 主色 */
  border-radius: 2px;
}

:deep(.el-slider__button) {
  width: 16px;
  height: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
  border: 2px solid #e0e0e0;
  transition:
    box-shadow 0.2s,
    border-color 0.2s;
}

:deep(.el-slider__button:hover),
:deep(.el-slider__button:focus) {
  box-shadow:
    0 0 0 8px rgba(108, 99, 255, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #6c63ff;
}

/* 反馈对话框样式 */
:deep(.feedback-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
  
  .el-button--primary {
    background-color: #6c63ff;
    border-color: #6c63ff;
  }
  
  .el-button--primary:hover {
    background-color: #5a52d5;
    border-color: #5a52d5;
  }
}

</style>
