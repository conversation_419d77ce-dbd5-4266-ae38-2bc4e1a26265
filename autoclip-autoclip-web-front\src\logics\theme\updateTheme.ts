// import { lighten, darken, lightness } from '@/utils/color';
// import { useAppStore } from '@/store/modules/app';
// import { setCssVar } from './util';
//
// const HEADER_BG_COLOR_VAR = '--header-bg-color';
// const HEADER_BG_HOVER_COLOR_VAR = '--header-bg-hover-color';
// const HEADER_MENU_ACTIVE_BG_COLOR_VAR = '--header-active-menu-bg-color';
//
// const SIDER_ACTIVE_BG_COLOR = '--sider-active-bg-color';
// const SIDER_DARK_BG_COLOR = '--sider-dark-bg-color';
// const SIDER_DARK_DARKEN_BG_COLOR = '--sider-dark-darken-bg-color';
// const SIDER_LIGHTEN_BG_COLOR = '--sider-dark-lighten-bg-color';
//
// /**
//  * Change the background color of the top header
//  * @param color
//  */
// export function updateTheme() {
//
// }
