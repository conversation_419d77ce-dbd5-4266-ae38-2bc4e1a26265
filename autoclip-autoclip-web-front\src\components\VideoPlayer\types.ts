// 视频源接口
import type Player from 'video.js/dist/types/player';

interface VideoSource {
  src: string;
  type: string;
  label?: string;
}

// 组件Props接口
interface VideoPlayerProps {
  sources?: VideoSource[];
  poster?: string;
  fluid?: boolean;
  responsive?: boolean;
  width?: number;
  height?: number;
  preload?: 'auto' | 'metadata' | 'none';
  autoplay?: boolean | 'muted' | 'play' | 'any';
  muted?: boolean;
  loop?: boolean;
  controls?: boolean;
  playbackRates?: number[];
  language?: string;
  languages?: Record<string, any>;
  videoClass?: string;
  options?: any;
}

// 组件事件接口
interface VideoPlayerEmits {
  (e: 'ready', player: Player): void;

  (e: 'play'): void;

  (e: 'playing'): void;

  (e: 'pause'): void;

  (e: 'ended'): void;

  (e: 'timeupdate', currentTime: number): void;

  (e: 'volumechange', volume: number): void;

  (e: 'error', error: any): void;

  (e: 'loadstart'): void;

  (e: 'loadeddata'): void;

  (e: 'loadedmetadata'): void;

  (e: 'canplay'): void;

  (e: 'canplaythrough'): void;

  (e: 'waiting'): void;

  (e: 'seeking'): void;

  (e: 'seeked'): void;

  (e: 'fullscreenchange', isFullscreen: boolean): void;
}

export type { VideoSource, VideoPlayerProps, VideoPlayerEmits };
