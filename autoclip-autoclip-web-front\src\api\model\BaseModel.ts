export interface BasicPageParams {
  pageNum?: number;
  pageSize?: number;
  totalPage?: number;
  total?: number | string;
}

export interface BasicFetchPageResult<T> {
  list: T[];
  pageNum: number;
  pageSize: number;
  totalPage?: number;
  total?: number;
}

export interface PageParam {
  pageSize?: number;
  pageNo?: number;
}

// 分页数据公共返回
export interface PageResult<T> {
  list: T; // 数据
  total: number; // 总量
}
