<!-- 
  迁移后的登录表单
  逐步将 Element Plus 按钮替换为 Inspira UI 按钮
-->
<template>
  <div
    class="relative w-full max-w-md rounded-lg border border-gray-100 bg-white shadow-2xl"
  >
    <!-- 关闭按钮 - 已迁移到 Inspira UI -->
    <InspiraButton
      type="text"
      :circle="true"
      icon="i-mdi:close"
      class="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
      @click="closeLogin"
    />

    <div class="pt-4 text-center text-xs text-orange-400">
      由于政策原因，暂时只能使用邮箱登录，如无法登陆请联系我 ！
    </div>
    <div class="space-y-6 p-6 pt-4">
      <!-- 登录/注册标题 -->
      <div class="text-center">
        <h2 class="mb-6 text-xl font-bold text-gray-800">登录</h2>
        <!-- 登录方式切换 -->
        <el-radio-group
          v-model="loginType"
          class="m-6 flex justify-center space-x-6"
        >
          <el-radio value="password"> 账号密码登录 </el-radio>
          <el-radio value="authCode"> 验证码登录 </el-radio>
        </el-radio-group>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginFormProps"
        :rules="loginRules"
        @submit.prevent="handleLogin"
        class="space-y-4"
      >
        <el-form-item prop="identifier" class="!mb-4">
          <el-input
            v-model="loginFormProps.identifier"
            placeholder="请输入手机号/邮箱"
            class="!h-12"
            size="large"
          >
            <template #prefix>
              <Icon icon="i-mdi:account" class="text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <!-- 密码/验证码输入 -->
        <el-form-item prop="passwordOrCode" class="!mb-4">
          <el-input
            v-model="loginFormProps.passwordOrCode"
            :type="loginType === 'password' ? 'password' : 'text'"
            :placeholder="
              loginType === 'password' ? '请输入密码' : '请输入验证码'
            "
            :autocomplete="
              loginType === 'password' ? 'current-password' : 'off'
            "
            class="!h-12"
            size="large"
            :show-password="loginType === 'password'"
          >
            <template #prefix>
              <Icon
                :icon="
                  loginType === 'password' ? 'i-mdi:lock' : 'i-mdi:key-variant'
                "
                class="text-gray-400"
              />
            </template>
            <!-- 获取验证码按钮 - 已迁移到 Inspira UI -->
            <template v-if="loginType === 'authCode'" #suffix>
              <InspiraButton
                type="text"
                size="small"
                :disabled="countdown > 0"
                class="text-primary px-3 py-1 text-sm"
                @click="getVerificationCode"
              >
                {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
              </InspiraButton>
            </template>
          </el-input>
        </el-form-item>

        <!-- 记住密码 & 忘记密码 -->
        <transition name="fade" mode="out-in">
          <div
            v-if="loginType === 'password'"
            class="!mb-4 flex items-center justify-between"
          >
            <el-checkbox v-model="remember" class="text-sm text-gray-600">
              记住密码
            </el-checkbox>
            <el-link
              type="primary"
              @click="switchForm(FormType.FORGEPASSWORD)"
              :underline="'never'"
              class="!text-sm"
            >
              忘记密码？
            </el-link>
          </div>
        </transition>

        <!-- 登录按钮 - 已迁移到 Inspira UI -->
        <el-form-item class="!mb-0">
          <InspiraButton
            type="primary"
            size="large"
            :block="true"
            :loading="loading"
            animation="rainbow"
            class="h-12 font-medium"
            @click="handleLogin"
          >
            登陆
          </InspiraButton>
        </el-form-item>
      </el-form>

      <el-divider class="!my-6">
        <span class="whitespace-nowrap text-sm text-gray-500"
          >或使用其他方式登录</span
        >
      </el-divider>

      <!-- 社交登录按钮 - 已迁移到 Inspira UI -->
      <div class="flex justify-center space-x-5">
        <InspiraButton
          v-for="(item, key) in socialList"
          :key="key"
          type="text"
          :circle="true"
          :icon="item.icon"
          class="h-10 w-10 border border-gray-300 text-xl text-gray-600 hover:border-primary hover:text-primary"
          @click="doSocialLogin(item.type)"
        />
      </div>

      <!-- 登录/注册切换提示 -->
      <p class="text-center text-sm text-gray-500">
        还没有账号?
        <el-link
          type="primary"
          :underline="'never'"
          @click="switchForm(FormType.REGISTER)"
          class="hover:underline"
        >
          立即注册
        </el-link>
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import AuthUtil, { getIdentifierType, SmsSceneEnum } from '@/api/member/auth';
import { Icon } from '@/components/Icon';
import InspiraButton from '@/components/inspira/Button/InspiraButton.vue';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@/hooks/web/useMessage';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance, FormItemRule, FormRules } from 'element-plus';
import { computed, reactive, ref } from 'vue';
import { FormType } from './useLogin';

const { t } = useI18n();

const { $message } = useMessage();

// Props 类型定义
interface Props {}

// Emits 类型定义
type Emits = {
  (event: 'close'): void;
  (event: 'switch-form', formType: FormType): void;
};

defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单引用
const loginFormRef = ref<FormInstance>();

// 登录类型
const loginType = ref<'password' | 'authCode'>('password');

// 用户名密码登录表单
interface LoginFormProps {
  identifier: string;
  passwordOrCode: string;
}

// 状态管理
const remember = ref(false);
const loading = ref(false);
const countdown = ref(0);

// 登录表单
const loginFormProps = reactive<LoginFormProps>({
  identifier: '',
  passwordOrCode: '',
});

// 表单验证规则
const loginRules = computed<FormRules>(() => {
  const baseRules: Recordable<Array<FormItemRule>> = {
    identifier: [
      {
        required: true,
        message: '请输入手机号或邮箱',
        trigger: 'blur',
      },
    ],
    passwordOrCode: [
      {
        required: true,
        message: loginType.value === 'password' ? '请输入密码' : '请输入验证码',
        trigger: 'blur',
      },
    ],
  };

  if (loginType.value === 'authCode') {
    baseRules.identifier?.push({
      pattern: /^1[3-9]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入正确的手机号或邮箱',
      trigger: 'blur',
    });
    baseRules.passwordOrCode?.push({
      pattern: /^\d{4,6}$/,
      message: '请输入4-6位数字验证码',
      trigger: 'blur',
    });
  }

  return baseRules as FormRules;
});

const resetForm = () => {
  // 重置登录表单
  loginFormProps.identifier = '';
  loginFormProps.passwordOrCode = '';

  // 重置表单验证状态
  loginFormRef.value?.resetFields();

  // 重置状态
  loginType.value = 'password';
  countdown.value = 0;
  loading.value = false;
};

// 关闭模态框
const closeLogin = () => {
  emit('close');
  resetForm();
};

// 切换表单类型（登录/注册）
const switchForm = (form: FormType) => {
  emit('switch-form', form);
};

const timer = ref<NodeJS.Timeout>();

// 获取登录验证码
const getVerificationCode = async () => {
  const identifier = loginFormProps.identifier;
  const identifierType = getIdentifierType(identifier);

  // 开始倒计时
  countdown.value = 60;
  timer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer.value);
    }
  }, 1000);

  AuthUtil.sendAuthCode(identifier, identifierType, SmsSceneEnum.MEMBER_LOGIN)
    .then(() => {
      $message.success('验证码已发送，请注意查收');
    })
    .catch(() => {
      countdown.value = 0;
      clearInterval(timer.value);
    });
};

const socialList = [
  { icon: 'i-mdi:wechat', type: 30 },
  { icon: 'i-mdi:qqchat', type: 0 },
  { icon: 'i-ant-design:alipay-circle-filled', type: 0 },
];

const loginLoading = ref(false);
const redirect = ref<string>('');

const doSocialLogin = async (type: number) => {
  if (type === 0) {
    $message.error(t('sys.api.apiRequestFailed'));
  } else {
    loginLoading.value = true;
    // 计算 redirectUri
    const redirectUri =
      location.origin +
      '/social-login?' +
      encodeURIComponent(`type=${type}&redirect=${redirect.value || '/'}`);

    // 进行跳转
    window.location.href = await AuthUtil.socialAuthRedirect(
      type,
      encodeURIComponent(redirectUri),
    );
  }
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;
  await loginFormRef.value.validate();

  let form = loginFormProps;
  loading.value = true;

  const userStore = useUserStore();

  userStore
    .login(
      {
        identifier: form.identifier,
        identifierType: getIdentifierType(form.identifier),
        password: form.passwordOrCode,
        code: form.passwordOrCode,
      },
      loginType.value === 'authCode',
    )
    .then(() => {
      $message.success('登陆成功');
      closeLogin();
    })
    .finally(() => {
      countdown.value = 0;
      loading.value = false;
      loginLoading.value = false;
      clearInterval(timer.value);
    });
};

watch(loginType, () => {
  loginFormRef.value?.resetFields();
});
</script>

<style scoped lang="scss">
/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid rgb(229 231 235);
  padding: 12px;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(156 163 175);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
}

:deep(.el-radio-group) {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-radio__input) {
  display: none;
}

:deep(.el-radio__label) {
  padding-left: 0;
  color: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

:deep(.el-radio.is-checked .el-radio__label) {
  color: var(--el-color-primary);
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 4px;
}

:deep(.el-checkbox__label) {
  color: rgb(75 85 99);
  font-size: 14px;
}

:deep(.el-divider__text) {
  background-color: white;
  color: rgb(107 114 128);
}

/* Inspira UI 按钮的特殊样式调整 */
.text-primary {
  color: var(--el-color-primary) !important;
}

.hover\:text-primary:hover {
  color: var(--el-color-primary) !important;
}

.hover\:border-primary:hover {
  border-color: var(--el-color-primary) !important;
}
</style>