<template>
  <div
    class="drag-drop-upload"
    :class="{ 'is-dragging': isDragging }"
    @dragenter.prevent="handleDragEnter"
    @dragleave.prevent="handleDragLeave"
    @dragover.prevent
    @drop.prevent="handleDrop"
  >
    <slot></slot>
    <div v-if="isDragging" class="drag-overlay">
      <div class="drag-content">
        <div class="text-2xl font-bold text-blue-600">释放以上传文件</div>
        <div class="mt-2 text-sm text-gray-500">支持视频和图片文件</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue';

interface Props {
  accept?: string;
  multiple?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  accept: 'video/*,image/*',
  multiple: false,
});

const emit = defineEmits<{
  (e: 'file-selected', files: File[]): void;
  (e: 'drag-enter', event: DragEvent): void;
  (e: 'drag-leave', event: DragEvent): void;
}>();

const isDragging = ref(false);
let dragCounter = 0;

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  dragCounter++;
  if (dragCounter === 1) {
    isDragging.value = true;
    emit('drag-enter', event);
  }
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  dragCounter--;
  if (dragCounter === 0) {
    isDragging.value = false;
    emit('drag-leave', event);
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
  dragCounter = 0;

  const files = Array.from(event.dataTransfer?.files || []);
  const acceptedFiles = files.filter((file) => {
    const fileType = file.type;
    return props.accept.split(',').some((type) => {
      if (type.endsWith('/*')) {
        const category = type.split('/')[0];
        return fileType.startsWith(category);
      }
      return fileType === type;
    });
  });

  if (acceptedFiles.length > 0) {
    emit('file-selected', props.multiple ? acceptedFiles : [acceptedFiles[0]]);
  }
};

// 清理计数器
onUnmounted(() => {
  dragCounter = 0;
});
</script>

<style scoped>
.drag-drop-upload {
  position: relative;
  width: 100%;
  height: 100%;
}

.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drag-content {
  background-color: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
  text-align: center;
}
</style>
