import { useProjectStore } from '@/store/editor/react/projectStore';
import { Player, PlayerRef } from '@remotion/player';
import React, { useEffect, useState } from 'react';
import type { Ref } from 'vue';
import Composition from './sequence/composition';

const fps = 30;

export const PreviewPlayer = ({
  onPaused,
  onPlayed,
  playerRefInstance,
}: {
  onPaused: () => void;
  onPlayed: () => void;
  playerRefInstance: Ref<PlayerRef | null | undefined>;
}) => {
  const playerRef: React.RefObject<PlayerRef | null> = React.createRef();
  const [currentRef, setCurrentRef] = useState<{
    isPlaying: boolean;
    isMuted: boolean;
    duration: number;
    durationInFrames: number;
  }>({
    isPlaying: false,
    isMuted: false,
    duration: 0,
    durationInFrames: 1,
  });

  useProjectStore.subscribe((state) => {
    const frame = Math.round(state.currentTime * fps);
    if (playerRef.current && !currentRef.isPlaying) {
      playerRef.current.pause();
      playerRef.current.seekTo(frame);
    }
  });

  useProjectStore.subscribe((state) => {
    const curRef: {
      isPlaying: boolean;
      isMuted: boolean;
      duration: number;
      durationInFrames: number;
    } = {} as any;
    curRef.isPlaying = state.isPlaying;
    curRef.isMuted = state.isMuted;
    curRef.duration = state.duration;
    curRef.durationInFrames = Math.floor(state.duration * fps) + 1;
    setCurrentRef(curRef);
  });

  useEffect(() => {
    if (playerRef.current) {
      playerRefInstance.value = playerRef.current;
    }
  }, []);

  useEffect(() => {
    playerRef?.current?.addEventListener('play', () => {
      onPlayed();
    });
    playerRef?.current?.addEventListener('pause', () => {
      onPaused();
    });
    return () => {
      playerRef?.current?.removeEventListener('play', () => {
        onPlayed();
      });
      playerRef?.current?.removeEventListener('pause', () => {
        onPaused();
      });
    };
  }, [playerRef]);

  useEffect(() => {
    if (!playerRef.current) return;
    if (currentRef.isPlaying) {
      playerRef.current.play();
    } else {
      playerRef.current.pause();
    }
  }, [currentRef.isPlaying]);

  useEffect(() => {
    if (!playerRef.current) return;
    if (currentRef.isMuted) {
      playerRef.current.mute();
    } else {
      playerRef.current.unmute();
    }
  }, [currentRef.isMuted]);

  return (
    <Player
      acknowledgeRemotionLicense={true}
      ref={playerRef}
      component={Composition}
      inputProps={{}}
      durationInFrames={currentRef.durationInFrames}
      compositionWidth={1920}
      compositionHeight={1080}
      fps={fps}
      style={{ width: '100%', height: '100%' }}
      controls
    />
  );
};
