import { defineStore } from 'pinia';
import { store } from '@/store';
import {
  ACCESS_TOKEN_KEY,
  REFRESH_TOKEN_KEY,
  USER_INFO_KEY,
} from '@/enums/cacheEnum';
import { PageEnum } from '@/enums/pageEnum';
import { getAuthCache, setAuthCache } from '@/utils/auth';
import { useMessage } from '@/hooks/web/useMessage';
import { router } from '@/router';
import { useI18n } from '@/hooks/web/useI18n';
import AuthUtil, {
  LoginPasswordParams,
  LoginAuthCodeParams,
  TokenType,
} from '@/api/member/auth';
import UserApi, { UserInfo } from '@/api/member/user';

interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  sessionTimeout?: boolean;
  lastUpdateTime: number;
  refreshToken?: string;
}

export const useUserStore = defineStore('app-user', {
  state: (): UserState => ({
    // user info
    userInfo: null,
    // token
    token: undefined,
    // Whether the login expired
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
    refreshToken: undefined,
  }),
  getters: {
    getIsSetUser(): boolean {
      return this.userInfo !== null;
    },
    getUserInfo(): UserInfo {
      return this.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getToken(): string {
      return this.token || getAuthCache<string>(ACCESS_TOKEN_KEY);
    },
    getRefreshToken(): string {
      return this.refreshToken || getAuthCache<string>(REFRESH_TOKEN_KEY);
    },
    getSessionTimeout(): boolean {
      return !!this.sessionTimeout;
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
  },
  actions: {
    setAccessToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(ACCESS_TOKEN_KEY, info);
    },
    setRefreshToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(REFRESH_TOKEN_KEY, info);
    },
    setUserInfo(info: UserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setSessionTimeout(flag: boolean) {
      this.sessionTimeout = flag;
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.sessionTimeout = false;
    },
    /**
     * @description: login
     */
    async login(
      params: LoginPasswordParams | LoginAuthCodeParams,
      sms: boolean,
    ): Promise<UserInfo | null> {
      try {
        let data: TokenType;
        if (sms) {
          params = params as LoginAuthCodeParams;
          data = await AuthUtil.authCodeLogin({
            identifier: params.identifier,
            identifierType: params.identifierType,
            code: params.code,
          });
        } else {
          params = params as LoginPasswordParams;
          data = await AuthUtil.login({
            identifier: params.identifier,
            identifierType: params.identifierType,
            password: params.password,
          });
        }
        const { accessToken, refreshToken } = data;

        // save token
        this.setAccessToken(accessToken);
        this.setRefreshToken(refreshToken);
        return this.afterLoginAction();
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async refreshTokenAction(): Promise<UserInfo | null> {
      if (!this.getRefreshToken) return null;
      try {
        const data = await AuthUtil.refreshToken(this.getRefreshToken);
        const { accessToken, refreshToken } = data;
        // save token
        this.setAccessToken(accessToken);
        this.setRefreshToken(refreshToken);
        return this.afterLoginAction();
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async afterLoginAction(): Promise<UserInfo | null> {
      if (!this.getToken) return null;
      // get user info
      const userInfo = await this.getUserInfoAction();

      const sessionTimeout = this.sessionTimeout;
      if (sessionTimeout) {
        this.setSessionTimeout(false);
      }
      return userInfo;
    },
    async getUserInfoAction(): Promise<UserInfo | null> {
      if (!this.getToken) return null;
      const userInfo = await UserApi.getUserInfo();
      this.setUserInfo(userInfo);
      return userInfo;
    },
    /**
     * @description: logout
     */
    async logout(goLogin = false) {
      if (this.getToken) {
        await AuthUtil.doLogout();
      }
      this.setAccessToken(undefined);
      this.setRefreshToken(undefined);
      this.setSessionTimeout(false);
      this.setUserInfo(null);
      goLogin && router.replace(PageEnum.BASE_HOME);
    },

    /**
     * @description: Confirm before logging out
     */
    confirmLoginOut() {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        title: t('sys.app.logoutTip'),
        content: t('sys.app.logoutMessage'),
        callback: async () => {
          await this.logout(true);
        },
      });
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
