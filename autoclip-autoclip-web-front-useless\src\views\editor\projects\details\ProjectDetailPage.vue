<script setup lang="ts">
import { getProject, storeProject, useProjectStore } from '@/store/editor';
import { useProjectsStore } from '@/store/editor/slices/projectsStore';
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// Import components
import UploadMedia from '../../AssetsPanel/AddButtons/UploadMedia.vue';
import ExportButton from '../../AssetsPanel/SidebarButtons/ExportButton.vue';
import HomeButton from '../../AssetsPanel/SidebarButtons/HomeButton.vue';
import LibraryButton from '../../AssetsPanel/SidebarButtons/LibraryButton.vue';
import TextButton from '../../AssetsPanel/SidebarButtons/TextButton.vue';
import AddText from '../../AssetsPanel/tools-section/AddText.vue';
import ExportList from '../../AssetsPanel/tools-section/ExportList.vue';
import MediaList from '../../AssetsPanel/tools-section/MediaList.vue';
import ProjectName from '../../player/ProjectName.vue';
import PlayerViewWrapper from '../../player/remotion/PlayerViewWrapper.vue';
import MediaProperties from '../../PropertiesSection/MediaProperties.vue';
import TextProperties from '../../PropertiesSection/TextProperties.vue';
import Timeline from '../../timeline/Timeline.vue';

const route = useRoute();
const router = useRouter();
const projectStore = useProjectStore();
const projectsStore = useProjectsStore();

const isLoading = ref(true);
const id = route.params.id as string;

// Load project when page is loaded
const loadProject = async () => {
  if (route.params.id) {
    isLoading.value = true;
    try {
      const project = await getProject(id);
      if (project) {
        projectsStore.setCurrentProject(id);
        isLoading.value = false;
      } else {
        router.push('/404');
      }
    } catch (error) {
      console.error('Error loading project:', error);
      router.push('/404');
    }
  }
};

// Load project state when current project changes
const loadProjectState = async () => {
  if (projectsStore.getCurrentProjectId) {
    try {
      const project = await getProject(projectsStore.getCurrentProjectId);
      if (project) {
        projectStore.rehydrate(project);
      }
    } catch (error) {
      console.error('Error loading project state:', error);
    }
  }
};

const saveProject = async () => {
  const projectId = projectStore.getId;
  if (!projectStore || projectId !== projectsStore.getCurrentProjectId) {
    return;
  }

  try {
    await storeProject(toRaw(projectStore.$state));
  } catch (error) {
    console.error('Error saving project:', error);
  }
};

const handleFocus = (section: 'media' | 'text' | 'export') => {
  projectStore.setActiveSection(section);
};

// Watchers
watch(() => projectsStore.getCurrentProjectId, loadProjectState, {
  immediate: true,
});
watch(() => projectStore, saveProject, { deep: true });

onMounted(() => {
  loadProject();
});
</script>

<template>
  <div class="flex h-screen select-none flex-col bg-white">
    <!-- Loading screen -->
    <div
      v-if="isLoading"
      class="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90"
    >
      <div
        class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-6 shadow-lg"
      >
        <div
          class="border-t-opacity-100 h-16 w-16 animate-spin rounded-full border-4 border-r-blue-600 border-t-blue-600 border-opacity-30"
        ></div>
        <p class="mt-4 text-lg text-gray-900">Loading project...</p>
      </div>
    </div>

    <div class="flex flex-1 overflow-hidden">
      <!-- Left Sidebar - Buttons -->
      <div
        class="min-w-[60px] max-w-[100px] flex-[0.1] overflow-y-auto border-r border-gray-300 bg-gray-50 p-4"
      >
        <div class="flex flex-col space-y-2">
          <HomeButton />
          <TextButton @click="handleFocus('text')" />
          <LibraryButton @click="handleFocus('media')" />
          <ExportButton @click="handleFocus('export')" />
          <!-- TODO: add shortcuts guide but in a better way -->
          <!-- <ShortcutsButton @click="handleFocus('export')" /> -->
        </div>
      </div>

      <!-- Add media and text -->
      <div
        class="min-w-[200px] flex-[0.3] overflow-y-auto border-r border-gray-300 bg-white p-4"
      >
        <div v-if="projectStore.getActiveSection === 'media'">
          <h2
            class="mb-2 flex flex-row items-center justify-center gap-2 text-lg font-semibold text-gray-900"
          >
            <UploadMedia />
          </h2>
          <MediaList />
        </div>

        <div v-if="projectStore.getActiveSection === 'text'">
          <AddText />
        </div>

        <div v-if="projectStore.getActiveSection === 'export'">
          <h2 class="mb-4 text-lg font-semibold text-gray-900">Export</h2>
          <ExportList />
        </div>
      </div>

      <!-- Center - Video Preview -->
      <div
        class="flex flex-[1] flex-col items-center justify-center overflow-hidden bg-gray-50"
      >
        <ProjectName />
        <PlayerViewWrapper />
      </div>

      <!-- Right Sidebar - Element Properties -->
      <div
        class="min-w-[200px] flex-[0.4] overflow-y-auto border-l border-gray-300 bg-white p-4"
      >
        <div v-if="projectStore.getActiveElement === 'media'">
          <h2 class="mb-4 text-lg font-semibold text-gray-900">
            Media Properties
          </h2>
          <MediaProperties />
        </div>

        <div v-if="projectStore.getActiveElement === 'text'">
          <h2 class="mb-4 text-lg font-semibold text-gray-900">
            Text Properties
          </h2>
          <TextProperties />
        </div>
      </div>
    </div>

    <!-- Timeline at bottom -->
    <div class="flex flex-row border-t border-gray-300">
      <div
        class="mt-20 flex flex-col items-center justify-center border-r border-gray-300 bg-gray-100"
      >
        <div class="relative h-16">
          <div class="flex items-center gap-2 p-4">
            <img
              alt="Video"
              class="h-auto max-h-[30px] w-auto max-w-[30px] opacity-70"
              height="30"
              width="30"
              src="https://www.svgrepo.com/show/532727/video.svg"
            />
          </div>
        </div>

        <div class="relative h-16">
          <div class="flex items-center gap-2 p-4">
            <img
              alt="Audio"
              class="h-auto max-h-[30px] w-auto max-w-[30px] opacity-70"
              height="30"
              width="30"
              src="https://www.svgrepo.com/show/532708/music.svg"
            />
          </div>
        </div>

        <div class="relative h-16">
          <div class="flex items-center gap-2 p-4">
            <img
              alt="Image"
              class="h-auto max-h-[30px] w-auto max-w-[30px] opacity-70"
              height="30"
              width="30"
              src="https://www.svgrepo.com/show/535454/image.svg"
            />
          </div>
        </div>

        <div class="relative h-16">
          <div class="flex items-center gap-2 p-4">
            <img
              alt="Text"
              class="h-auto max-h-[30px] w-auto max-w-[30px] opacity-70"
              height="30"
              width="30"
              src="https://www.svgrepo.com/show/535686/text.svg"
            />
          </div>
        </div>
      </div>
      <Timeline />
    </div>
  </div>
</template>
