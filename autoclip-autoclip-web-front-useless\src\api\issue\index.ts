import { defHttp } from '@/utils/http/axios';
import type { IssueRespVO, IssueCreateReqVO, PageResult } from '@/types/issue';

const IssueApi = {
  // 创建问题反馈
  createIssue: (data: IssueCreateReqVO) => {
    return defHttp.request<number>({
      url: '/autoclip/issue/create',
      method: 'POST',
      data,
    });
  },

  // 获取我的问题反馈列表
  getMyIssues: (params: any) => {
    return defHttp.request<PageResult<IssueRespVO>>({
      url: '/autoclip/issue/list',
      method: 'GET',
      params,
    });
  },

  // 获取问题反馈详情
  getIssue: (issueId: number) => {
    return defHttp.request<IssueRespVO>({
      url: `/autoclip/issue/${issueId}`,
      method: 'GET',
    });
  },
};

export default IssueApi;
