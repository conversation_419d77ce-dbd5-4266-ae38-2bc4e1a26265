import { defHttp } from '@/utils/http/axios';

const AddressApi = {
  // 获得用户收件地址列表
  getAddressList: () => {
    return defHttp.request({
      url: '/member/address/list',
      method: 'GET',
    });
  },
  // 创建用户收件地址
  createAddress: (data) => {
    return defHttp.request({
      url: '/member/address/create',
      method: 'POST',
      data,
    });
  },
  // 更新用户收件地址
  updateAddress: (data) => {
    return defHttp.request({
      url: '/member/address/update',
      method: 'PUT',
      data,
    });
  },
  // 获得用户收件地址
  getAddress: (id) => {
    return defHttp.request({
      url: '/member/address/get',
      method: 'GET',
      params: { id },
    });
  },
  // 删除用户收件地址
  deleteAddress: (id) => {
    return defHttp.request({
      url: '/member/address/delete',
      method: 'DELETE',
      params: { id },
    });
  },
};

export default AddressApi;
