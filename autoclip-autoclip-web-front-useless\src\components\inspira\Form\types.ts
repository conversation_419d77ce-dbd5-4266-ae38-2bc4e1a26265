// Inspira Form 组件类型定义

import type { FormRules } from 'element-plus';

export interface InspiraFormProps {
  // 表单数据对象
  model?: Record<string, any>;
  
  // 表单验证规则
  rules?: FormRules;
  
  // 行内表单模式
  inline?: boolean;
  
  // 标签的位置
  labelPosition?: 'left' | 'right' | 'top';
  
  // 标签的宽度
  labelWidth?: string | number;
  
  // 表单域标签的后缀
  labelSuffix?: string;
  
  // 是否隐藏必填字段的标签旁边的红色星号
  hideRequiredAsterisk?: boolean;
  
  // 是否显示校验错误信息
  showMessage?: boolean;
  
  // 是否以行内形式展示校验信息
  inlineMessage?: boolean;
  
  // 是否在输入框中显示校验结果反馈图标
  statusIcon?: boolean;
  
  // 是否在 rules 属性改变后立即触发一次验证
  validateOnRuleChange?: boolean;
  
  // 用于控制该表单内组件的尺寸
  size?: 'large' | 'default' | 'small';
  
  // 是否禁用该表单内的所有组件
  disabled?: boolean;
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'fade' | 'slide' | 'bounce' | 'none';
}

export interface InspiraFormEmits {
  validate: [prop: string, isValid: boolean, message: string];
  submit: [event: Event];
}

export interface InspiraFormSlots {
  default: () => any;
}

// 表单项组件类型定义
export interface InspiraFormItemProps {
  // 表单域 model 字段
  prop?: string;
  
  // 标签文本
  label?: string;
  
  // 标签宽度
  labelWidth?: string | number;
  
  // 是否必填
  required?: boolean;
  
  // 表单验证规则
  rules?: FormRules[string];
  
  // 表单域验证错误信息
  error?: string;
  
  // 是否显示校验错误信息
  showMessage?: boolean;
  
  // 以行内形式展示校验信息
  inlineMessage?: boolean;
  
  // 用于控制该表单域下组件的尺寸
  size?: 'large' | 'default' | 'small';
  
  // 自定义类名
  class?: string;
  
  // 动画类型
  animation?: 'fade' | 'slide' | 'bounce' | 'none';
}

export interface InspiraFormItemEmits {
  // 表单项验证事件
  validate: [prop: string, isValid: boolean, message: string];
}

export interface InspiraFormItemSlots {
  default: () => any;
  label: () => any;
  error: () => any;
}

// 表单验证结果类型
export interface FormValidateResult {
  valid: boolean;
  fields?: Record<string, string[]>;
}

// 表单方法类型
export interface InspiraFormMethods {
  validate: (callback?: (valid: boolean, fields?: any) => void) => Promise<boolean>;
  validateField: (props: string | string[], callback?: (errorMessage?: string) => void) => void;
  resetFields: () => void;
  scrollToField: (prop: string) => void;
  clearValidate: (props?: string | string[]) => void;
}