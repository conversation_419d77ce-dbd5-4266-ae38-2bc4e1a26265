<template>
  <el-footer class="relative flex flex-col">
    <div class="min-h-[200px]"></div>
    <!-- 页脚内容，去掉pt-12，改为pt-4或pt-6 -->
    <div class="relative bg-[#5c6770] pb-8 pt-4">
      <div class="-top-78px absolute w-full">
        <svg
          viewBox="0 0 1920 80"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="h-80px block"
        >
          <path d="M0,40 Q480,80 960,40 T1920,40 V80H0Z" fill="#5c6770" />
        </svg>
      </div>
      <div
        class="mx-auto flex max-w-7xl flex-col items-end justify-between px-4 md:flex-row"
      >
        <!-- 左侧语言 -->
        <div class="mb-4 flex flex-col items-center md:mb-0 md:items-start">
          <button
            class="flex items-center rounded-full bg-white bg-opacity-80 px-6 py-2 text-lg font-semibold text-gray-700 shadow"
          >
            <span class="mr-2 text-xl">🌐</span>
            简体中文
          </button>
          <div class="mt-4 text-sm text-white">© restcut.com</div>
        </div>
        <!-- 右侧社交和链接 -->
        <div class="flex flex-col items-center md:items-end">
          <div
            class="flex flex-wrap items-end gap-x-6 gap-y-2 text-sm text-white"
          >
            <a
              href="https://beian.miit.gov.cn/"
              class="hover:underline"
              target="_blank"
              >浙ICP备2025177770号</a
            >
            <a href="https://beian.miit.gov.cn/" class="hover:underline"
              >浙公网安备 暂无</a
            >
            <a href="#" class="hover:underline">服务条款</a>
            <a href="#" class="hover:underline">一般条款和交易条件</a>
            <a href="#" class="hover:underline">隐私政策</a>
            <a href="#" class="hover:underline">Cookie政策</a>
            <a href="#" class="hover:underline">印记</a>
            <div class="flex flex-col items-center rounded-xl bg-white p-2">
              <span class="text-0.6rem text-gray-500">意见反馈</span>
              <img
                v-if="qrUrl"
                :src="qrUrl"
                alt="qrcode"
                width="100"
                height="100"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-footer>
</template>

<script setup lang="ts">
import { InfraFileApi } from '@/api/enum/infraFile';
import { generateQRUrl } from '@/utils/qrcode';

defineOptions({
  name: 'LayoutFooter',
});
const qrUrl = ref<string | null>(null);
onMounted(async () => {
  qrUrl.value = await generateQRUrl(InfraFileApi.GROUP_CHAT_SHORT_LINK);
});
</script>

<style scoped></style>
