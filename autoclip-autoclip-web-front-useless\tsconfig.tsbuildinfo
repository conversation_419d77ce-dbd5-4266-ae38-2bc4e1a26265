{"root": ["./src/main.ts", "./src/api/autoclip/auto_clip.ts", "./src/api/enum/AutoClipApi.ts", "./src/api/infra/file/index.ts", "./src/api/member/address.ts", "./src/api/member/auth.ts", "./src/api/member/point.ts", "./src/api/member/signin.ts", "./src/api/member/social.ts", "./src/api/member/user.ts", "./src/api/model/BaseModel.ts", "./src/api/model/uploadModel.ts", "./src/api/pay/channel.ts", "./src/api/pay/order.ts", "./src/api/pay/transfer.ts", "./src/api/pay/wallet.ts", "./src/api/system/notify/message/index.ts", "./src/assets/videos/pingpong.ts", "./src/components/Application/index.ts", "./src/components/Application/src/useAppContext.ts", "./src/components/Cropper/index.ts", "./src/components/Cropper/src/types.ts", "./src/components/Dialog/index.ts", "./src/components/Dropdown/index.ts", "./src/components/Dropdown/src/typing.ts", "./src/components/Form/index.ts", "./src/components/Form/src/componentMap.ts", "./src/components/Form/src/helper.ts", "./src/components/Form/src/types.ts", "./src/components/Icon/index.ts", "./src/components/Icon/data/icons.data.ts", "./src/components/Icon/src/create-icon.ts", "./src/components/Icon/src/index.ts", "./src/components/ImageViewer/index.ts", "./src/components/ImageViewer/src/types.ts", "./src/components/InputPassword/index.ts", "./src/components/Loading/index.ts", "./src/components/Loading/src/createLoading.ts", "./src/components/Loading/src/typing.ts", "./src/components/Loading/src/useLoading.ts", "./src/components/Login/index.ts", "./src/components/Login/src/useLogin.ts", "./src/components/VideoPlayer/types.ts", "./src/components/XButton/index.ts", "./src/constants/globals.ts", "./src/directives/clickOutside.ts", "./src/directives/index.ts", "./src/directives/loading.ts", "./src/directives/repeatClick.ts", "./src/directives/ripple/index.ts", "./src/enums/appEnum.ts", "./src/enums/breakpointEnum.ts", "./src/enums/cacheEnum.ts", "./src/enums/exceptionEnum.ts", "./src/enums/httpEnum.ts", "./src/enums/menuEnum.ts", "./src/enums/pageEnum.ts", "./src/enums/sizeEnum.ts", "./src/hooks/component/useFormItem.ts", "./src/hooks/component/usePageContext.ts", "./src/hooks/core/onMountedOrActivated.ts", "./src/hooks/core/useAttrs.ts", "./src/hooks/core/useContext.ts", "./src/hooks/core/useRefs.ts", "./src/hooks/core/useTimeout.ts", "./src/hooks/event/useBreakpoint.ts", "./src/hooks/event/useEventListener.ts", "./src/hooks/event/useIntersectionObserver.ts", "./src/hooks/event/useScroll.ts", "./src/hooks/event/useScrollTo.ts", "./src/hooks/event/useWindowSizeFn.ts", "./src/hooks/setting/index.ts", "./src/hooks/setting/useHeaderSetting.ts", "./src/hooks/setting/useMenuSetting.ts", "./src/hooks/setting/useRootSetting.ts", "./src/hooks/setting/useTransitionSetting.ts", "./src/hooks/web/useAppInject.ts", "./src/hooks/web/useDesign.ts", "./src/hooks/web/useFullContent.ts", "./src/hooks/web/useI18n.ts", "./src/hooks/web/useIcon.ts", "./src/hooks/web/useMessage.ts", "./src/hooks/web/usePage.ts", "./src/hooks/web/usePagination.ts", "./src/hooks/web/useScript.ts", "./src/hooks/web/useSortable.ts", "./src/hooks/web/useTitle.ts", "./src/hooks/web/useUpload.ts", "./src/layouts/default/content/useContentContext.ts", "./src/layouts/default/content/useContentViewHeight.ts", "./src/layouts/default/header/components/index.ts", "./src/layouts/default/header/components/Message/index.ts", "./src/layouts/page/transition.ts", "./src/locales/helper.ts", "./src/locales/setupI18n.ts", "./src/locales/useLocale.ts", "./src/locales/lang/en.ts", "./src/locales/lang/zh_CN.ts", "./src/logics/initAppConfig.ts", "./src/logics/error-handle/index.ts", "./src/logics/mitt/routeChange.ts", "./src/logics/theme/dark.ts", "./src/logics/theme/updateBackground.ts", "./src/logics/theme/updateColorWeak.ts", "./src/logics/theme/updateGrayMode.ts", "./src/logics/theme/updateTheme.ts", "./src/logics/theme/util.ts", "./src/router/constant.ts", "./src/router/index.ts", "./src/router/types.ts", "./src/router/guard/index.ts", "./src/router/routes/index.ts", "./src/router/routes/constantModules/assets.ts", "./src/router/routes/constantModules/autoClip.ts", "./src/router/routes/constantModules/editor.ts", "./src/router/routes/constantModules/history.ts", "./src/router/routes/constantModules/home.ts", "./src/router/routes/constantModules/pageNotFound.ts", "./src/router/routes/constantModules/user.ts", "./src/settings/componentSetting.ts", "./src/settings/designSetting.ts", "./src/settings/encryptionSetting.ts", "./src/settings/localeSetting.ts", "./src/settings/projectSetting.ts", "./src/settings/siteSetting.ts", "./src/store/index.ts", "./src/store/stateBridge.ts", "./src/store/editor/index.ts", "./src/store/editor/react/projectStore.ts", "./src/store/editor/slices/projectStore.ts", "./src/store/editor/slices/projectsStore.ts", "./src/store/modules/app.ts", "./src/store/modules/errorLog.ts", "./src/store/modules/locale.ts", "./src/store/modules/menu.ts", "./src/store/modules/user.ts", "./src/types/auto-imports-components.d.ts", "./src/types/auto-imports.d.ts", "./src/types/components.d.ts", "./src/types/editor.ts", "./src/types/form.d.ts", "./src/utils/bem.ts", "./src/utils/cipher.ts", "./src/utils/color.ts", "./src/utils/constants.ts", "./src/utils/data.ts", "./src/utils/dateUtil.ts", "./src/utils/dom.ts", "./src/utils/domUtils.ts", "./src/utils/env.ts", "./src/utils/extractConfigs.ts", "./src/utils/extractThumbnail.ts", "./src/utils/filt.ts", "./src/utils/formRules.ts", "./src/utils/index.ts", "./src/utils/inference.ts", "./src/utils/is.ts", "./src/utils/jsencrypt.ts", "./src/utils/letter.ts", "./src/utils/log.ts", "./src/utils/mitt.ts", "./src/utils/outline.ts", "./src/utils/propTypes.ts", "./src/utils/props.ts", "./src/utils/tsxHelper.ts", "./src/utils/types.ts", "./src/utils/utils.ts", "./src/utils/uuid.ts", "./src/utils/videoUtils.ts", "./src/utils/auth/index.ts", "./src/utils/cache/index.ts", "./src/utils/cache/memory.ts", "./src/utils/cache/persistent.ts", "./src/utils/cache/storageCache.ts", "./src/utils/event/index.ts", "./src/utils/file/base64Conver.ts", "./src/utils/file/download.ts", "./src/utils/helper/treeHelper.ts", "./src/utils/http/helper.ts", "./src/utils/http/axios/Axios.ts", "./src/utils/http/axios/axiosCancel.ts", "./src/utils/http/axios/axiosRetry.ts", "./src/utils/http/axios/axiosTransform.ts", "./src/utils/http/axios/checkStatus.ts", "./src/utils/http/axios/helper.ts", "./src/utils/http/axios/index.ts", "./src/views/autoclip/pingpong/components/types.ts", "./src/views/editor/AssetsPanel/index.ts", "./src/views/editor/keys/index.ts", "./src/views/editor/keys/useGlobalKeyHandler.ts", "./src/views/editor/projects/index.ts", "./src/views/sys/exception/index.ts", "./src/utils/factory/createAsyncComponent.tsx", "./src/utils/helper/tsxHelper.tsx", "./src/views/editor/player/remotion/Player.tsx", "./src/views/editor/player/remotion/sequence/composition.tsx", "./src/views/editor/player/remotion/sequence/sequence-item.tsx", "./src/App.vue", "./src/components/Application/src/AppDarkModeToggle.vue", "./src/components/Application/src/AppLocalePicker.vue", "./src/components/Application/src/AppLogo.vue", "./src/components/Application/src/AppProvider.vue", "./src/components/Cropper/src/CopperModal.vue", "./src/components/Cropper/src/Cropper.vue", "./src/components/Cropper/src/CropperAvatar.vue", "./src/components/Dialog/src/Dialog.vue", "./src/components/DragDropUpload/index.vue", "./src/components/Dropdown/src/Dropdown.vue", "./src/components/Form/src/Form.vue", "./src/components/Form/src/FormItem.vue", "./src/components/Form/src/components/CheckboxOptions.vue", "./src/components/Form/src/components/RadioOptions.vue", "./src/components/Form/src/components/SelectOptions.vue", "./src/components/Icon/Icon.vue", "./src/components/ImageViewer/src/ImageViewer.vue", "./src/components/InputPassword/src/InputPassword.vue", "./src/components/Loading/src/Loading.vue", "./src/components/Login/src/ForgePassword.vue", "./src/components/Login/src/Login.vue", "./src/components/Login/src/LoginForm.vue", "./src/components/Login/src/RegisterForm/ChangePasswordForm.vue", "./src/components/Login/src/RegisterForm/IdentifierRegisterForm.vue", "./src/components/Login/src/RegisterForm/index.vue", "./src/components/PauseAfterPlayVideo/index.vue", "./src/components/VideoInfoDialog/index.vue", "./src/components/VideoPlayer/InterVideoPlayer.vue", "./src/components/VideoPlayer/index.vue", "./src/components/XButton/src/XButton.vue", "./src/components/XButton/src/XTextButton.vue", "./src/layouts/default/index.vue", "./src/layouts/default/content/index.vue", "./src/layouts/default/footer/index.vue", "./src/layouts/default/header/index.vue", "./src/layouts/default/header/components/ErrorAction.vue", "./src/layouts/default/header/components/MenuTrigger.vue", "./src/layouts/default/header/components/UserInfo.vue", "./src/layouts/default/header/components/Message/src/Message.vue", "./src/layouts/default/menu/src/Menu.vue", "./src/layouts/default/menu/src/MenuItem.vue", "./src/layouts/default/menu/src/SubMenu.vue", "./src/layouts/default/sidebar/index.vue", "./src/layouts/page/index.vue", "./src/views/assets/index.vue", "./src/views/autoclip/pingpong/MessageReceiver.vue", "./src/views/autoclip/pingpong/PingPongLeftPanel.vue", "./src/views/autoclip/pingpong/PingPongRightPanel.vue", "./src/views/autoclip/pingpong/VideoUploader.vue", "./src/views/autoclip/pingpong/index.vue", "./src/views/autoclip/pingpong/components/ProcessingOverlay.vue", "./src/views/editor/AssetsPanel/AddButtons/AddMedia.vue", "./src/views/editor/AssetsPanel/AddButtons/UploadMedia.vue", "./src/views/editor/AssetsPanel/SidebarButtons/ExportButton.vue", "./src/views/editor/AssetsPanel/SidebarButtons/HomeButton.vue", "./src/views/editor/AssetsPanel/SidebarButtons/LibraryButton.vue", "./src/views/editor/AssetsPanel/SidebarButtons/ShortcutsButton.vue", "./src/views/editor/AssetsPanel/SidebarButtons/TextButton.vue", "./src/views/editor/AssetsPanel/tools-section/AddText.vue", "./src/views/editor/AssetsPanel/tools-section/ExportList.vue", "./src/views/editor/AssetsPanel/tools-section/MediaList.vue", "./src/views/editor/PropertiesSection/MediaProperties.vue", "./src/views/editor/PropertiesSection/TextProperties.vue", "./src/views/editor/keys/GlobalKeyHandler.vue", "./src/views/editor/player/ProjectName.vue", "./src/views/editor/player/remotion/PlayerViewWrapper.vue", "./src/views/editor/projects/ProjectsPage.vue", "./src/views/editor/projects/details/ProjectDetailPage.vue", "./src/views/editor/render/Ffmpeg/Export.vue", "./src/views/editor/render/Ffmpeg/Ffmpeg.vue", "./src/views/editor/render/Ffmpeg/FfmpegRender.vue", "./src/views/editor/render/Ffmpeg/ProgressBar.vue", "./src/views/editor/render/Ffmpeg/RenderOptions.vue", "./src/views/editor/timeline/Header.vue", "./src/views/editor/timeline/Timeline.vue", "./src/views/editor/timeline/elements-timeline/AudioTimeline.vue", "./src/views/editor/timeline/elements-timeline/ImageTimeline.vue", "./src/views/editor/timeline/elements-timeline/TextTimeline.vue", "./src/views/editor/timeline/elements-timeline/VideoTimeline.vue", "./src/views/history/index.vue", "./src/views/home/<USER>", "./src/views/profile/UserProfileMenu.vue", "./src/views/profile/index.vue", "./src/views/profile/admin/UserAvatar.vue", "./src/views/profile/admin/index.vue", "./src/views/profile/security/ResetPwd.vue", "./src/views/profile/security/UserSocial.vue", "./src/views/profile/security/XTextButton.vue", "./src/views/profile/security/index.vue", "./src/views/sys/exception/Exception.vue", "./src/views/sys/redirect/index.vue", "./types/axios.d.ts", "./types/config.d.ts", "./types/global.d.ts", "./types/icon.d.ts", "./types/index.d.ts", "./types/store.d.ts", "./types/utils.d.ts", "./types/vue-router.d.ts", "./internal/constant.ts", "./internal/getConfigFileName.ts", "./internal/utils.ts", "./internal/script/buildConf.ts", "./internal/script/postBuild.ts", "./internal/vite/proxy.ts", "./internal/vite/plugin/compress.ts", "./internal/vite/plugin/html.ts", "./internal/vite/plugin/imagemin.ts", "./internal/vite/plugin/index.ts", "./internal/vite/plugin/pwa.ts", "./internal/vite/utils/modifyVars.ts", "./vite.config.ts"], "version": "5.8.3"}