import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const { t } = useI18n();

export const editor: AppRouteModule[] = [
  {
    path: '/editor',
    name: 'editor',
    component: LAYOUT,
    redirect: '/editor/projects',
    meta: {
      title: t('编辑器'),
      icon: 'login',
      orderNo: 3,
      hideChildrenInMenu: true,
      hideFooter: true,
    },
    children: [
      {
        path: '/editor/projects',
        name: 'editorProjects',
        component: () => import('@/views/editor/projects/ProjectsPage.vue'),
        meta: {
          hidden: true,
          title: '编辑项目列表',
          noTagsView: true,
          hideMenu: true,
        },
      },
    ],
  },
  {
    path: '/editor/projects/:id',
    name: 'editorProject',
    component: () =>
      import('@/views/editor/projects/details/ProjectDetailPage.vue'),
    meta: {
      hidden: true,
      title: '编辑项目',
      noTagsView: true,
      hideMenu: true,
    },
  },
];

export default editor;
