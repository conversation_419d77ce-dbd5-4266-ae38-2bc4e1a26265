<script lang="ts" setup>
import { Icon } from '@/components/Icon';
import { ButtonProps } from 'element-plus';

defineOptions({ name: 'XTextButton' });

const props = withDefaults(
  defineProps<{
    preIcon?: string;
    postIcon?: string;
    title?: string;
    onClick: (...args: any[]) => any;
    type?: '' | 'primary' | 'success' | 'warning' | 'danger' | 'info';
    circle?: boolean;
    round?: boolean;
    plain?: boolean;
  }>(),
  {
    preIcon: '',
    postIcon: '',
    title: '',
    type: 'primary',
    circle: false,
    round: false,
    plain: false,
    onClick: () => {},
  },
);

const getBindValue = computed<Partial<ButtonProps>>(
  (): Partial<ButtonProps> => {
    const delArr: string[] = ['title', 'preIcon', 'postIcon', 'onClick'];
    const attrs = useAttrs();
    const obj = { ...attrs, ...props };
    for (const key in obj) {
      if (delArr.indexOf(key) !== -1) {
        delete obj[key];
      }
    }
    return obj;
  },
);
</script>

<template>
  <el-button link v-bind="getBindValue" @click="onClick">
    <Icon v-if="preIcon" :icon="preIcon" class="mr-1px" />
    {{ title ? title : '' }}
    <Icon v-if="postIcon" :icon="postIcon" class="mr-1px" />
  </el-button>
</template>
<style lang="scss" scoped>
:deep(.el-button.is-text) {
  padding: 8px 4px;
  margin-left: 0;
}

:deep(.el-button.is-link) {
  padding: 8px 4px;
  margin-left: 0;
}
</style>
