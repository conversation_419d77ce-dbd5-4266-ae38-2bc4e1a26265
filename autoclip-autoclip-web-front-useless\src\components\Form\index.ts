import Form from './src/Form.vue';
import { ElForm } from 'element-plus';
import { FormSchema, FormSetPropsType } from '@/types/form';
import { ComponentRef } from '#/index';

export interface FormExpose {
  setValues: (data: Recordable) => void;
  setProps: (props: Recordable) => void;
  delSchema: (field: string) => void;
  addSchema: (formSchema: FormSchema, index?: number) => void;
  setSchema: (schemaProps: FormSetPropsType[]) => void;
  formModel: Recordable;
  // @ts-ignore
  getElFormRef: () => ComponentRef<typeof ElForm>;
}

export { Form };
