<template>
  <div
    class="mt-2 h-4 w-full overflow-hidden rounded-lg border border-gray-300 bg-gray-200"
  >
    <div
      class="h-full bg-blue-500 transition-all duration-200"
      :style="{ width: `${(progress * 100).toFixed(1)}%` }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { FFmpeg } from '@ffmpeg/ffmpeg';

const props = defineProps<{
  ffmpeg: FFmpeg;
}>();

const progress = ref(0);

const handleProgress = ({ progress: newProgress }: { progress: number }) => {
  const clampedProgress = Math.max(0, Math.min(newProgress, 1));
  progress.value = clampedProgress;
};

onMounted(() => {
  props.ffmpeg.on('progress', handleProgress);
});

onUnmounted(() => {
  props.ffmpeg.off('progress', handleProgress);
});
</script>
