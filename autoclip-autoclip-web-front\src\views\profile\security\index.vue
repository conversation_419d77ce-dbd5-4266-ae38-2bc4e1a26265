<script setup lang="ts">
import UserSocial from '@/views/profile/security/UserSocial.vue';
import ResetPwd from '@/views/profile/security/ResetPwd.vue';
</script>

<template>
  <div class="flex w-full flex-col gap-1">
    <el-card :header="'密码设置'" shadow="never">
      <ResetPwd />
    </el-card>
    <el-card :header="'社交设置'" shadow="never">
      <UserSocial />
    </el-card>
  </div>
</template>

<style scoped lang="scss">
@media (width < 40rem) {
  :deep(.el-form) {
    width: 100%;
    margin: auto;
  }
}
@media (width >= 40rem) {
  :deep(.el-form) {
    width: 75%;
    margin: auto;
  }
}
</style>
