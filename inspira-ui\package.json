{"name": "inspira-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@vueuse/core": "^13.5.0", "motion-v": "^1.5.0", "simplex-noise": "^4.0.3", "tailwindcss": "^4.1.11", "vue": "^3.5.17"}, "devDependencies": {"@iconify/vue": "^5.0.0", "@tsconfig/node22": "^22.0.2", "@types/node": "^24.0.14", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.6.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.6.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "vite": "npm:rolldown-vite@^7.0.9", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^3.0.1"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"vite": "7.0.9"}}}}