import { defHttp } from '@/utils/http/axios';

// 文件预签名地址 Response VO
export interface FilePresignedUrlRespVO {
  // 文件配置编号
  configId: number;
  // 文件上传 URL
  uploadUrl: string;
  // 文件 URL
  url: string;
  // 文件路径
  path: string;
}

export interface FileCreateReqVO {
  // 文件配置编号
  configId?: number;
  // 文件路径
  path?: string;
  // 原文件名
  name: string;
  // 文件 URL
  url: string;
  // 文件 MIME 类型
  type: string;
  // 文件大小
  size: number;
}

export interface FilePresignedUrlReqVO {
  // 文件名称
  name: string;
  // 文件目录
  directory: string;
}

export const getFilePresignedUrl = (
  params: FilePresignedUrlReqVO,
): Promise<FilePresignedUrlRespVO> => {
  const { name, directory } = params;
  return defHttp.get<FilePresignedUrlRespVO>({
    url: '/autoclip/video/presigned-url',
    params: { name: name, directory: directory },
  });
};

// 创建文件
export const createFile = (data: FileCreateReqVO) => {
  return defHttp.post({ url: '/infra/file/create', data });
};

// 上传文件
export const updateFile = (data: any) => {
  return defHttp.post({ url: '/infra/file/upload', data });
};
