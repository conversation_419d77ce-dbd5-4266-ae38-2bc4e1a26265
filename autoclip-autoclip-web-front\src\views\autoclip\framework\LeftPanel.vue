<template>
  <div
    class="md:w-sm scrollbar-width:none relative mx-4 my-4 flex flex-col overflow-y-auto rounded-3xl bg-white max-md:h-[calc(80vh-6rem)] md:shadow-lg"
    style="scrollbar-width: none"
  >
    <div
      class="z-1 sticky top-0 flex w-full flex-row items-center gap-2 bg-white px-4 py-4"
    >
      <div class="i-mdi:settings text-xl text-gray-600"></div>
      <h1>{{ title }}</h1>
    </div>
    <div class="px-4">
      <el-upload
        class="mb-2 w-full"
        ref="uploadRef"
        id="input"
        :auto-upload="false"
        :multiple="false"
        v-model:file-list="inputFileList"
        :on-change="handleChange"
        :before-upload="beforeUpload"
        :http-request="postVideo"
        :limit="1"
        :on-success="onUploadSuccess"
        :on-exceed="handleExceed"
        :accept="accept"
        :disabled="!!inputPreviewVideoUrl"
        drag
      >
        <transition :name="getBasicTransition" mode="out-in" appear>
          <div v-if="inputPreviewVideoUrl" class="group relative p-2">
            <div class="absolute right-4 top-4 z-10">
              <button
                class="rounded-full p-2 text-white brightness-200 transition-colors hover:bg-red-600"
                @click.stop="clearFiles"
              >
                <div class="i-ep-delete text-lg"></div>
              </button>
            </div>
            <div class="relative cursor-pointer" @click="previewVideo">
              <PauseAfterPlayVideo
                :url="inputPreviewVideoUrl"
                class="w-full rounded-lg border border-gray-200"
                muted
                loop
                preload="metadata"
                :controls="false"
                :playsinline="true"
              />
              <div
                class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30"
              >
                <div class="flex items-center gap-2 text-white brightness-200">
                  <div class="i-ep-video-play text-2xl"></div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="py-40px px-14px flex flex-col items-center justify-center"
          >
            <div class="mb-2 text-3xl text-gray-300">+</div>
            <div class="mb-1 text-sm text-gray-500">点击或拖拽上传视频</div>
            <div class="text-xs text-gray-400">
              支持 MP4、WebM 格式，最大 2GB
            </div>
          </div>
        </transition>
      </el-upload>
      <div class="mb-2 flex w-full flex-col items-start justify-between">
        <div
          class="mb-2 flex w-full flex-col items-start justify-between rounded-xl border-2 border-gray-200 p-6 pb-2"
        >
          <div class="mb-2 text-lg font-bold">选项</div>
          <transition
            v-for="optionItem in optionItems"
            :key="optionItem.key + optionItem.label"
            :name="getBasicTransition"
            mode="out-in"
            appear
          >
            <div
              v-if="!optionItem.visibleOn || optionItem.visibleOn(optionItems)"
              class="flex w-full flex-col items-start justify-between border-gray-200 pb-2"
            >
              <div
                v-if="optionItem.type === 'select'"
                class="mb-4 w-full border-t-2 border-gray-200 pt-4"
              >
                <div class="flex items-center justify-between gap-2">
                  <span class="whitespace-nowrap text-sm text-gray-800">{{
                    optionItem.label
                  }}</span>
                  <el-select v-model="optionItem.value" class="w-48">
                    <el-option
                      v-for="option in optionItem.selectOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                      :disabled="option.disabled"
                    />
                  </el-select>
                </div>
              </div>

              <div
                v-if="optionItem.type === 'switch'"
                class="mb-2 flex w-full items-center gap-1 border-t border-gray-200 pt-4"
              >
                <label class="flex w-full cursor-pointer items-center gap-1">
                  <div class="flex items-center gap-1 mr-2">
                    <span class="text-sm text-gray-800">{{
                      optionItem.label
                    }}</span>
                    <el-tooltip
                      v-if="optionItem.tooltip"
                      :content="optionItem.tooltip"
                      placement="top"
                      class="z-10 mr-2"
                      effect="light"
                    >
                      <div class="i-ep-question-filled text-gray-300"></div>
                    </el-tooltip>
                  </div>
                  <input
                    type="checkbox"
                    v-model="optionItem.value"
                    class="toggle-checkbox"
                  />
                </label>
              </div>
              <div
                v-else-if="optionItem.type === 'slider'"
                class="mb-4 w-full border-t-2 border-gray-200 pt-4"
              >
                <div class="flex items-center gap-2">
                  <span class="text-sm text-gray-800">{{
                    optionItem.label
                  }}</span>
                  <span class="cursor-pointer" v-if="optionItem.tooltip">
                    <el-tooltip
                      :content="optionItem.tooltip"
                      placement="top"
                      class="z-10"
                      effect="light"
                    >
                      <div class="i-ep-question-filled text-gray-300"></div>
                    </el-tooltip>
                  </span>
                </div>
                <div class="flex items-center gap-2">
                  <el-slider
                    v-model="optionItem.value"
                    :show-tooltip="false"
                    size="small"
                    :min="optionItem.sliderOptions?.min"
                    :max="optionItem.sliderOptions?.max"
                    :step="optionItem.sliderOptions?.step"
                  />
                  <span class="w-10 text-right">{{
                    optionItem.value.toFixed(1)
                  }}</span>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
      <div
        class="z-1 sticky bottom-0 flex w-full flex-row gap-2 bg-gradient-to-t from-white from-80% via-white/95 to-transparent to-100% pb-4 pt-10"
      >
        <button
          class="w-full rounded-xl bg-indigo-600 py-3 text-lg font-bold text-white shadow transition hover:bg-indigo-700"
          @click="submitUpload"
          :disabled="isProcessing"
        >
          {{ isProcessing ? '处理中...' : '裁剪视频' }}
        </button>
      </div>
    </div>

    <el-dialog
      v-if="previewDialogVisible"
      v-model="previewDialogVisible"
      title="视频预览"
      :width="getIsMobile ? '95%' : '80%'"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      @closed="handleDialogClosed"
    >
      <div class="flex justify-center">
        <video
          v-if="inputPreviewVideoUrl"
          ref="previewVideoRef"
          :src="inputPreviewVideoUrl"
          controls
          class="max-h-[70vh] w-full"
        ></video>
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { VideoInfoRespVO } from '@/api/autoclip/auto_clip';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';
import { useMessage } from '@/hooks/web/useMessage';
import { VideoInfo } from '@/utils/videoUtils';
import { useWindowSize } from '@vueuse/core';
import {
  ElDialog,
  ElUpload,
  genFileId,
  UploadFile,
  UploadFiles,
  UploadProps,
  UploadRawFile,
  UploadRequestOptions,
} from 'element-plus';
import { debounce } from 'lodash-es';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { OptionItem } from './types';

const route = useRoute();
const props = defineProps<{
  title: string;
  optionItems: OptionItem<Record<string, any>>[];
  onPostVideo: (
    fileInfo: UploadRequestOptions,
    inputFile: UploadFile,
  ) => Promise<VideoInfoRespVO>;
  onProcessVideo: (videoInfo: VideoInfo) => Promise<VideoInfoRespVO>;
}>();

const { getBasicTransition } = useTransitionSetting();

const { $message } = useMessage();

const { width } = useWindowSize();
const getIsMobile = computed(() => width.value < 768);

const accept = 'video/mp4,video/webm';
const isProcessing = ref(false);
const inputFileList = ref<UploadFiles>([]);
const inputPreviewVideoUrl = ref('');

const jumpToOutput = () => {
  const uploadElement = document.getElementById('output');
  if (uploadElement) {
    uploadElement.scrollIntoView({ behavior: 'smooth' });
  }
};

const postVideo = async (options: UploadRequestOptions) => {
  isProcessing.value = true;
  return props
    .onPostVideo(options, inputFileList.value[0] as UploadFile)
    .catch(() => {
      isProcessing.value = false;
    });
};

const uploadRef = ref<InstanceType<typeof ElUpload>>();

const beforeUpload = (file) => {
  const isAllowedType = accept
    .split(',')
    .some((type) => file.type === type.trim());
  if (!isAllowedType) {
    $message.error('请上传 MP4 或 WebM 格式的视频');
    return false;
  }
  const isLtMaxSize = file.size / 1024 / 1024 < 2000;
  if (!isLtMaxSize) {
    $message.error('视频大小不能超过 2000MB');
    return false;
  }
  isProcessing.value = true;
  return true;
};

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

const handleChange = (uploadFile, uploadFiles) => {
  if (
    uploadFiles.length < 1 ||
    !uploadFile.raw ||
    uploadFile.status !== 'ready'
  )
    return;
  isProcessing.value = false;
  videoInfoRef.value = undefined;

  inputPreviewVideoUrl.value = URL.createObjectURL(uploadFile.raw);
};

onMounted(() => {
  // Handle file from route query
  const videoInfoStr = route.query.videoInfo as string;
  if (videoInfoStr == undefined || videoInfoStr == '') {
    return;
  }
  const videoInfo = JSON.parse(videoInfoStr) as VideoInfo;
  const type = videoInfo.urlType;

  if (type === 'local') {
    fetch(new URL(videoInfo.url))
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], videoInfo.name, {
          type: videoInfo.type,
        }) as UploadRawFile;
        file.uid = genFileId();
        uploadRef.value?.clearFiles();
        uploadRef.value?.handleStart(file);
      });
  } else {
    const videoInfoStr = route.query.videoInfo as string;
    if (videoInfoStr) {
      try {
        handleStart(videoInfo);
      } catch (error) {
        $message.error('Error parsing video info:', error);
      }
    }
  }
});

const checkIsProcessing = () => {
  if (isProcessing.value) {
    $message.info('该视频正在处理中');
    throw Error('该视频正在处理中');
  }
};

const videoInfoRef = ref<VideoInfo>();

const handleStartForVideoInfo = async (videoInfo: VideoInfo) => {
  isProcessing.value = false;
  inputPreviewVideoUrl.value = videoInfo.url;
  videoInfoRef.value = videoInfo;
};

const onUploadSuccess = () => {};

const handleStart = (rawFile: UploadRawFile | VideoInfo) => {
  if ('url' in rawFile) {
    handleStartForVideoInfo(rawFile);
    return;
  } else {
    uploadRef.value?.handleStart?.(rawFile);
  }
};

const processVideoInfo = () => {
  checkIsProcessing();
  const videoInfo = videoInfoRef.value;
  if (!videoInfo) {
    return;
  }
  isProcessing.value = true;
  props.onProcessVideo(videoInfo).catch(() => {
    isProcessing.value = false;
  });
};

const submitUpload = debounce(() => {
  checkIsProcessing();
  if (videoInfoRef.value) {
    processVideoInfo();
  } else {
    uploadRef.value?.submit();
  }
  jumpToOutput();
}, 300);

const previewDialogVisible = ref(false);
const previewVideoRef = ref<HTMLVideoElement>();

const handleDialogClosed = () => {
  previewDialogVisible.value = false;
};

const previewVideo = () => {
  if (!inputPreviewVideoUrl.value) {
    $message.warning('请先上传视频');
    return;
  }
  previewDialogVisible.value = true;
};

const clearFiles = () => {
  uploadRef.value?.clearFiles?.();
  videoInfoRef.value = undefined;
  inputPreviewVideoUrl.value = '';
  inputFileList.value = [];
  isProcessing.value = false;
};

defineExpose({
  clearFiles,
  handleStart,
  inputPreviewVideoUrl,
  isProcessing,
});
</script>

<style lang="scss" scoped>
:deep(.el-overlay-dialog) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-upload-dragger) {
  border-width: 2px;
}

:deep(.el-dialog) {
  margin: auto;
}
</style>
