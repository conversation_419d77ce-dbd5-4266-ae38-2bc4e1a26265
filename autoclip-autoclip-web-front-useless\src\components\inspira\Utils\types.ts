// Inspira UI 工具组件类型定义

export interface ErrorBoundaryProps {
  // 降级组件名称
  fallbackComponent: string;
  
  // 传递给降级组件的 props
  fallbackProps?: Record<string, any>;
  
  // 是否在控制台显示错误信息
  showError?: boolean;
  
  // 错误重试次数
  maxRetries?: number;
  
  // 重试间隔（毫秒）
  retryDelay?: number;
  
  // 自定义错误处理函数
  onError?: (error: Error, instance: any, info: string) => void;
  
  // 自定义降级渲染函数
  onFallback?: (componentName: string, error: Error) => void;
}

export interface ErrorBoundaryEmits {
  error: [error: Error];
  fallback: [componentName: string];
  retry: [attempt: number];
  reset: [];
}

export interface ErrorBoundarySlots {
  default: () => any;
  fallback: (props: { error: Error; retry: () => void; reset: () => void }) => any;
}

export interface ComponentMonitorStats {
  componentName: string;
  renderCount: number;
  errorCount: number;
  lastError?: Error;
  lastErrorTime?: Date;
  performance: {
    averageRenderTime: number;
    maxRenderTime: number;
    minRenderTime: number;
    totalRenderTime: number;
  };
}

export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
  enabled: boolean;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  iterationCount?: number | 'infinite';
}

export interface ThemeConfig {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  border: string;
  shadow: string;
  radius: string;
}