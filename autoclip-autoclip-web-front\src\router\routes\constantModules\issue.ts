import { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

export const issue: AppRouteModule = {
  path: '/issue',
  name: 'issue',
  component: LAYOUT,
  redirect: '/issue/index',
  meta: {
    title: '问题反馈',
    icon: 'i-mdi:bug-outline',
    orderNo: 4,
    hideChildrenInMenu: true,
    hideFooter: true,
  },
  children: [
    {
      path: '/issue/index',
      name: 'issueIndex',
      component: () => import('@/views/issue/index.vue'),
      meta: {
        hidden: true,
        title: '问题反馈',
        noTagsView: true,
      },
    },
  ],
};
export default issue;