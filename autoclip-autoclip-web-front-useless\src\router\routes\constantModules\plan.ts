import { RouteRecordRaw } from 'vue-router';
import { LAYOUT } from '@/router/constant';

const route: RouteRecordRaw = {
  path: '/plan',
  component: LAYOUT,
  redirect: '/plan/index',
  meta: {
    title: '订阅方案',
    icon: 'i-mdi:credit-card-outline',
    hideChildrenInMenu: true,
    hideMenu: true,
  },
  children: [
    {
      path: '/plan/index',
      name: 'PlanPage',
      component: () => import('@/views/plan/index.vue'),
      meta: {
        title: 'Choose Your Plan',
      },
    },
  ],
};

export default route;
