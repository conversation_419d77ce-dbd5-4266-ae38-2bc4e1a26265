import QRCodeStyling from 'qr-code-styling';

const getQRConfig = (url: string, showLogo: boolean, dotsStyle: string) => ({
  width: 300,
  height: 300,
  type: 'canvas' as const,
  data: url,
  dotsOptions: {
    color: '#000000',
    type: dotsStyle as
      | 'dots'
      | 'rounded'
      | 'classy'
      | 'classy-rounded'
      | 'square'
      | 'extra-rounded',
  },
  cornersSquareOptions: {
    type: 'extra-rounded' as const,
  },
  cornersDotOptions: {
    type: 'dot' as const,
  },
  backgroundOptions: {
    color: '#FFFFFF',
  },
  qrOptions: {
    errorCorrectionLevel: 'H' as const,
  },
  ...(showLogo
    ? {
        image: wechat,
        imageOptions: {
          hideBackgroundDots: true,
          imageSize: 0.23,
          margin: 5,
        },
      }
    : {}),
});
import wechat from '@/assets/svg/wechat.svg';

const generateQRUrl = async (url: string) => {
  const qrUrl = url;

  const config = getQRConfig(qrUrl, true, 'dots');

  const qrCodeInstance = new QRCodeStyling(config);

  const canvas = await qrCodeInstance.getRawData('png');
  if (canvas instanceof Blob) {
    return URL.createObjectURL(canvas);
  }
  return null;
};

export { generateQRUrl };
