# CLAUDE.md - AutoClip Web Frontend

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository, with special focus on the UI migration from Element Plus to Inspira UI.

## Development Commands

- **Development**: `pnpm dev` - Start development server
- **Build**: `pnpm build` - Build for production (runs TypeScript check, build, and post-build scripts)
- **Type Check**: `pnpm lint:tsc` - Run TypeScript type checking without emitting files
- **Format**: `pnpm lint:prettier` - Format code with Prettier
- **Preview**: `pnpm preview` - Preview production build locally

## Architecture Overview

This is a Vue 3 + TypeScript video editing and auto-clipping web application with a hybrid Vue/React architecture, currently undergoing UI migration from Element Plus to Inspira UI.

### Tech Stack
- **Frontend Framework**: Vue 3 with Composition API and `<script setup>`
- **Build Tool**: Vite with custom plugin configuration
- **UI Library**: 
  - **Current**: Element Plus with custom theming
  - **Target**: Inspira UI with modern animations and effects
  - **Strategy**: Gradual migration with hybrid approach
- **Styling**: SCSS + UnoCSS + Tailwind utilities
- **State Management**: 
  - Pinia for Vue components
  - Zustand for React components (used in video editor)
  - Custom state bridge synchronizing between both stores
- **Video Processing**: FFmpeg via @ffmpeg/ffmpeg, Remotion for video composition
- **Routing**: Vue Router with route guards and authentication
- **Internationalization**: Vue I18n with English and Chinese support
- **HTTP Client**: Axios with custom interceptors and retry logic

### Key Directory Structure

#### Core Application (`src/`)
- **`main.ts`**: Application bootstrap with store, router, i18n, and global directive setup
- **`App.vue`**: Root component with Element Plus configuration provider
- **`router/`**: Vue Router configuration with authentication guards
- **`store/`**: 
  - Pinia stores for Vue (`modules/`)
  - Zustand stores for React components (`editor/react/`)
  - State bridge synchronization (`stateBridge.ts`)

#### Views and Features
- **`views/autoclip/`**: Auto-clipping functionality for different sports (badminton, ping-pong)
  - `framework/`: Shared framework components with WebSocket message handling
- **`views/editor/`**: Video editor with React/Remotion integration
  - Timeline editing, media properties, FFmpeg rendering
  - Mixed Vue/React architecture for video composition
- **`views/home/<USER>
- **`views/profile/`**: User management and authentication

#### UI Components (`components/`)
- **`Application/`**: App-level providers and configuration
- **`VideoPlayer/`**: Custom video player components
- **`Cropper/`**: Image/video cropping functionality
- **`Form/`**: Form components with validation
- **`Loading/`**: Loading states and indicators
- **`Login/`**: Authentication forms (primary target for UI migration)
- **`inspira/`**: **NEW** - Inspira UI component wrappers and customizations
- **`hybrid/`**: **NEW** - Mixed Element Plus + Inspira UI components

#### Configuration and Utils
- **`settings/`**: App configuration (design, locale, project settings)
- **`utils/`**: 
  - HTTP client with axios configuration
  - Authentication utilities
  - File handling and video processing utilities
  - IndexedDB integration for project storage
- **`locales/`**: i18n configuration and language files

### State Management Architecture

The application uses a unique dual-store pattern:
- **Pinia stores** handle Vue component state
- **Zustand stores** manage React component state (primarily for video editor)
- **State bridge** (`stateBridge.ts`) synchronizes data between both systems

### Build Configuration

- **Vite config** (`vite.config.ts`): Custom plugin setup, proxy configuration, SCSS/Less preprocessing
- **Path aliases**: `@/` maps to `src/`, `/#/` maps to `types/`
- **Environment variables**: Loaded via Vite with custom wrapper utilities
- **Build optimization**: Terser minification, chunk splitting, compression plugins

### Authentication & API

- **JWT-based authentication** with encrypted storage
- **API structure** organized by domain (`api/autoclip/`, `api/member/`, etc.)
- **Axios interceptors** for request/response handling and retry logic
- **WebSocket integration** for real-time video processing updates

### Video Processing Pipeline

1. **File Upload**: Drag-drop or file selection with preview
2. **Processing**: WebSocket communication for progress updates
3. **Editing**: Remotion-based video composition with React components
4. **Export**: FFmpeg-based rendering with progress tracking
5. **Storage**: IndexedDB for project persistence

## UI Migration Guidelines

### Core Principles
1. **Functionality First**: Never break existing functionality during UI migration
2. **Gradual Migration**: Replace components incrementally, not all at once
3. **Fallback Strategy**: Always provide Element Plus fallback for failed Inspira UI components
4. **Performance Awareness**: Monitor performance impact of animations and effects
5. **Accessibility**: Maintain or improve accessibility standards

### Component Migration Priority
1. **High Priority**: Buttons, Inputs, Forms (core interaction elements)
2. **Medium Priority**: Dialogs, Radio/Checkbox, Navigation elements
3. **Low Priority**: Decorative elements, Dividers, Links

### Inspira UI Integration Strategy

#### Available Inspira UI Components
- **`RainbowButton`**: Animated gradient button for primary actions
- **`AuroraBackground`**: Animated background for login/landing pages
- **`FallingStarsBg`**: Particle background for main application
- **`Vortex`**: Loading/transition background effect
- **`FlipWords`**: Text animation for titles and headings
- **`TextHighlight`**: Highlighted text with animation
- **`AnimatedTooltip`**: Enhanced tooltip with smooth animations
- **`BlurReveal`**: Content reveal animation
- **`BorderBeam`**: Animated border effects

#### Component Mapping Strategy
```typescript
// Element Plus -> Inspira UI Migration Map
const componentMigration = {
  'el-button': 'RainbowButton | CustomButton',
  'el-input': 'CustomInput + animations',
  'el-dialog': 'CustomModal + animations',
  'el-form': 'Hybrid (keep validation + Inspira styling)',
  'el-radio-group': 'CustomRadio + animations',
  'el-checkbox': 'CustomCheckbox + animations',
  'el-divider': 'CustomDivider + effects',
  'el-link': 'CustomLink + hover effects'
};
```

#### Error Handling Pattern
```typescript
// Always provide fallback to Element Plus
const withFallback = (InspiraComponent, ElementComponent) => {
  return defineComponent({
    setup(props, { slots }) {
      const hasError = ref(false);
      
      onErrorCaptured((error) => {
        console.warn('Inspira component failed, falling back:', error);
        hasError.value = true;
        return false;
      });
      
      return () => hasError.value 
        ? h(ElementComponent, props, slots)
        : h(InspiraComponent, props, slots);
    }
  });
};
```

### Development Workflow

#### Before Making Changes
1. **Read existing code** to understand current functionality
2. **Test current behavior** to establish baseline
3. **Identify dependencies** that might be affected
4. **Plan rollback strategy** in case of issues

#### During Migration
1. **Create wrapper components** instead of direct replacement
2. **Maintain API compatibility** with existing props and events
3. **Add error boundaries** around new components
4. **Test thoroughly** in different scenarios
5. **Monitor performance** impact

#### After Migration
1. **Regression testing** of all affected functionality
2. **Performance comparison** with previous implementation
3. **User experience validation** 
4. **Documentation updates**

### Testing Requirements

#### Functional Testing
- All existing user flows must continue to work
- Form validation and submission must be preserved
- Authentication flows must remain intact
- Video processing functionality must be unaffected

#### Visual Testing
- Components should look better than before
- Animations should enhance, not distract from, user experience
- Responsive design must be maintained
- Accessibility standards must be met or improved

#### Performance Testing
- Page load times should not increase significantly
- Animation performance should be smooth (60fps)
- Memory usage should not increase substantially
- Bundle size impact should be monitored

## Important Notes

- The project uses Vue 3 `<script setup>` syntax extensively
- Mixed Vue/React architecture requires careful state synchronization
- Video processing is CPU-intensive and handled via Web Workers where possible
- IndexedDB is used for client-side project and file storage
- WebSocket connections handle real-time processing updates
- **UI Migration**: Always prioritize functionality over aesthetics
- **Error Handling**: Every Inspira UI component must have Element Plus fallback
- **Testing**: Comprehensive testing required before considering migration complete