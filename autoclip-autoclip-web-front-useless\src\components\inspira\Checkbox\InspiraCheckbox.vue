<!-- 
  Inspira UI 复选框组件
  兼容 Element Plus Checkbox API，提供动画增强效果
-->
<template>
  <ErrorBoundary
    fallback-component="el-checkbox"
    :fallback-props="elementPlusProps"
    @error="handleError"
  >
    <label :class="computedClass" @click="handleClick">
      <!-- 隐藏的原生 checkbox -->
      <input
        ref="checkboxRef"
        type="checkbox"
        :name="name"
        :value="value"
        :checked="isChecked"
        :disabled="disabled"
        :indeterminate="indeterminate"
        class="inspira-checkbox__input"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 自定义复选框外观 -->
      <span :class="checkboxClass">
        <!-- 选中状态的图标 -->
        <Icon
          v-if="isChecked && !indeterminate"
          icon="i-mdi:check"
          :class="iconClass"
        />
        <!-- 半选状态的图标 -->
        <Icon
          v-else-if="indeterminate"
          icon="i-mdi:minus"
          :class="iconClass"
        />
      </span>
      
      <!-- 标签文本 -->
      <span v-if="$slots.default || label" :class="labelClass">
        <slot>{{ label }}</slot>
      </span>
    </label>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed, ref, inject } from 'vue';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/Icon';
import ErrorBoundary from '../Utils/ErrorBoundary.vue';
import type { InspiraCheckboxProps, InspiraCheckboxEmits } from './types';

const props = withDefaults(defineProps<InspiraCheckboxProps>(), {
  size: 'default',
  disabled: false,
  indeterminate: false,
  animation: 'scale',
});

const emit = defineEmits<InspiraCheckboxEmits>();

// 复选框引用
const checkboxRef = ref<HTMLInputElement>();

// 从 CheckboxGroup 注入的值
const checkboxGroup = inject('checkboxGroup', null) as any;

// 计算是否选中
const isChecked = computed(() => {
  if (checkboxGroup) {
    return checkboxGroup.modelValue.includes(props.value);
  }
  return Array.isArray(props.modelValue) 
    ? props.modelValue.includes(props.value)
    : props.modelValue;
});

// 计算是否禁用
const isDisabled = computed(() => {
  return props.disabled || (checkboxGroup && checkboxGroup.disabled);
});

// 计算尺寸
const computedSize = computed(() => {
  return checkboxGroup?.size || props.size;
});

// Element Plus 降级 props
const elementPlusProps = computed(() => ({
  modelValue: props.modelValue,
  value: props.value,
  label: props.label,
  disabled: props.disabled,
  indeterminate: props.indeterminate,
  size: props.size,
  name: props.name,
  class: props.class,
}));

// 计算样式类
const computedClass = computed(() => {
  return cn(
    'inspira-checkbox',
    `inspira-checkbox--${computedSize.value}`,
    {
      'inspira-checkbox--checked': isChecked.value,
      'inspira-checkbox--disabled': isDisabled.value,
      'inspira-checkbox--indeterminate': props.indeterminate,
      [`inspira-checkbox--${props.animation}`]: props.animation !== 'none',
    },
    props.class
  );
});

const checkboxClass = computed(() => {
  return cn('inspira-checkbox__box', {
    'inspira-checkbox__box--checked': isChecked.value,
    'inspira-checkbox__box--disabled': isDisabled.value,
    'inspira-checkbox__box--indeterminate': props.indeterminate,
  });
});

const iconClass = computed(() => {
  return cn('inspira-checkbox__icon', {
    'inspira-checkbox__icon--checked': isChecked.value,
    'inspira-checkbox__icon--indeterminate': props.indeterminate,
  });
});

const labelClass = computed(() => {
  return cn('inspira-checkbox__label', {
    'inspira-checkbox__label--checked': isChecked.value,
    'inspira-checkbox__label--disabled': isDisabled.value,
  });
});

// 事件处理
const handleClick = () => {
  if (isDisabled.value) return;
  
  if (checkboxGroup) {
    checkboxGroup.changeEvent(props.value);
  } else {
    const newValue = Array.isArray(props.modelValue)
      ? isChecked.value
        ? props.modelValue.filter(item => item !== props.value)
        : [...props.modelValue, props.value]
      : !props.modelValue;
    
    emit('update:modelValue', newValue);
    emit('change', newValue);
  }
};

const handleChange = (event: Event) => {
  // 原生 change 事件已经在 handleClick 中处理
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

const handleError = (error: Error) => {
  console.warn('InspiraCheckbox error, falling back to el-checkbox:', error);
};

// 暴露方法
const focus = () => {
  checkboxRef.value?.focus();
};

const blur = () => {
  checkboxRef.value?.blur();
};

defineExpose({
  focus,
  blur,
  checkboxRef,
});
</script>

<style scoped>
.inspira-checkbox {
  @apply inspira-ui;
  
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-right: 1rem;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &:last-child {
    margin-right: 0;
  }
  
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  &:hover:not(.inspira-checkbox--disabled) {
    .inspira-checkbox__box {
      border-color: var(--inspira-primary);
    }
  }
}

.inspira-checkbox__input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  
  &:focus + .inspira-checkbox__box {
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
  }
}

.inspira-checkbox__box {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: 2px solid var(--inspira-border);
  border-radius: 4px;
  background-color: var(--inspira-background);
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &--checked {
    border-color: var(--inspira-primary);
    background-color: var(--inspira-primary);
  }
  
  &--indeterminate {
    border-color: var(--inspira-primary);
    background-color: var(--inspira-primary);
  }
  
  &--disabled {
    border-color: var(--inspira-border);
    background-color: var(--inspira-surface);
  }
}

.inspira-checkbox__icon {
  color: white;
  font-size: 12px;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  transform: scale(0);
  
  &--checked,
  &--indeterminate {
    transform: scale(1);
  }
}

.inspira-checkbox__label {
  margin-left: 8px;
  color: var(--inspira-text);
  font-size: 14px;
  line-height: 1.5;
  transition: all var(--inspira-animation-duration) var(--inspira-animation-easing);
  
  &--checked {
    color: var(--inspira-primary);
    font-weight: 500;
  }
  
  &--disabled {
    color: var(--inspira-text-secondary);
  }
}

/* 尺寸变体 */
.inspira-checkbox--small {
  .inspira-checkbox__box {
    width: 14px;
    height: 14px;
  }
  
  .inspira-checkbox__icon {
    font-size: 10px;
  }
  
  .inspira-checkbox__label {
    font-size: 12px;
    margin-left: 6px;
  }
}

.inspira-checkbox--large {
  .inspira-checkbox__box {
    width: 18px;
    height: 18px;
  }
  
  .inspira-checkbox__icon {
    font-size: 14px;
  }
  
  .inspira-checkbox__label {
    font-size: 16px;
    margin-left: 10px;
  }
}

/* 动画效果 */
.inspira-checkbox--scale {
  &:active:not(.inspira-checkbox--disabled) {
    .inspira-checkbox__box {
      transform: scale(0.95);
    }
  }
}

.inspira-checkbox--bounce {
  .inspira-checkbox__box--checked {
    animation: inspira-checkbox-bounce 0.3s ease-out;
  }
}

.inspira-checkbox--pulse {
  .inspira-checkbox__box--checked {
    animation: inspira-checkbox-pulse 0.5s ease-out;
  }
}

.inspira-checkbox--flip {
  .inspira-checkbox__icon {
    &--checked {
      animation: inspira-checkbox-flip 0.4s ease-out;
    }
  }
}

.inspira-checkbox--slide {
  .inspira-checkbox__icon {
    transform: translateY(-10px) scale(0);
    
    &--checked,
    &--indeterminate {
      transform: translateY(0) scale(1);
      transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
  }
}

/* 动画定义 */
@keyframes inspira-checkbox-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes inspira-checkbox-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

@keyframes inspira-checkbox-flip {
  0% {
    transform: scale(0) rotateY(0deg);
  }
  50% {
    transform: scale(1.2) rotateY(180deg);
  }
  100% {
    transform: scale(1) rotateY(360deg);
  }
}
</style>