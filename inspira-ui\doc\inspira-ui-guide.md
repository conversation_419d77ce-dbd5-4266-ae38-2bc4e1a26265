# Inspira UI 组件库指南

## 📋 目录

- [组件库简介](#组件库简介)
- [快速开始](#快速开始)
- [组件分类索引](#组件分类索引)(已迁往index.md))
- [常用组件API](#常用组件api)



## 🚀 组件库简介

**Inspira UI** 是一个功能强大的 Vue.js 和 Nuxt.js 组件库，旨在帮助开发者构建美观、现代化的网站界面。该组件库提供了丰富的UI组件、动画效果和交互元素，让您能够轻松创建出色的用户体验。

### ✨ 主要特性

- 🎨 **现代化设计** - 基于现代设计原则，提供美观的UI组件
- 🔧 **Vue 3 + TypeScript** - 完全支持 Vue 3 Composition API 和 TypeScript
- 🎭 **丰富的动画** - 内置多种文本动画、特效和交互动画
- 📱 **响应式设计** - 所有组件都支持响应式布局
- ⚡ **高性能** - 优化的组件实现，确保良好的性能表现
- 🎯 **易于使用** - 简洁的API设计，上手简单
- 🔩 **高度可定制** - 支持主题定制和样式覆盖

### 🛠️ 技术栈

- **Vue 3** - 现代化的渐进式JavaScript框架
- **TypeScript** - 提供类型安全的开发体验
- **Tailwind CSS** - 实用优先的CSS框架
- **VueUse** - Vue组合式函数工具库
- **Motion-v** - 动画库
- **Three.js** - 3D图形库（用于3D组件）

## 🚦 快速开始

### 基本工作流程(重点)

决定要引用的组件类别->在index.md找到组件类别并根据组件介绍确定要使用的组件->通过craw4ai mcp(若可用)或内置的搜索功能获取Vue代码实现、API文档、安装说明->将获取到的信息中的[Install Manually]部分的代码在components文件夹中添加vue文件,完整填入其中->在app.vue中引用,通过之前获取的api了解可修改的变量,通过获取到的完整示例代码了解如何在app.vue中使用

### 组件结构规范

所有Inspira UI组件都遵循以下结构：

```vue
<template>
  <!-- 模板内容 -->
</template>

<script lang="ts" setup>
// 使用 TypeScript + Composition API
interface Props {
  // 属性定义
}

const props = withDefaults(defineProps<Props>(), {
  // 默认值
});
</script>

<style scoped>
/* 样式（如需要） */
</style>
```

### 基本使用示例

```vue
<template>
  <div>
    <!-- 使用按钮组件 -->
    <HyperText 
      text="Hello Inspira UI!" 
      :duration="0.8"
      :animate-on-load="true"
    />
    
    <!-- 使用卡片组件 -->
    <CardSpotlight>
      <div class="p-6">
        <h3>特色内容</h3>
        <p>这是一个带聚光灯效果的卡片组件</p>
      </div>
    </CardSpotlight>
  </div>
</template>
```

## 📖 常用组件API

### HyperText 组件

超级文本动画组件，提供字符替换动画效果。

```vue
<HyperText 
  text="Hello World!" 
  :duration="0.8" 
  :animate-on-load="true"
  class="text-2xl font-bold"
/>
```

**Props:**
- `text` (string, 必需) - 要动画的文本
- `duration` (number, 默认: 0.8) - 动画持续时间（秒）
- `animateOnLoad` (boolean, 默认: true) - 是否在加载时播放动画
- `class` (string, 默认: "") - 额外的CSS类

### CardSpotlight 组件

带聚光灯跟随效果的卡片组件。

```vue
<CardSpotlight 
  :gradient-size="200"
  gradient-color="#262626"
  :gradient-opacity="0.8"
>
  <div class="p-6">
    <!-- 卡片内容 -->
  </div>
</CardSpotlight>
```

**Props:**
- `gradientSize` (number, 默认: 200) - 聚光灯半径（像素）
- `gradientColor` (string, 默认: "#262626") - 聚光灯颜色
- `gradientOpacity` (number, 默认: 0.8) - 聚光灯透明度
- `slotClass` (string) - 内容容器的CSS类

### FileUpload 组件

文件上传组件，支持拖拽上传。

```vue
<FileUpload @on-change="handleFileChange">
  <FileUploadGrid />
  <div class="upload-content">
    <!-- 上传界面内容 -->
  </div>
</FileUpload>
```

**Props:**
- `class` (string) - 容器的额外CSS类

**Events:**
- `onChange` - 文件选择/上传时触发，参数: `(files: File[]) => void`

### PatternBackground 组件

可动画的图案背景组件。

```vue
<PatternBackground 
  :animate="true"
  direction="top"
  variant="grid"
  size="md"
  mask="ellipse"
  :speed="10000"
/>
```

**Props:**
- `animate` (boolean, 默认: false) - 是否启用动画
- `direction` (string, 默认: "top") - 动画方向
- `variant` (string, 默认: "grid") - 图案类型 (grid | dot)
- `size` (string, 默认: "md") - 图案大小 (xs | sm | md | lg)
- `mask` (string, 默认: "ellipse") - 遮罩类型
- `speed` (number, 默认: 10000) - 动画速度（毫秒）

### Confetti 组件

彩带庆祝效果组件。

```vue
<Confetti 
  :options="confettiOptions" 
  :global-options="globalOptions"
  :manual-start="false" 
/>
```

**Props:**
- `options` (ConfettiOptions, 默认: {}) - 彩带配置选项
- `globalOptions` (ConfettiGlobalOptions, 默认: {}) - 全局配置
- `manualstart` (boolean, 默认: false) - 是否手动启动

