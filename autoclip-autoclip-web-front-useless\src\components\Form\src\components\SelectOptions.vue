<template>
  <template
    v-for="option in item?.componentProps?.options"
    :key="getOptionKey(option)"
  >
    <el-option-group
      v-if="option?.options?.length"
      :label="getOptionLabel(option)"
    >
      <el-option
        v-for="subOption in option.options"
        :key="getOptionKey(subOption)"
        v-bind="getOptionProps(subOption)"
        :label="getOptionLabel(subOption)"
        :value="getOptionValue(subOption)"
      >
        <slot
          v-if="item?.componentProps?.optionsSlot"
          :name="`${item.field}-option`"
          :item="subOption"
        />
      </el-option>
    </el-option-group>
    <el-option
      v-else
      v-bind="getOptionProps(option)"
      :label="getOptionLabel(option)"
      :value="getOptionValue(option)"
    >
      <slot
        v-if="item?.componentProps?.optionsSlot"
        :name="`${item.field}-option`"
        :item="option"
      />
    </el-option>
  </template>
</template>

<script setup lang="ts">
import { ElOption, ElOptionGroup } from 'element-plus';
import { FormSchema } from '@/types/form';
import { ComponentOptions } from '@/types/components';

interface Props {
  item: FormSchema;
}

const props = defineProps<Props>();

const getOptionLabel = (option: ComponentOptions) => {
  const labelAlias = props.item?.componentProps?.optionsAlias?.labelField;
  return labelAlias ? option[labelAlias] : option.label;
};

const getOptionValue = (option: ComponentOptions) => {
  const valueAlias = props.item?.componentProps?.optionsAlias?.valueField;
  return valueAlias ? option[valueAlias] : option.value;
};

const getOptionProps = (option: ComponentOptions) => {
  const { label, value, ...other } = option;
  return other;
};

const getOptionKey = (option: ComponentOptions) => {
  return getOptionValue(option) || getOptionLabel(option);
};
</script>
