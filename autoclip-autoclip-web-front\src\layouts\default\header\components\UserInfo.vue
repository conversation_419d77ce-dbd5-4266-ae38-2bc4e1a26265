<script lang="ts" setup>
import { ElMessageBox } from 'element-plus';

import avatarImg from '@/assets/imgs/boy.png';
import { PageEnum } from '@/enums/pageEnum';
import { useDesign } from '@/hooks/web/useDesign';
import { useI18n } from '@/hooks/web/useI18n';
import { router } from '@/router';
import { useUserStore } from '@/store/modules/user';

defineOptions({ name: 'UserInfo' });

withDefaults(
  defineProps<{
    showUsername?: boolean;
  }>(),
  {
    showUsername: false,
  },
);
const { t } = useI18n();

const { push } = useRouter();

const userStore = useUserStore();

const { prefixCls } = useDesign('user-info');

const avatar = computed(() => userStore.getUserInfo?.avatar || avatarImg);
const userName = computed(() => userStore.getUserInfo?.nickname ?? 'Admin');

const loginOut = async () => {
  try {
    await ElMessageBox.confirm(
      t('common.loginOutMessage'),
      t('common.reminder'),
      {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      },
    );
    await userStore.logout();
    await router.replace(PageEnum.BASE_HOME);
  } catch {}
};
const toProfile = async () => {
  await push('/user/profile');
};
const toPlan = async () => {
  await push('/plan');
};
</script>

<template>
  <ElDropdown class="custom-hover" :class="prefixCls" trigger="click">
    <div class="flex items-center">
      <ElAvatar
        :src="avatar"
        alt=""
        class="w-[calc(var(--logo-height)-25px)] rounded-[50%]"
      />
      <span
        v-if="showUsername"
        class="text-14px <lg:hidden pl-[5px] text-[var(--top-header-text-color)]"
      >
        {{ userName }}
      </span>
    </div>
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem @click="toProfile">
          <div class="i-ep:tools mr-2"></div>
          <div>{{ t('common.profile') }}</div>
        </ElDropdownItem>
        <ElDropdownItem @click="toPlan">
          <div class="i-mdi:credit-card-outline mr-2"></div>
          <div>订阅方案</div>
        </ElDropdownItem>
        <ElDropdownItem divided @click="loginOut">
          <div class="i-ep:switch-button mr-2"></div>
          <div>{{ t('common.loginOut') }}</div>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>

<style scoped lang="scss">
.fade-bottom-enter-active,
.fade-bottom-leave-active {
  transition:
    opacity 0.25s,
    transform 0.3s;
}

.fade-bottom-enter-from {
  opacity: 0;
  transform: translateY(-10%);
}

.fade-bottom-leave-to {
  opacity: 0;
  transform: translateY(10%);
}
</style>
