<script setup lang="ts">
const emit = defineEmits<{
  (event: 'trigger'): void;
}>();

const props = withDefaults(
  defineProps<{
    active: boolean;
  }>(),
  {
    active: false,
  },
);

const trigger = () => {
  emit('trigger');
};
</script>

<template>
  <div class="flex h-full items-center">
    <button
      class="hamburger-btn"
      :class="{ active: props.active }"
      @click="trigger"
    >
      <span class="line line-1"></span>
      <span class="line line-2"></span>
      <span class="line line-3"></span>
    </button>
  </div>
</template>

<style scoped lang="scss">
$--hamburger-size: 0.5rem;
.hamburger-btn {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: calc($--hamburger-size * 2.5);
  height: calc($--hamburger-size * 2);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;

  .line {
    width: 100%;
    height: calc($--hamburger-size * 0.25);
    background: #333;
    border-radius: calc($--hamburger-size * 0.25);
    transition: all 0.3s ease;
    transform-origin: center;
  }

  &.active {
    .line-1 {
      transform: translateY(calc($--hamburger-size * 0.875)) rotate(45deg);
    }

    .line-2 {
      opacity: 0;
      transform: rotate(45deg);
    }

    .line-3 {
      transform: translateY(calc($--hamburger-size * -0.875)) rotate(-45deg);
    }
  }
}
</style>
