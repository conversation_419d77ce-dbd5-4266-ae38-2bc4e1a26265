<template>
  <div class="w-full">
    <div v-if="getIsMobile" class="z-3 w-full border-b border-solid p-2">
      <MenuTrigger :active="!hidden" @trigger="() => (hidden = !hidden)" />
    </div>
    <UserProfileMenu v-if="getIsMobile && !hidden" />
    <div class="my-10 flex h-full gap-20 sm:mx-2 md:mx-40">
      <UserProfileMenu v-if="!getIsMobile" />
      <RouterView>
        <template #default="{ Component, route }">
          <transition :name="getBasicTransition" mode="out-in" appear>
            <component :is="Component" :key="route.fullPath" />
          </transition>
        </template>
      </RouterView>
    </div>
  </div>
</template>
<script lang="ts" setup>
import MenuTrigger from '@/layouts/default/header/components/MenuTrigger.vue';
import { useAppInject } from '@/hooks/web/useAppInject';
import { unref } from 'vue';
import UserProfileMenu from '@/views/profile/UserProfileMenu.vue';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';

defineOptions({
  name: 'Profile',
});

const { getBasicTransition } = useTransitionSetting();
const { getIsMobile } = useAppInject();
const hidden = ref(unref(getIsMobile));
</script>
<style scoped>
:deep(.el-menu) {
  background-color: white;
  z-index: 8;
}

.user {
  max-height: 960px;
  padding: 15px 20px 20px;
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-card .el-card__header, .el-card .el-card__body) {
  padding: 15px !important;
}

.profile-tabs > .el-tabs__content {
  padding: 32px;
  font-weight: 600;
  color: #6b778c;
}

.el-tabs--left .el-tabs__content {
  height: 100%;
}
</style>
