<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useProjectStore } from '@/store/editor';
import { useMessage } from '@/hooks/web/useMessage';
import { getFile, deleteFile } from '@/store/editor';
import AddMedia from '../AddButtons/AddMedia.vue';
import { storeToRefs } from 'pinia';

interface UploadedFile {
  file: File;
  id: string;
}

const projectStore = useProjectStore();
const { $message } = useMessage();
const { mediaFiles, filesID } = storeToRefs(projectStore);

const files = ref<UploadedFile[]>([]);
let mounted = true;

const fetchFiles = async () => {
  try {
    const storedFilesArray: UploadedFile[] = [];

    for (const fileId of filesID?.value || []) {
      const file = await getFile(fileId);
      if (file && mounted) {
        storedFilesArray.push({
          file: file,
          id: fileId,
        });
      }
    }

    if (mounted) {
      files.value = storedFilesArray;
    }
  } catch (error) {
    $message.error('Error fetching files');
    console.error('Error fetching files:', error);
  }
};

const onDeleteMedia = async (id: string) => {
  try {
    const onUpdateMedia = toRaw(mediaFiles.value).filter(
      (f) => f.fileId !== id,
    );
    projectStore.setMediaFiles(onUpdateMedia);
    projectStore.setFilesID(filesID?.value?.filter((f) => f !== id) || []);
    await deleteFile(id);
    $message.success('File deleted successfully');
  } catch (error) {
    $message.error('Error deleting file');
    console.error('Error deleting file:', error);
  }
};

// Watchers
watch(
  () => filesID?.value,
  () => {
    fetchFiles();
  },
  { immediate: true },
);

onMounted(() => {
  mounted = true;
  fetchFiles();
});

onUnmounted(() => {
  mounted = false;
});
</script>

<template>
  <div v-if="files.length > 0" class="space-y-3">
    <div
      v-for="mediaFile in files"
      :key="mediaFile.id"
      class="rounded-lg border border-gray-200 bg-white p-3 transition-shadow duration-200 hover:shadow-md"
    >
      <div class="flex items-center justify-between">
        <div class="flex min-w-0 flex-1 items-center space-x-3">
          <AddMedia :file-id="mediaFile.id" />
          <div class="min-w-0 flex-1">
            <span
              class="block truncate text-sm font-medium text-gray-900"
              :title="mediaFile.file.name"
            >
              {{ mediaFile.file.name }}
            </span>
            <span class="text-xs text-gray-500">
              {{ (mediaFile.file.size / 1024 / 1024).toFixed(2) }} MB
            </span>
          </div>
        </div>
        <button
          @click="onDeleteMedia(mediaFile.id)"
          class="rounded-md p-1 text-red-500 transition-colors duration-200 hover:bg-red-50 hover:text-red-700"
          :aria-label="`Delete ${mediaFile.file.name}`"
        >
          <svg
            class="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
  <div v-else class="py-8 text-center text-gray-500">
    <svg
      class="mx-auto mb-4 h-12 w-12 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h12V6H6zm3 3a1 1 0 112 0v6a1 1 0 11-2 0V9zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V9z"
      />
    </svg>
    <p class="text-sm">No media files uploaded yet</p>
    <p class="mt-1 text-xs text-gray-400">Upload files to get started</p>
  </div>
</template>

<style scoped>
.media-list {
  padding: 1rem;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
