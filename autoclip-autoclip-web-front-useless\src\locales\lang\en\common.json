{"okText": "OK", "closeText": "Close", "cancelText": "Cancel", "loadingText": "Loading...", "saveText": "Save", "delText": "Delete", "resetText": "Reset", "searchText": "Search", "queryText": "Search", "inputText": "Please input", "chooseText": "Please choose ", "redo": "Refresh", "back": "Back", "light": "Light", "dark": "Dark", "selectText": "Please select", "startTimeText": "Start time", "endTimeText": "End time", "login": "<PERSON><PERSON>", "required": "This is required", "loginOut": "Login out", "document": "Document", "profile": "User Center", "reminder": "Reminder", "loginOutMessage": "Exit the system?", "ok": "OK", "save": "Save", "cancel": "Cancel", "close": "Close", "reload": "Reload current", "success": "Success", "closeTab": "Close current", "closeTheLeftTab": "Close left", "closeTheRightTab": "Close right", "closeOther": "Close other", "closeAll": "Close all", "prevLabel": "Prev", "nextLabel": "Next", "skipLabel": "Jump", "doneLabel": "End", "menu": "<PERSON><PERSON>", "menuDes": "Menu bar rendered in routed structure", "collapse": "Collapse", "collapseDes": "Expand and zoom the menu bar", "tagsView": "Tags view", "tagsViewDes": "Used to record routing history", "tool": "Tool", "toolDes": "Used to set up custom systems", "query": "Query", "reset": "Reset", "shrink": "Put away", "expand": "Expand", "confirmTitle": "System Hint", "exportMessage": "Whether to confirm export data item?", "importMessage": "Whether to confirm import data item?", "createSuccess": "Create Success", "updateSuccess": "Update Success", "delMessage": "Delete the selected data?", "delDataMessage": "Delete the data?", "delNoData": "Please select the data to delete", "delSuccess": "Deleted successfully", "index": "Index", "status": "Status", "createTime": "Create Time", "updateTime": "Update Time", "copy": "Copy", "copySuccess": "Copy Success", "copyError": "<PERSON><PERSON>"}