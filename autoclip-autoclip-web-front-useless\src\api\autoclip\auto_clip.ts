import { AutoClipApi } from '@/api/enum/AutoClipApi';
import { FileCreateReqVO } from '@/api/infra/file';
import { defHttp } from '@/utils/http/axios';
import {
  BasicFetchPageResult,
  PageParam
} from '../model/BaseModel';

export enum SportType {
  PINGPONG = 0,
  BADMINTON = 1,
}

export enum MatchType {
  DOUBLES_MATCH = 0,
  SINGLES_MATCH = 1,
}

export enum ModeEnum {
  BACKEND_CLIP = 0,
  CUSTOM_CLIP = 1,
}

export const ModeDescriptionMapping = {
  [ModeEnum.BACKEND_CLIP]: '后台剪辑',
  [ModeEnum.CUSTOM_CLIP]: '自定义剪辑',
};

export const MatchTypeDescriptionMapping = {
  [MatchType.DOUBLES_MATCH]: '双打比赛',
  [MatchType.SINGLES_MATCH]: '单打比赛',
};

// 视频处理记录过滤参数
export interface VideoProcessRecordFilterParam extends PageParam {
  videoName?: string;
  status?: number;
  sportType?: number;
  createTimeStart?: string;
  createTimeEnd?: string;
  minVideoDuration?: number;
  maxVideoDuration?: number;
  minProgress?: number;
  maxProgress?: number;
}

// 视频处理进度过滤参数
export interface VideoProcessProgressFilterParam extends PageParam {
  videoName?: string;
  status?: number;
  sportType?: number;
  createTimeStart?: string;
  createTimeEnd?: string;
  minVideoDuration?: number;
  maxVideoDuration?: number;
  minProgress?: number;
  maxProgress?: number;
  onlyProcessing?: boolean;
}

// 视频列表过滤参数
export interface VideoListFilterParam extends PageParam {
  fileName?: string;
  videoProcessType?: number;
  sportType?: number;
  createTimeStart?: string;
  createTimeEnd?: string;
  expireTimeStart?: string;
  expireTimeEnd?: string;
  isExpired?: boolean;
  minDuration?: number;
  maxDuration?: number;
  minSize?: number;
  maxSize?: number;
  matchType?: number;
  mode?: number;
  greatBallEditing?: boolean;
  removeReplay?: boolean;
  getMatchSegments?: boolean;
}

export interface VideoClipConfigReqVo {
  mode: ModeEnum;
  matchType: MatchType;
  greatBallEditing: boolean;
  removeReplay: boolean;
  getMatchSegments: boolean;

  reserveTimeBeforeSingleRound: number;
  reserveTimeAfterSingleRound: number;
  minimumDurationSingleRound: number;
  minimumDurationGreatBall: number;
}

export interface PingPongVideoClipConfigReqVo extends VideoClipConfigReqVo {
  maxFireBallTime: number;
  mergeFireBallAndPlayBall: boolean;
}

export interface BadmintonVideoClipConfigReqVo extends VideoClipConfigReqVo {}

interface PingPongAutoClipParams {
  fileInfo: FileCreateReqVO;
  videoClipConfig: PingPongVideoClipConfigReqVo | null;
}

interface BadmintonAutoClipParams {
  fileInfo: FileCreateReqVO;
  videoClipConfig: BadmintonVideoClipConfigReqVo | null;
}

export enum VideoProcessType {
  RAW = 0,
  GREAT_MATCH = 1,
  ALL_MATCH_MERGED = 2,
}

// 定义视频信息接口
export interface VideoInfoRespVO {
  fileName: string;
  fileUrl: string;
  expireTime: string;
  fileType: string;
  createTime: string;
  thumbnailUrl?: string;
  videoProcessType: VideoProcessType;
  size: number;
  duration: number;
  config?: any;
}

export enum ProcessStatus {
  PREPARING = 0,
  PROCESSING = 1,
  COMPLETED = 2,
  FAILED = 3,
}

export interface VideoProcessProgressVO {
  // 原文件名
  name: string;
  // 文件 URL
  url: string;
  // 视频ID
  videoId: number;
  // 处理状态
  status: ProcessStatus;
  // 处理进度(0-100)
  progress: number;
  // 视频时长(秒)
  videoDuration: number;
  // 队列位置
  position: number;
  // 处理速度(秒/分钟)
  processSpeed: number;
  // 预计剩余时间(秒)
  estimatedRemainingTime: number;
  // 已处理时间(秒)
  processedTime: number;
}

export interface VideoProcessRecordVO {
  // 输入视频名称
  videoName: string;
  // 视频ID
  inputVideoId: number;
  // 输出视频ID
  outputVideoId: number;
  // 处理状态
  status: ProcessStatus;
  // 处理进度(0-100)
  progress: number;
  // 视频类型
  sportType: SportType;
  // 视频时长(秒)
  videoDuration: number;
  // 额外信息
  extraInfo: string;
  // 创建时间
  createTime: string;
  // 视频处理配置
  videoClipConfigReqVo: VideoClipConfigReqVo;
}

/**
 * @description: Upload interface
 */
export function auto_pingpong_clip_video(params: PingPongAutoClipParams) {
  return defHttp.post({
    data: params,
    url: AutoClipApi.PINGPONG_AUTO_CLIP,
  });
}

export function auto_badminton_clip_video(params: BadmintonAutoClipParams) {
  return defHttp.post({
    data: params,
    url: AutoClipApi.BADMINTON_AUTO_CLIP,
  });
}

// 请求视频列表
export const getVideoList = async (
  pageParams: VideoListFilterParam,
): Promise<BasicFetchPageResult<VideoInfoRespVO>> => {
  return await defHttp.get<BasicFetchPageResult<VideoInfoRespVO>>({
    url: AutoClipApi.VIDEO_INFO_LIST,
    params: pageParams,
  });
};

export const getVideoInfoRespVO = async (
  id: number,
): Promise<VideoInfoRespVO> => {
  return await defHttp.get<VideoInfoRespVO>({
    url: AutoClipApi.VIDEO_INFO.replace('{videoId}', id.toString()),
  });
};

// 获取视频处理记录列表
export const getVideoProcessRecords = async (
  pageParams: VideoProcessRecordFilterParam,
): Promise<BasicFetchPageResult<VideoProcessRecordVO>> => {
  return await defHttp.get<BasicFetchPageResult<VideoProcessRecordVO>>({
    url: AutoClipApi.RECORDS,
    params: pageParams,
  });
};

// 获取视频处理进度列表
export const getVideoProcessProgresses = async (
  filterParams?: VideoProcessProgressFilterParam,
): Promise<BasicFetchPageResult<VideoProcessProgressVO>> => {
  return await defHttp.get<BasicFetchPageResult<VideoProcessProgressVO>>({
    url: AutoClipApi.PROGRESSES,
    params: filterParams,
  });
};

// 获取单个视频处理进度
export const getVideoProcessProgress = async (
  id: number,
): Promise<VideoProcessProgressVO> => {
  return await defHttp.get<VideoProcessProgressVO>({
    url: AutoClipApi.PROGRESS,
    params: { id },
  });
};
