<template>
  <el-header
    class="border-3 top-0 z-20 w-full border-gray-100 border-opacity-50"
    style="--el-header-height: 4rem"
  >
    <div
      class="mx-auto flex h-full max-w-screen-xl items-center justify-between"
    >
      <AppLogo
        v-if="getShowHeaderLogo"
        :class="`${prefixCls}-logo mr-10`"
        :theme="getHeaderTheme"
        :style="getLogoWidth"
      />

      <Menu
        v-if="!getIsMobile"
        :elMenuConfig="{ mode: 'horizontal', collapse: false, ellipsis: false }"
        ref="menuRef"
        class="mr-auto"
      />

      <div class="flex items-center gap-4">
        <AppLocalePicker :show-text="false" />

        <div class="flex items-center gap-4" v-if="alreadyLogin">
          <Message />
          <UserInfo />
        </div>
        <el-button
          v-else
          size="default"
          class="!rounded-md !border !border-black/50 !px-3 !py-2 !text-sm !font-semibold transition-colors hover:!border-white/90"
          @click="setShowLoginModal(true)"
        >
          Login
        </el-button>
        <MenuTrigger
          v-if="getIsMobile"
          :active="!getMenuHidden"
          @trigger="setMenuSetting({ hidden: !getMenuHidden })"
        />
      </div>
    </div>
  </el-header>
</template>

<script lang="ts" setup>
import { useDesign } from '@/hooks/web/useDesign';
import { ElButton, ElHeader } from 'element-plus';
import { computed, ref, unref } from 'vue';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
import { useAppInject } from '@/hooks/web/useAppInject';
import { propTypes } from '@/utils/propTypes';

import { AppLogo } from '@/components/Application';

import { useUserStore } from '@/store/modules/user';
import UserInfo from '@/layouts/default/header/components/UserInfo.vue';
import AppLocalePicker from '@/components/Application/src/AppLocalePicker.vue';
import Menu from '../menu/src/Menu.vue';
import { Message } from '@/layouts/default/header/components/Message';
import MenuTrigger from '@/layouts/default/header/components/MenuTrigger.vue';
import { useRootSetting } from '@/hooks/setting/useRootSetting';

defineOptions({ name: 'LayoutHeader' });

defineProps({
  fixed: propTypes.bool,
});

const menuRef = ref();

const { prefixCls } = useDesign('layout-header');
const { getIsMixMode, getMenuWidth } = useMenuSetting();
const { setRootSetting } = useRootSetting();

const setShowLoginModal = (flag: boolean) => {
  setRootSetting({
    showLoginModal: flag,
  });
};
const { getHeaderTheme, getShowHeaderLogo } = useHeaderSetting();

const { getIsMobile } = useAppInject();
const { setMenuSetting, getMenuHidden } = useMenuSetting();

const getLogoWidth = computed(() => {
  if (!unref(getIsMixMode) || unref(getIsMobile)) {
    return {};
  }
  const width = unref(getMenuWidth) < 180 ? 180 : unref(getMenuWidth);
  return { width: `${width}px` };
});

const userStore = useUserStore();
const alreadyLogin = computed(() => {
  return userStore.getToken != null;
});
</script>

<style lang="scss" scoped></style>
