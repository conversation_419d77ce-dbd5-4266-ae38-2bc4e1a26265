<template>
  <div class="flex w-full flex-col gap-2">
    <div class="flex w-full flex-row items-center justify-between gap-12">
      <div class="flex flex-row items-center gap-2">
        <!-- Track Marker -->
        <el-button
          @click="toggleMarkerTrack"
          :class="{ 'is-active': enableMarkerTracking }"
          class="track-marker-btn"
        >
          <el-icon>
            <component :is="enableMarkerTracking ? 'Check' : 'Close'" />
          </el-icon>
          <span class="ml-2"
            >Track Marker <span class="text-xs">(T)</span></span
          >
        </el-button>

        <!-- Split -->
        <el-button @click="handleSplit" class="action-btn">
          <el-image
            class="h-5 w-5"
            :src="'https://www.svgrepo.com/show/509075/cut.svg'"
            alt="Split"
          />
          <span class="ml-2">Split <span class="text-xs">(S)</span></span>
        </el-button>

        <!-- Duplicate -->
        <el-button @click="handleDuplicate" class="action-btn">
          <el-image
            class="h-5 w-5"
            :src="'https://www.svgrepo.com/show/521623/duplicate.svg'"
            alt="Duplicate"
          />
          <span class="ml-2">Duplicate <span class="text-xs">(D)</span></span>
        </el-button>

        <!-- Delete -->
        <el-button @click="handleDelete" class="action-btn">
          <el-image
            class="h-5 w-5"
            :src="'https://www.svgrepo.com/show/511788/delete-1487.svg'"
            alt="Delete"
          />
          <span class="ml-2">Delete <span class="text-xs">(Del)</span></span>
        </el-button>

        <!-- Truncate -->
        <el-button @click="handleTruncate" class="action-btn">
          <div class="i-mdi-delete-circle"></div>
          <span class="ml-2">Truncate <span class="text-xs">(T)</span></span>
        </el-button>
      </div>

      <!-- Timeline Zoom -->
      <div class="mr-4 flex flex-row items-center justify-between gap-2">
        <label class="mt-1 block text-sm font-semibold text-gray-700"
          >Zoom</label
        >
        <span class="text-lg text-gray-700">-</span>
        <el-slider
          v-model="timelineZoomValue"
          :min="30"
          :max="120"
          :step="1"
          @input="throttledZoom"
          class="w-[100px]"
        />
        <span class="text-lg text-gray-700">+</span>
      </div>
    </div>

    <div
      ref="timelineRef"
      class="relative w-full overflow-x-auto rounded-lg border border-gray-300 bg-white shadow-sm"
      @click="handleTimelineClick"
    >
      <!-- Timeline Header -->
      <Header />

      <div class="bg-white" style="width: 100%" ref="timelineContainerRef">
        <!-- Timeline cursor -->
        <div
          class="absolute bottom-0 top-0 z-50 w-[2px] bg-blue-500"
          :style="{ left: `${currentTime * timelineZoom}px` }"
        />

        <!-- Timeline elements -->
        <div class="w-full">
          <div class="relative z-10 h-16 border-b border-gray-100">
            <VideoTimeline />
          </div>

          <div class="relative z-10 h-16 border-b border-gray-100">
            <AudioTimeline />
          </div>

          <div class="relative z-10 h-16 border-b border-gray-100">
            <ImageTimeline />
          </div>

          <div class="relative z-10 h-16">
            <TextTimeline />
          </div>
        </div>
      </div>
    </div>
    <Moveable
      v-if="timelineContainerRef"
      :target="timelineContainerRef"
      :container="null"
      :renderDirections="['w', 'e']"
      :edge="false"
      :origin="false"
      :hideChildMoveableDefaultLines="true"
      :draggable="true"
      :throttleDrag="0"
      :rotatable="false"
      :resizable="false"
      :throttleResize="0"
      :hideDefaultLines="true"
      @drag="onDrag($event)"
    />
  </div>
</template>

<script setup lang="ts">
import { useMessage } from '@/hooks/web/useMessage';
import { useProjectStore } from '@/store/editor';
import { MediaFile, TextElement } from '@/types/editor';
import { throttle } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
import Moveable from 'vue3-moveable';
import Header from './Header.vue';
import AudioTimeline from './elements-timeline/AudioTimeline.vue';
import ImageTimeline from './elements-timeline/ImageTimeline.vue';
import TextTimeline from './elements-timeline/TextTimeline.vue';
import VideoTimeline from './elements-timeline/VideoTimeline.vue';
import { buildUUID } from '@/utils/uuid';

const { $message } = useMessage();
const projectStore = useProjectStore();
const timelineRef = ref<HTMLDivElement | null>(null);
const timelineContainerRef = ref<HTMLDivElement | null>(null);
const timelineZoomValue = ref(100);

const {
  currentTime,
  timelineZoom,
  enableMarkerTracking,
  activeElement,
  activeElementIndex,
  mediaFiles,
  textElements,
  duration,
} = storeToRefs(projectStore);

// Throttled zoom handler
const throttledZoom = throttle((value: number) => {
  projectStore.setTimelineZoom(value);
}, 100);

// Event handlers
const toggleMarkerTrack = () => {
  projectStore.setMarkerTrack(!enableMarkerTracking.value);
};

const onDrag = (e: any) => {
  handleDrag(e.target, e.left);
};

const handleDrag = throttle((target: HTMLElement, left: number) => {
  const constrainedLeft = Math.max(left, 0);
  const newPositionStart = constrainedLeft / timelineZoom.value;
  projectStore.setCurrentTime(newPositionStart);

  target.style.left = `${constrainedLeft}px`;
}, 20);

const handleSplit = () => {
  let element: MediaFile | TextElement | null = null;
  let elements: (MediaFile | TextElement)[] = [];

  if (!activeElement.value) {
    $message.error('No element selected.');
    return;
  }

  if (activeElement.value === 'media') {
    elements = toRaw(mediaFiles.value);
    element = elements[activeElementIndex.value];

    if (!element) {
      $message.error('No element selected.');
      return;
    }

    const { positionStart, positionEnd } = element as MediaFile;

    if (
      currentTime.value <= positionStart ||
      currentTime.value >= positionEnd
    ) {
      $message.error('Marker is outside the selected element bounds.');
      return;
    }

    const positionDuration = positionEnd - positionStart;

    // Media logic (uses startTime/endTime for trimming)
    const { startTime, endTime } = element as MediaFile;
    const sourceDuration = endTime - startTime;
    const ratio = (currentTime.value - positionStart) / positionDuration;
    const splitSourceOffset = startTime + ratio * sourceDuration;

    const firstPart: MediaFile = {
      ...(element as MediaFile),
      id: buildUUID(),
      positionStart,
      positionEnd: currentTime.value,
      startTime,
      endTime: splitSourceOffset,
    };

    const secondPart: MediaFile = {
      ...(element as MediaFile),
      id: buildUUID(),
      positionStart: currentTime.value,
      positionEnd,
      startTime: splitSourceOffset,
      endTime,
    };

    elements.splice(activeElementIndex.value, 1, firstPart, secondPart);
  } else if (activeElement.value === 'text') {
    elements = toRaw(textElements.value);
    element = elements[activeElementIndex.value];

    if (!element) {
      $message.error('No element selected.');
      return;
    }

    const { positionStart, positionEnd } = element as TextElement;

    if (
      currentTime.value <= positionStart ||
      currentTime.value >= positionEnd
    ) {
      $message.error('Marker is outside the selected element.');
      return;
    }

    const firstPart: TextElement = {
      ...(element as TextElement),
      id: buildUUID(),
      positionStart,
      positionEnd: currentTime.value,
    };

    const secondPart: TextElement = {
      ...(element as TextElement),
      id: buildUUID(),
      positionStart: currentTime.value,
      positionEnd,
    };

    elements.splice(activeElementIndex.value, 1, firstPart, secondPart);
  }

  if (elements) {
    if (activeElement.value === 'media') {
      projectStore.setMediaFiles(elements as MediaFile[]);
    } else if (activeElement.value === 'text') {
      projectStore.setTextElements(elements as TextElement[]);
    }
    projectStore.setActiveElement(null);
    $message.success('Element split successfully.');
  }
};

const handleDuplicate = () => {
  let element: MediaFile | TextElement | null = null;
  let elements: (MediaFile | TextElement)[] = [];

  if (activeElement.value === 'media') {
    elements = toRaw(mediaFiles.value);
    element = elements[activeElementIndex.value];
  } else if (activeElement.value === 'text') {
    elements = toRaw(textElements.value);
    element = elements[activeElementIndex.value];
  }

  if (!element) {
    $message.error('No element selected.');
    return;
  }

  const duplicatedElement = {
    ...element,
    id: buildUUID(),
  };

  elements.splice(activeElementIndex.value + 1, 0, duplicatedElement as any);

  if (activeElement.value === 'media') {
    projectStore.setMediaFiles(elements as MediaFile[]);
  } else if (activeElement.value === 'text') {
    projectStore.setTextElements(elements as TextElement[]);
  }

  projectStore.setActiveElement(null);
  $message.success('Element duplicated successfully.');
};

const handleTruncate = () => {
  if (activeElement.value === 'media') {
    projectStore.setMediaFiles([]);
  } else if (activeElement.value === 'text') {
    projectStore.setTextElements([]);
  }

  projectStore.setActiveElement(null);
  projectStore.setActiveElementIndex(0);
  $message.success('Element truncated successfully.');
};

const handleDelete = () => {
  let element: MediaFile | TextElement | null = null;
  let elements: (MediaFile | TextElement)[] = [];

  if (activeElement.value === 'media') {
    elements = toRaw(mediaFiles.value);
    element = elements[activeElementIndex.value];
  } else if (activeElement.value === 'text') {
    elements = toRaw(textElements.value);
    element = elements[activeElementIndex.value];
  }

  if (!element) {
    $message.error('No element selected.');
    return;
  }

  const filteredElements = elements.filter((ele) => ele.id !== element!.id);

  if (activeElement.value === 'media') {
    projectStore.setMediaFiles(filteredElements as MediaFile[]);
  } else if (activeElement.value === 'text') {
    projectStore.setTextElements(filteredElements as TextElement[]);
  }

  projectStore.setActiveElement(null);
  $message.success('Element deleted successfully.');
};

const handleTimelineClick = (e: MouseEvent) => {
  if (!timelineRef.value) return;

  projectStore.setIsPlaying(false);
  const rect = timelineRef.value.getBoundingClientRect();
  const scrollOffset = timelineRef.value.scrollLeft;
  const offsetX = e.clientX - rect.left + scrollOffset;
  const seconds = offsetX / timelineZoom.value;
  const clampedTime = Math.max(0, Math.min(duration.value, seconds));

  projectStore.setCurrentTime(clampedTime);
};
</script>

<style scoped>
.track-marker-btn,
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 6px;
  transition: all 0.3s;
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  margin-top: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.track-marker-btn.is-active {
  background-color: #dbeafe;
  color: #2563eb;
  border-color: #2563eb;
}

.action-btn:hover,
.track-marker-btn:hover {
  background-color: #f9fafb;
  color: #1f2937;
  border-color: #9ca3af;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
</style>
