import {
  ElMessageBox,
  ElMessage,
  ElNotification,
  NotificationParams,
  MessageBoxData,
} from 'element-plus';
import { isString } from '@/utils/is';
import { VNode } from 'vue';
import { useI18n } from '@/hooks/web/useI18n';

export interface Notification {
  close(): void;
}

// 类型定义适配Element Plus
export interface NotifyApi {
  info(config: NotificationParams): Notification;
  success(config: NotificationParams): Notification;
  error(config: NotificationParams): Notification;
  warn(config: NotificationParams): Notification;
  warning(config: NotificationParams): Notification;
}

export declare type IconType = 'success' | 'info' | 'error' | 'warning';

// Element Plus的MessageBox选项
export interface ModalOptionsEx {
  title?: string;
  content: string | VNode;
  iconType?: IconType;
  confirmButtonText?: string;
  cancelButtonText?: string;
  showCancelButton?: boolean;
  callback?: (action: string) => void;
}

export type ModalOptionsPartial = Partial<ModalOptionsEx> &
  Pick<ModalOptionsEx, 'content'>;

// 处理内容渲染
function renderContent({ content }: Pick<ModalOptionsEx, 'content'>) {
  if (isString(content)) {
    return content;
  } else {
    // Element Plus原生支持VNode，无需额外处理
    return content;
  }
}

/**
 * @description: 创建确认框
 */
function createConfirm(options: ModalOptionsEx): Promise<MessageBoxData> {
  const iconType = options.iconType || 'warning';
  const message = renderContent(options);

  return ElMessageBox.confirm(message, options.title || '', {
    confirmButtonText: options.confirmButtonText || '确定',
    cancelButtonText: options.cancelButtonText || '取消',
    type: iconType,
    ...options,
  });
}

const getBaseOptions = () => {
  const { t } = useI18n();
  return {
    confirmButtonText: t('common.okText'),
    center: true,
  };
};

function createModalOptions(
  options: ModalOptionsPartial,
  type: IconType,
): ModalOptionsEx {
  return {
    ...getBaseOptions(),
    ...options,
    content: renderContent(options),
    iconType: type,
  };
}

function createSuccessModal(options: ModalOptionsPartial) {
  return ElMessageBox.alert(options.content, options.title || '', {
    ...createModalOptions(options, 'success'),
    type: 'success',
  });
}

function createErrorModal(options: ModalOptionsPartial) {
  return ElMessageBox.alert(options.content, options.title || '', {
    ...createModalOptions(options, 'error'),
    type: 'error',
  });
}

function createInfoModal(options: ModalOptionsPartial) {
  return ElMessageBox.alert(options.content, options.title || '', {
    ...createModalOptions(options, 'info'),
    type: 'info',
  });
}

function createWarningModal(options: ModalOptionsPartial) {
  return ElMessageBox.alert(options.content, options.title || '', {
    ...createModalOptions(options, 'warning'),
    type: 'warning',
  });
}

const notify = (param, type): Notification => {
  let default_config = {
    duration: 3000,
  };
  if (typeof param === 'string') {
    return ElNotification({ message: param, type: type, ...default_config });
  } else if (typeof param === 'object') {
    return ElNotification({ ...(param as object), type: type });
  } else {
    return ElNotification(param);
  }
};

/**
 * @description: 消息通消息通知知
 */
export function useMessage() {
  const notification: NotifyApi = {
    info: (config) => notify(config, 'info'),
    success: (config) => notify(config, 'success'),
    error: (config) => notify(config, 'error'),
    warn: (config) => notify(config, 'warning'),
    warning: (config) => notify(config, 'warning'),
  };
  return {
    $message: ElMessage,
    $notification: notification,
    createConfirm: createConfirm,
    createSuccessModal,
    createErrorModal,
    createInfoModal,
    createWarningModal,
  };
}
