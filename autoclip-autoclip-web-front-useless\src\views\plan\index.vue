<template>
  <div class="min-h-screen w-full bg-gray-50 p-8">
    <div class="mx-auto max-w-7xl">
      <div class="mb-12 text-center">
        <h1 class="text-5xl font-bold text-gray-700">
          <!-- Plans that grow with you -->
          和你一起成长的计划
        </h1>
        <p class="mt-6 text-gray-800">
          网站维护艰难，无论是服务器、计算、网络流量、存储以及人力都需要消耗大量的成本，
          支持我们，让网站可以继续运营下去。
        </p>
      </div>

      <!-- 加载状态 -->
      <!-- 订阅方案卡片 -->
      <div v-loading="loading" class="grid grid-cols-1 gap-8 md:grid-cols-3">
        <el-card
          v-for="plan in plans"
          :key="plan.id"
          class="relative rounded-2xl"
          :class="{
            'border-2 border-blue-500 shadow-xl': plan.recommended,
            'border-2 border-green-500 shadow-xl': isCurrentPlan(plan),
          }"
        >
          <div class="p-6">
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <h3 class="text-2xl font-semibold text-gray-800">
                  {{ plan.planName }}
                </h3>
                <div v-if="plan.recommended">
                  <span
                    class="rounded-full bg-blue-500 px-4 py-1 text-sm font-semibold text-white"
                  >
                    推荐
                  </span>
                </div>
                <div v-if="isCurrentPlan(plan)">
                  <span
                    class="rounded-full bg-green-500 px-4 py-1 text-sm font-semibold text-white"
                  >
                    Current Plan
                  </span>
                </div>
              </div>
              <p class="mt-2 text-gray-500">{{ plan.description }}</p>
            </div>

            <div class="mb-8">
              <span class="text-5xl font-bold text-gray-700">
                暂无
                <!-- ￥{{ plan.monthlyPrice }} -->
              </span>
              <span class="text-gray-500">/ {{ planType }} billed monthly</span>
            </div>

            <el-button
              :type="getButtonType(plan)"
              size="large"
              class="!h-12 w-full !text-lg !font-semibold"
              :disabled="isCurrentPlan(plan)"
            >
              {{ getButtonText(plan) }}
            </el-button>

            <div class="mt-10 border-t border-gray-200 pt-8">
              <ul class="space-y-3">
                <li
                  v-for="feature in plan.features"
                  :key="feature.id"
                  class="flex items-center"
                >
                  <Icon icon="i-mdi:check-circle" class="mr-3 text-blue-500" />
                  <span class="text-gray-600">{{ feature.name }}</span>
                </li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 用户当前订阅信息 -->
      <div
        v-if="userSubscription && !loading"
        class="mt-12 rounded-lg bg-white p-6 shadow-sm"
      >
        <h3 class="mb-4 text-xl font-semibold text-gray-800">
          Your Current Subscription
        </h3>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div>
            <p class="text-sm text-gray-500">Plan</p>
            <p class="text-lg font-semibold text-gray-800">
              {{ userSubscription.planName }}
            </p>
          </div>
          <div v-if="userSubscription.usedMinutes !== undefined">
            <p class="text-sm text-gray-500">Usage This Month</p>
            <p class="text-lg font-semibold text-gray-800">
              {{ userSubscription.usedMinutes }} /
              {{
                userSubscription.monthlyMinutes === -1
                  ? 'Unlimited'
                  : userSubscription.monthlyMinutes
              }}
              minutes
            </p>
          </div>
          <div v-if="userSubscription.endTime">
            <p class="text-sm text-gray-500">Expires</p>
            <p class="text-lg font-semibold text-gray-800">
              {{ formatDate(userSubscription.endTime) }}
            </p>
          </div>
        </div>
      </div>

      <p class="mt-8 text-center text-xs text-gray-500">
        Prices shown do not include applicable tax. *Usage limits apply.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  getSubscriptionPlans,
  getUserSubscriptionInfo,
  getSubscriptionPlansMock,
  SubscriptionPlanRespVO,
  UserSubscriptionRespVO,
  SubscriptionPlanEnum,
} from '@/api/pay/plan';
import { Icon } from '@/components/Icon';
import { useMessage } from '@/hooks/web/useMessage';

const { $notification } = useMessage();

const planType = ref('每月');
const plans = ref<SubscriptionPlanRespVO[]>([]);
const userSubscription = ref<UserSubscriptionRespVO | null>(null);
const loading = ref(false);

const getButtonType = (plan: SubscriptionPlanRespVO) => {
  if (isCurrentPlan(plan)) {
    return 'success';
  }
  if (plan.recommended) {
    return 'primary';
  }
  return 'warning';
};

const getButtonText = (plan: SubscriptionPlanRespVO) => {
  if (isCurrentPlan(plan)) {
    return 'Current Plan';
  }
  switch (plan.planType) {
    case SubscriptionPlanEnum.FREE:
      return 'Stay on Free plan';
    case SubscriptionPlanEnum.PRO:
      return 'Get Pro plan';
    case SubscriptionPlanEnum.MAX:
      return 'Get Max plan';
    default:
      return 'Select Plan';
  }
};

const isCurrentPlan = (plan: SubscriptionPlanRespVO) => {
  return !!(
    userSubscription.value && userSubscription.value.planType === plan.planType
  );
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const loadData = async () => {
  loading.value = true;
  try {
    // 并行获取订阅方案和用户订阅信息
    const [plansData, userSubscriptionData] = await Promise.all([
      getSubscriptionPlans().catch(() => getSubscriptionPlansMock()), // 如果API失败，使用mock数据
      getUserSubscriptionInfo().catch(() => null), // 如果获取用户信息失败，设为null
    ]);

    plans.value = plansData;
    userSubscription.value = userSubscriptionData;
  } catch (error) {
    console.error('Failed to load subscription data:', error);
    $notification.error('Failed to load subscription information');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});
</script>
