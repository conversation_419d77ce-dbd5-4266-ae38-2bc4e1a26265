<template>
  <div
    ref="containerRef"
    class="video-carousel-container relative aspect-video w-full select-none overflow-hidden"
    :style="{
      perspective: '1500px',
      perspectiveOrigin: 'center center',
    }"
  >
    <div class="video-carousel-wrapper relative h-full w-full">
      <div
        ref="trackRef"
        class="video-carousel-track relative flex aspect-video w-full"
        :style="{
          transformStyle: 'preserve-3d',
          transform: `translateX(${translateX}px)`,
        }"
        @mousedown="handleMouseDown"
        @touchstart="handleTouchStart"
      >
        <div
          v-for="(video, index) in videoList"
          :key="index"
          class="video-carousel-item absolute left-1/2 top-1/2 flex w-full max-w-[600px] -translate-x-1/2 -translate-y-1/2 items-center justify-center"
          :style="{
            transformOrigin: 'center center',
            transformStyle: 'preserve-3d',
            perspective: '1000px',
            ...getItemStyle(index),
          }"
          @click="onVideoClick(video, index)"
        >
          <div
            class="video-wrapper hover:shadow-3xl relative w-full overflow-hidden rounded-2xl shadow-2xl transition-all duration-300"
            :class="{ 'shadow-4xl': index === currentIndex }"
            :style="{ transformStyle: 'preserve-3d' }"
          >
            <PauseAfterPlayVideo
              v-if="video.sources"
              class="carousel-video h-auto w-full rounded-2xl"
              :sources="video.sources"
              :autoplay="false"
              :controls="index === currentIndex"
              :muted="true"
              :loop="false"
              :preload="'metadata'"
              :responsive="true"
              :fluid="true"
              :playbackRates="[0.5, 1, 1.25, 1.5, 2]"
              :language="'zh-CN'"
              :url="video.sources[0]?.src"
              :useVideoPlayer="true"
            />
            <div
              class="video-overlay absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-8 pb-6 text-white"
            >
              <h3 class="video-title mb-2 text-xl font-semibold">
                {{ video.title }}
              </h3>
              <p class="video-description text-sm leading-relaxed opacity-90">
                {{ video.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <button
      v-if="showNavigation"
      class="nav-btn nav-btn-left absolute left-5 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 cursor-pointer items-center justify-center rounded-full border-none bg-white/90 text-2xl text-gray-700 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:bg-white hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-30 max-md:left-2.5 max-md:h-10 max-md:w-10 max-md:text-xl md:left-5 md:h-12 md:w-12 md:text-2xl"
      :disabled="currentIndex === 0 && !props.infinite"
      @click="slideTo(currentIndex - 1)"
    >
      ‹
    </button>

    <button
      v-if="showNavigation"
      class="nav-btn nav-btn-right absolute right-5 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 cursor-pointer items-center justify-center rounded-full border-none bg-white/90 text-2xl text-gray-700 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:bg-white hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-30 max-md:right-2.5 max-md:h-10 max-md:w-10 max-md:text-xl md:right-5 md:h-12 md:w-12 md:text-2xl"
      :disabled="currentIndex === videoList.length - 1 && !props.infinite"
      @click="slideTo(currentIndex + 1)"
    >
      ›
    </button>

    <!-- 指示器 -->
    <div
      v-if="showIndicators"
      class="video-carousel-indicators absolute bottom-5 left-1/2 z-10 flex -translate-x-1/2 gap-2"
    >
      <button
        v-for="(_, index) in videoList"
        :key="index"
        class="indicator hover:scale-120 h-3 w-3 cursor-pointer rounded-full border-none bg-white/50 transition-all duration-300 hover:bg-white/80"
        :class="{
          'indicator--active scale-130 bg-white': index === currentIndex,
        }"
        @click="slideTo(index)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import PauseAfterPlayVideo from '@/components/PauseAfterPlayVideo/index.vue';
import { VideoSource } from '@/components/VideoPlayer/types';

interface VideoItem {
  sources: VideoSource[];
  title?: string;
  description?: string;
  poster?: string;
}

interface Props {
  videoList: VideoItem[];
  autoplay?: boolean;
  autoplayDelay?: number;
  infinite?: boolean;
  showNavigation?: boolean;
  showIndicators?: boolean;
  effect?: 'slide' | 'fade' | 'coverflow';
}

const props = withDefaults(defineProps<Props>(), {
  videoList: () => [],
  autoplay: false,
  autoplayDelay: 3000,
  infinite: true,
  showNavigation: true,
  showIndicators: true,
  effect: 'coverflow',
});

const emit = defineEmits<{
  change: [currentIndex: number, video: VideoItem];
  click: [video: VideoItem, index: number];
}>();

// 响应式状态
const currentIndex = ref(0);
const isTransitioning = ref(false);
const containerRef = ref<HTMLElement>();
const trackRef = ref<HTMLElement>();

// 拖拽相关
const isDragging = ref(false);
const startX = ref(0);
const currentX = ref(0);
const translateX = ref(0);
const dragTargetIndex = ref<number | null>(null);

// 自动播放
let autoplayTimer: number | null = null;

const getItemStyle = (index: number) => {
  if (props.effect === 'coverflow') {
    const offset = index - currentIndex.value;
    const absOffset = Math.abs(offset);

    let transform = 'translateX(-50%) translateY(-50%)';
    let opacity = 1;
    let zIndex = 0;

    // 动态z-index管理：处理拖拽和转换期间的层级
    const isCurrentItem = index === currentIndex.value;
    const isTargetItem =
      dragTargetIndex.value !== null && index === dragTargetIndex.value;
    const isInTransition = isTransitioning.value || isDragging.value;
    const isKeyItem = isCurrentItem || isTargetItem; // 关键项目（当前或目标）

    if (offset === 0) {
      // 当前项目 - 最高层级，在Z轴最前面
      const zOffset = isInTransition ? '400px' : '200px'; // 动画期间Z轴更前
      transform = `translateX(-50%) translateY(-50%) translateZ(${zOffset}) scale(1) rotateY(0deg)`;
      zIndex = isInTransition ? 300 : 100;
      opacity = 1;
    } else if (absOffset === 1) {
      // 相邻项目，Z轴稍微后退
      const rotateY = offset > 0 ? 45 : -45;
      const additionalX = offset > 0 ? 30 : -30;
      let zOffset = '-50px';

      if (isTargetItem && isInTransition) {
        // 目标项目在动画期间提升到较前位置，但仍然在当前项目后面
        zOffset = '150px';
        zIndex = 250;
      } else {
        zIndex = 50;
        // 动画期间非关键项目更后退
        if (isInTransition && !isKeyItem) {
          zOffset = '-200px';
        }
      }

      transform = `translateX(calc(-50% + ${additionalX}%)) translateY(-50%) translateZ(${zOffset}) scale(0.8) rotateY(${rotateY}deg)`;
      opacity = isInTransition && !isKeyItem ? 0.4 : 0.7; // 动画期间非关键项目更透明
    } else if (absOffset === 2) {
      // 第二层项目，Z轴更后退
      const rotateY = offset > 0 ? 70 : -70;
      const additionalX = offset > 0 ? 45 : -55;
      let zOffset = '-150px';

      if (isTargetItem && isInTransition) {
        // 目标项目在动画期间提升
        zOffset = '100px';
        zIndex = 200;
      } else {
        zIndex = 25;
        // 动画期间非关键项目更后退
        if (isInTransition && !isKeyItem) {
          zOffset = '-400px';
        }
      }

      transform = `translateX(calc(-50% + ${additionalX}%)) translateY(-50%) translateZ(${zOffset}) scale(0.6) rotateY(${rotateY}deg)`;
      opacity = isInTransition && !isKeyItem ? 0.2 : 0.5; // 动画期间非关键项目更透明
    } else {
      // 远距离项目 - 最低层级，Z轴最后面
      const rotateY = offset > 0 ? 90 : -90;
      const additionalX = offset > 0 ? 60 : -60;
      let zOffset = '-300px';

      if (isTargetItem && isInTransition) {
        // 目标项目在动画期间提升
        zOffset = '50px';
        zIndex = 150;
      } else {
        zIndex = 10;
        // 动画期间非关键项目更后退
        if (isInTransition && !isKeyItem) {
          zOffset = '-600px';
        }
      }

      transform = `translateX(calc(-50% + ${additionalX}%)) translateY(-50%) translateZ(${zOffset}) scale(0.5) rotateY(${rotateY}deg)`;
      opacity = isInTransition && !isKeyItem ? 0.1 : 0.3; // 动画期间非关键项目更透明
    }

    // 额外保障：在任何动画期间，确保当前项目的Z轴绝对最前
    if (isInTransition && isCurrentItem) {
      // 强制当前项目在最前面，距离拉开更大
      transform = transform.replace(/translateZ\([^)]+\)/, 'translateZ(800px)');
      zIndex = 1000;
    }

    return {
      transform,
      opacity,
      zIndex,
      transition: isTransitioning.value
        ? 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        : 'none',
    };
  }

  return {
    transform: 'translateX(-50%) translateY(-50%)',
    zIndex: 50,
    opacity: 1,
  };
};

// 滑动到指定索引
const slideTo = (index: number) => {
  if (isTransitioning.value) return;

  let targetIndex = index;

  if (props.infinite) {
    if (index < 0) {
      targetIndex = props.videoList.length - 1;
    } else if (index >= props.videoList.length) {
      targetIndex = 0;
    }
  } else {
    targetIndex = Math.max(0, Math.min(index, props.videoList.length - 1));
  }

  if (targetIndex === currentIndex.value) return;

  // 设置目标索引，用于动态z-index管理
  dragTargetIndex.value = targetIndex;
  isTransitioning.value = true;
  currentIndex.value = targetIndex;

  // 动画结束后重置状态
  setTimeout(() => {
    isTransitioning.value = false;
    dragTargetIndex.value = null; // 动画结束后清空目标索引
  }, 500);

  emit('change', targetIndex, props.videoList[targetIndex]);
};

// 鼠标事件处理
const handleMouseDown = (event: MouseEvent) => {
  if (!trackRef.value || isTransitioning.value) return;

  isDragging.value = true;
  startX.value = event.clientX;
  currentX.value = event.clientX;

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);

  event.preventDefault();
};

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return;

  currentX.value = event.clientX;
  const deltaX = currentX.value - startX.value;
  translateX.value = (deltaX / window.innerWidth) * 100;

  // 根据拖拽距离计算目标索引
  const threshold = 50;
  if (Math.abs(deltaX) > threshold) {
    let targetIndex = currentIndex.value;
    if (deltaX > 0) {
      targetIndex = currentIndex.value - 1;
    } else {
      targetIndex = currentIndex.value + 1;
    }

    // 处理边界情况
    if (props.infinite) {
      if (targetIndex < 0) {
        targetIndex = props.videoList.length - 1;
      } else if (targetIndex >= props.videoList.length) {
        targetIndex = 0;
      }
    } else {
      targetIndex = Math.max(
        0,
        Math.min(targetIndex, props.videoList.length - 1),
      );
    }

    dragTargetIndex.value = targetIndex;
  } else {
    dragTargetIndex.value = null;
  }
};

const handleMouseUp = () => {
  if (!isDragging.value) return;

  const deltaX = currentX.value - startX.value;
  const threshold = 50;

  if (Math.abs(deltaX) > threshold) {
    if (deltaX > 0) {
      slideTo(currentIndex.value - 1);
    } else {
      slideTo(currentIndex.value + 1);
    }
  } else {
    // 如果没有达到滑动阈值，清空目标索引并重置状态
    dragTargetIndex.value = null;
    isTransitioning.value = false;
  }

  isDragging.value = false;
  translateX.value = 0;

  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
};

// 触摸事件处理
const handleTouchStart = (event: TouchEvent) => {
  if (!trackRef.value || event.touches.length !== 1 || isTransitioning.value)
    return;

  isDragging.value = true;
  startX.value = event.touches[0].clientX;
  currentX.value = event.touches[0].clientX;

  document.addEventListener('touchmove', handleTouchMove, { passive: false });
  document.addEventListener('touchend', handleTouchEnd);
};

const handleTouchMove = (event: TouchEvent) => {
  if (!isDragging.value || event.touches.length !== 1) return;

  currentX.value = event.touches[0].clientX;
  const deltaX = currentX.value - startX.value;
  translateX.value = (deltaX / window.innerWidth) * 100;

  // 根据拖拽距离计算目标索引
  const threshold = 50;
  if (Math.abs(deltaX) > threshold) {
    let targetIndex = currentIndex.value;
    if (deltaX > 0) {
      targetIndex = currentIndex.value - 1;
    } else {
      targetIndex = currentIndex.value + 1;
    }

    // 处理边界情况
    if (props.infinite) {
      if (targetIndex < 0) {
        targetIndex = props.videoList.length - 1;
      } else if (targetIndex >= props.videoList.length) {
        targetIndex = 0;
      }
    } else {
      targetIndex = Math.max(
        0,
        Math.min(targetIndex, props.videoList.length - 1),
      );
    }

    dragTargetIndex.value = targetIndex;
  } else {
    dragTargetIndex.value = null;
  }

  event.preventDefault();
};

const handleTouchEnd = () => {
  if (!isDragging.value) return;

  const deltaX = currentX.value - startX.value;
  const threshold = 50;

  if (Math.abs(deltaX) > threshold) {
    if (deltaX > 0) {
      slideTo(currentIndex.value - 1);
    } else {
      slideTo(currentIndex.value + 1);
    }
  } else {
    // 如果没有达到滑动阈值，清空目标索引并重置状态
    dragTargetIndex.value = null;
    isTransitioning.value = false;
  }

  isDragging.value = false;
  translateX.value = 0;

  document.removeEventListener('touchmove', handleTouchMove);
  document.removeEventListener('touchend', handleTouchEnd);
};

// 视频点击事件
const onVideoClick = (video: VideoItem, index: number) => {
  if (isTransitioning.value) return;

  if (index !== currentIndex.value) {
    slideTo(index);
  }
  emit('click', video, index);
};

// 自动播放控制
const startAutoplay = () => {
  if (props.autoplay && !autoplayTimer) {
    autoplayTimer = window.setInterval(() => {
      if (!isTransitioning.value) {
        slideTo(currentIndex.value + 1);
      }
    }, props.autoplayDelay);
  }
};

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer);
    autoplayTimer = null;
  }
};

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  if (isTransitioning.value) return;

  switch (event.key) {
    case 'ArrowLeft':
      slideTo(currentIndex.value - 1);
      break;
    case 'ArrowRight':
      slideTo(currentIndex.value + 1);
      break;
  }
};

// 生命周期
onMounted(() => {
  startAutoplay();
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  stopAutoplay();
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.removeEventListener('touchmove', handleTouchMove);
  document.removeEventListener('touchend', handleTouchEnd);

  // 清理状态
  dragTargetIndex.value = null;
  isDragging.value = false;
  isTransitioning.value = false;
});

// 监听鼠标进入离开容器
watchEffect(() => {
  const container = containerRef.value;
  if (!container) return;

  const handleMouseEnter = () => stopAutoplay();
  const handleMouseLeave = () => startAutoplay();

  container.addEventListener('mouseenter', handleMouseEnter);
  container.addEventListener('mouseleave', handleMouseLeave);

  return () => {
    container.removeEventListener('mouseenter', handleMouseEnter);
    container.removeEventListener('mouseleave', handleMouseLeave);
  };
});

// 暴露方法
defineExpose({
  slideTo,
  next: () => {
    if (!isTransitioning.value) {
      slideTo(currentIndex.value + 1);
    }
  },
  prev: () => {
    if (!isTransitioning.value) {
      slideTo(currentIndex.value - 1);
    }
  },
  getCurrentIndex: () => currentIndex.value,
});
</script>

<style scoped>
/* 只保留Tailwind无法直接实现的自定义样式 */
.video-carousel-container {
  perspective: 1500px;
  perspective-origin: center center;
}

.video-carousel-track {
  transform-style: preserve-3d;
}

.video-carousel-item {
  transform-origin: center center;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.video-wrapper {
  transform-style: preserve-3d;
}

/* 自定义阴影 */
.shadow-3xl {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
}

.shadow-4xl {
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.4);
}

/* 自定义缩放 */
.scale-120 {
  transform: scale(1.2);
}

.scale-130 {
  transform: scale(1.3);
}

/* 梯度背景 */
.bg-gradient-to-t {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}
</style>
