import { store } from '@/store';
import { ProjectState } from '@/types/editor';
import { defineStore } from 'pinia';

interface ProjectsState {
  projects: ProjectState[];
  currentProjectId: string | null;
}

export const useProjectsStore = defineStore('projects', {
  state: (): ProjectsState => ({
    projects: [],
    currentProjectId: null,
  }),

  getters: {
    getProjects(): ProjectState[] {
      return this.projects;
    },

    getCurrentProjectId(): string | null {
      return this.currentProjectId;
    },
  },

  actions: {
    addProject(project: ProjectState) {
      this.projects.push(project);
      this.currentProjectId = project.id;
    },

    updateProject(project: ProjectState) {
      const index = this.projects.findIndex((p) => p.id === project.id);
      if (index !== -1) {
        this.projects[index] = project;
      }
    },

    deleteProject(projectId: string) {
      this.projects = this.projects.filter((p) => p.id !== projectId);
      if (this.currentProjectId === projectId) {
        this.currentProjectId =
          this.projects.length > 0 ? this.projects[0].id : null;
      }
    },

    setCurrentProject(projectId: string) {
      this.currentProjectId = projectId;
    },

    rehydrateProjects(projects: ProjectState[]) {
      this.projects = projects;
    },
  },
});

export function useProjectsStoreWithOut() {
  return useProjectsStore(store);
}
