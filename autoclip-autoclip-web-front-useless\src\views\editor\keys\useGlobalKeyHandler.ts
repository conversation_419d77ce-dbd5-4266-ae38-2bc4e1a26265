import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useProjectStore } from '@/store/editor';

export interface GlobalKeyHandlerOptions {
  handleDuplicate: () => void;
  handleSplit: () => void;
  handleDelete: () => void;
}

export function useGlobalKeyHandler(options: GlobalKeyHandlerOptions) {
  const { handleDuplicate, handleSplit, handleDelete } = options;

  const projectStore = useProjectStore();
  const duration = computed(() => projectStore.getDuration);
  const isPlaying = computed(() => projectStore.getIsPlaying);
  const isMuted = computed(() => projectStore.getIsMuted);
  const currentTime = computed(() => projectStore.getCurrentTime);
  const enableMarkerTracking = computed(
    () => projectStore.getEnableMarkerTracking,
  );

  // Store latest state values in refs
  const isPlayingRef = ref(isPlaying.value);
  const isMutedRef = ref(isMuted.value);
  const currentTimeRef = ref(currentTime.value);
  const enableMarkerTrackingRef = ref(enableMarkerTracking.value);

  // Watch for state changes and update refs
  watch(
    () => isPlaying.value,
    (newValue) => {
      isPlayingRef.value = newValue;
    },
  );

  watch(
    () => isMuted.value,
    (newValue) => {
      isMutedRef.value = newValue;
    },
  );

  watch(
    () => currentTime.value,
    (newValue) => {
      currentTimeRef.value = newValue;
    },
  );

  watch(
    () => enableMarkerTracking.value,
    (newValue) => {
      enableMarkerTrackingRef.value = newValue;
    },
  );

  const hasInteracted = ref(false);

  const handleClick = () => {
    hasInteracted.value = true;
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (!hasInteracted.value) return;

    const target = e.target as HTMLElement;
    const isTyping =
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.isContentEditable;

    if (isTyping) return;

    switch (e.code) {
      case 'Space':
        e.preventDefault();
        projectStore.setIsPlaying(!isPlayingRef.value);
        break;
      case 'KeyM':
        e.preventDefault();
        projectStore.setIsMuted(!isMutedRef.value);
        break;
      case 'KeyD':
        e.preventDefault();
        handleDuplicate();
        break;
      case 'KeyS':
        e.preventDefault();
        handleSplit();
        break;
      case 'Delete':
        e.preventDefault();
        handleDelete();
        break;
      case 'KeyT':
        e.preventDefault();
        projectStore.setMarkerTrack(!enableMarkerTrackingRef.value);
        break;
      case 'ArrowRight':
        e.preventDefault();
        if (isPlayingRef.value) return;
        const nextTime =
          currentTimeRef.value + 0.01 > duration.value
            ? 0
            : currentTimeRef.value + 0.01;
        projectStore.setCurrentTime(nextTime);
        break;
      case 'ArrowLeft':
        e.preventDefault();
        if (isPlayingRef.value) return;
        const prevTime =
          currentTimeRef.value - 0.01 > duration.value
            ? 0
            : currentTimeRef.value - 0.01;
        projectStore.setCurrentTime(prevTime);
        break;
      default:
        break;
    }
  };

  const setupKeyHandlers = () => {
    // Add click listener to detect user interaction
    window.addEventListener('click', handleClick, { once: true });

    // Add keydown listener
    window.addEventListener('keydown', handleKeyDown);
  };

  const cleanupKeyHandlers = () => {
    // Clean up event listeners
    window.removeEventListener('click', handleClick);
    window.removeEventListener('keydown', handleKeyDown);
  };

  onMounted(() => {
    setupKeyHandlers();
  });

  onUnmounted(() => {
    cleanupKeyHandlers();
  });

  return {
    hasInteracted,
    setupKeyHandlers,
    cleanupKeyHandlers,
  };
}
