<script lang="ts" setup>
import { useProjectStore } from '@/store/editor';
import { PlayerRef } from '@remotion/player';
import { storeToRefs } from 'pinia';
import * as React from 'react';
import { createRoot, type Root } from 'react-dom/client';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { PreviewPlayer } from './Player';

const reactRoot = ref();
let root: Root;

const { isPlaying } = storeToRefs(useProjectStore());

const onPaused = () => {
  isPlaying.value = false;
};
const onPlayed = () => {
  isPlaying.value = true;
};

const playerRef = ref<PlayerRef | null>();

const render = () => {
  if (!root) {
    root = createRoot(reactRoot.value);
  } else {
    root.unmount();
    root = createRoot(reactRoot.value);
  }
  root.render(
    React.createElement(PreviewPlayer, {
      onPaused: onPaused,
      onPlayed: onPlayed,
      playerRefInstance: playerRef,
    }),
  );
};

watchEffect(
  () => {
    render();
  },
  {
    flush: 'post',
  },
);

onBeforeUnmount(() => {
  if (root) {
    root.unmount();
  }
});
onMounted(() => {
  render();
});
</script>
<template>
  <div ref="reactRoot" class="h-full w-full"></div>
</template>
