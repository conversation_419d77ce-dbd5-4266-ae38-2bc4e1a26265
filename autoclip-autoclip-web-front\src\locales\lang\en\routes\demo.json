{"charts": {"baiduMap": "Baidu map", "aMap": "A map", "googleMap": "Google map", "charts": "Chart", "map": "Map", "line": "Line", "pie": "Pie"}, "comp": {"comp": "Component", "basic": "Basic", "transition": "Animation", "countTo": "Count To", "scroll": "<PERSON><PERSON>", "scrollBasic": "Basic", "scrollAction": "Scroll Function", "virtualScroll": "Virtual Scroll", "tree": "Tree", "treeBasic": "Basic", "editTree": "Searchable/toolbar", "actionTree": "Function operation", "modal": "Modal", "drawer": "Drawer", "desc": "Desc", "verify": "Verify", "verifyDrag": "Drag ", "verifyRotate": "Picture Restore", "qrcode": "QR code", "strength": "Password strength", "upload": "Upload", "loading": "Loading", "time": "Relative Time", "cropperImage": "Cropper Image", "cardList": "Card List"}, "editor": {"editor": "Editor", "codeEditor": "Code editor", "markdown": "Markdown editor", "tinymce": "Rich text", "tinymceBasic": "Basic", "tinymceForm": "embedded form"}, "excel": {"excel": "Excel", "customExport": "Select export format", "jsonExport": "JSON data export", "arrayExport": "Array data export", "importExcel": "Import"}, "feat": {"feat": "Page Function", "icon": "Icon", "screenShot": "Screen Shot", "tabs": "Tabs", "tabDetail": "<PERSON><PERSON>", "sessionTimeout": "Session Timeout", "print": "Print", "contextMenu": "Context Menu", "download": "Download", "clickOutSide": "ClickOutSide", "imgPreview": "Picture Preview", "copy": "Clipboard", "ellipsis": "EllipsisText", "msg": "Message prompt", "watermark": "Watermark", "ripple": "<PERSON><PERSON><PERSON>", "fullScreen": "Full Screen", "errorLog": "<PERSON><PERSON><PERSON>", "tab": "Tab with parameters", "tab1": "Tab with parameter 1", "tab2": "Tab with parameter 2", "menu": "Menu with parameters", "menu1": "Menu with parameters 1", "menu2": "Menu with parameters 2", "ws": "Websocket test", "breadcrumb": "Breadcrumbs", "breadcrumbFlat": "Flat Mode", "breadcrumbFlatDetail": "Flat mode details", "requestDemo": "Retry request demo", "breadcrumbChildren": "Level mode", "breadcrumbChildrenDetail": "Level mode detail"}, "flow": {"name": "Graphics editor", "flowChart": "<PERSON><PERSON><PERSON>"}, "form": {"form": "Form", "basic": "Basic", "useForm": "useForm", "refForm": "RefForm", "advancedForm": "Shrinkable", "ruleForm": "Form validation", "dynamicForm": "Dynamic", "customerForm": "Custom", "appendForm": "Append", "tabsForm": "TabsForm"}, "iframe": {"frame": "External", "antv": "ant<PERSON>ue doc (embedded)", "doc": "Project doc (embedded)", "docExternal": "Project doc (external)"}, "level": {"level": "MultiMenu"}, "page": {"page": "Page", "form": "Form", "formBasic": "Basic Form", "formStep": "Step Form", "formHigh": "Advanced Form", "desc": "Details", "descBasic": "Basic Details", "descHigh": "Advanced Details", "result": "Result", "resultSuccess": "Success", "resultFail": "Failed", "account": "Personal", "accountCenter": "Personal Center", "accountSetting": "Personal Settings", "exception": "Exception", "netWorkError": "Network Error", "notData": "No data", "list": "List page", "listCard": "Card list", "basic": "Basic list", "listBasic": "Basic list", "listSearch": "Search list"}, "permission": {"permission": "Permission", "front": "front-end", "frontPage": "Page", "frontBtn": "<PERSON><PERSON>", "frontTestA": "Test page A", "frontTestB": "Test page B", "back": "background", "backPage": "Page", "backBtn": "<PERSON><PERSON>"}, "steps": {"page": "Intro page"}, "system": {"moduleName": "System management", "account": "Account management", "vxeTableAccount": "Account management(VxeTable)", "account_detail": "Account detail", "password": "Change password", "dept": "Department management", "menu": "Menu management", "role": "Role management"}, "table": {"table": "Table", "basic": "Basic", "treeTable": "Tree", "fetchTable": "Remote loading", "fixedColumn": "Fixed column", "customerCell": "Custom column", "formTable": "Open search", "useTable": "UseTable", "refTable": "RefTable", "multipleHeader": "MultiLevel header", "mergeHeader": "Merge cells", "expandTable": "Expandable table", "fixedHeight": "Fixed height", "footerTable": "Footer", "editCellTable": "Editable cell", "editRowTable": "Editable row", "authColumn": "Auth column", "resizeParentHeightTable": "resizeParentHeightTable", "vxeTable": "VxeTable"}}