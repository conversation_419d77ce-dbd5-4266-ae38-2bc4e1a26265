<script setup lang="ts">
import { useGlobalKeyHandler } from './useGlobalKeyHandler';

interface GlobalKeyHandlerProps {
  handleDuplicate: () => void;
  handleSplit: () => void;
  handleDelete: () => void;
}

const props = defineProps<GlobalKeyHandlerProps>();

// Use the composable function
useGlobalKeyHandler({
  handleDuplicate: props.handleDuplicate,
  handleSplit: props.handleSplit,
  handleDelete: props.handleDelete,
});
</script>

<template>
  <!-- This component doesn't render anything -->
</template>
