<template>
  <InternalVideoPlayer ref="videoPlayerRef" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { defineExpose } from 'vue';
import InternalVideoPlayer from '@/components/VideoPlayer/InternalVideoPlayer.vue';

defineOptions({
  name: 'VideoPlayer',
});

const videoPlayerRef = ref<InstanceType<typeof InternalVideoPlayer>>();

defineExpose({
  instance: videoPlayerRef,
});
</script>

<style lang="scss" scoped>
:deep(.vjs-control-bar) {
  background-color: #ffffff;
  opacity: 0.9;
}
:deep(.vjs-play-progress) {
  background-color: #000000;
  opacity: 0.9;
}
</style>
