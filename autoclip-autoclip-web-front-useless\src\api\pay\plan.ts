import { defHttp } from '@/utils/http/axios';

export enum SubscriptionPlanEnum {
  FREE = 0,
  PRO = 1,
  MAX = 2,
}

export interface PermissionFeatureRespVO {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
  sort: number;
  status: number;
}

export interface SubscriptionPlanRespVO {
  id: number;
  planType: SubscriptionPlanEnum;
  planCode: string;
  planName: string;
  monthlyPrice: number;
  description: string;
  sort: number;
  status: number;
  features: PermissionFeatureRespVO[];
  recommended: boolean;
  popular: boolean;
}

export interface UserSubscriptionRespVO {
  id: number;
  userId: number;
  planType: SubscriptionPlanEnum;
  startTime?: string;
  endTime?: string;
  usedMinutes?: number;
  resetTime?: string;
  status: number;
  planName: string;
  monthlyPrice: number;
  monthlyMinutes: number;
  videoSaveDays: number;
  featureIds: number[];
  features: PermissionFeatureRespVO[];
  customClipEnabled: boolean;
  greatBallEditingEnabled: boolean;
  removeReplayEnabled: boolean;
  getMatchSegmentsEnabled: boolean;
  maxFireBallTimeEnabled: boolean;
  reserveTimeBeforeEnabled: boolean;
  reserveTimeAfterEnabled: boolean;
  minimumDurationSingleRoundEnabled: boolean;
  minimumDurationGreatBallEnabled: boolean;
  priorityProcessing: boolean;
}

// 获取用户订阅信息
export const getUserSubscriptionInfo = () => {
  return defHttp.request<UserSubscriptionRespVO>({
    url: '/autoclip/subscription/info',
    method: 'GET',
  });
};

// 获取所有订阅方案
export const getSubscriptionPlans = () => {
  return defHttp.request<SubscriptionPlanRespVO[]>({
    url: '/autoclip/subscription/plans',
    method: 'GET',
  });
};

// 备用mock数据，当API不可用时使用
const mockPlans: SubscriptionPlanRespVO[] = [
  {
    id: 1,
    planType: SubscriptionPlanEnum.FREE,
    planCode: 'FREE',
    planName: 'Free',
    monthlyPrice: 0,
    description: 'For everyday productivity',
    sort: 1,
    status: 1,
    recommended: false,
    popular: false,
    features: [
      {
        id: 1,
        name: 'Chat on web, iOS, and Android',
        code: 'feat_chat',
        description: '',
        category: 'General',
        sort: 1,
        status: 1,
      },
      {
        id: 2,
        name: 'Generate code and visualize data',
        code: 'feat_code',
        description: '',
        category: 'General',
        sort: 2,
        status: 1,
      },
      {
        id: 3,
        name: 'Write, edit, and create content',
        code: 'feat_content',
        description: '',
        category: 'General',
        sort: 3,
        status: 1,
      },
    ],
  },
  {
    id: 2,
    planType: SubscriptionPlanEnum.PRO,
    planCode: 'PRO',
    planName: 'Pro',
    monthlyPrice: 20,
    description: 'For everyday productivity',
    sort: 2,
    status: 1,
    recommended: true,
    popular: true,
    features: [
      {
        id: 4,
        name: 'More usage',
        code: 'feat_more_usage',
        description: '',
        category: 'Advanced',
        sort: 1,
        status: 1,
      },
      {
        id: 5,
        name: 'Access to unlimited Projects',
        code: 'feat_projects',
        description: '',
        category: 'Advanced',
        sort: 2,
        status: 1,
      },
      {
        id: 6,
        name: 'Connect Google Workspace',
        code: 'feat_google',
        description: '',
        category: 'Advanced',
        sort: 3,
        status: 1,
      },
    ],
  },
  {
    id: 3,
    planType: SubscriptionPlanEnum.MAX,
    planCode: 'MAX',
    planName: 'Max',
    monthlyPrice: 100,
    description: 'For everyday productivity',
    sort: 3,
    status: 1,
    recommended: false,
    popular: false,
    features: [
      {
        id: 7,
        name: 'Choose 5x or 20x more usage than Pro*',
        code: 'feat_pro_usage',
        description: '',
        category: 'Premium',
        sort: 1,
        status: 1,
      },
      {
        id: 8,
        name: 'Higher output limits for all tasks',
        code: 'feat_limits',
        description: '',
        category: 'Premium',
        sort: 2,
        status: 1,
      },
      {
        id: 9,
        name: 'Early access to advanced features',
        code: 'feat_early_access',
        description: '',
        category: 'Premium',
        sort: 3,
        status: 1,
      },
      {
        id: 10,
        name: 'Priority access at high traffic times',
        code: 'feat_priority',
        description: '',
        category: 'Premium',
        sort: 4,
        status: 1,
      },
    ],
  },
];

// 获取订阅方案的备用方法（使用mock数据）
export const getSubscriptionPlansMock = () => {
  return new Promise<SubscriptionPlanRespVO[]>((resolve) => {
    setTimeout(() => {
      resolve(mockPlans);
    }, 500);
  });
};
